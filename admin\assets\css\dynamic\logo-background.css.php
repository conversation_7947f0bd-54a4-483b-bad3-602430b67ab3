<?php
/**
 * Dynamic CSS for logo background color
 * This file generates CSS to ensure the logo background color matches the theme color
 */

// Set content type to CSS
header("Content-type: text/css");

// Include database connection
$db_path = __DIR__ . '/../config.php';
require_once $db_path;

// Get theme colors from database
$theme_query = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'appearance' AND setting_key IN ('theme_color', 'secondary_color')";
$theme_result = $conn->query($theme_query);

$theme_color = "#3c3c45";  // Default theme color
$secondary_color = "#2c3e50";  // Default secondary

if ($theme_result && $theme_result->num_rows > 0) {
    while ($row = $theme_result->fetch_assoc()) {
        if ($row['setting_key'] === 'theme_color') {
            $theme_color = $row['setting_value'];
        }
        if ($row['setting_key'] === 'secondary_color') {
            $secondary_color = $row['setting_value'];
        }
    }
}

// Use theme color if available, otherwise fall back to secondary color
$logo_bg_color = !empty($theme_color) ? $theme_color : $secondary_color;
?>

/* Logo background styling */
.sidebar-logo {
    background-color: <?php echo $logo_bg_color; ?>;
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Logo image styling */
.sidebar-logo img {
    max-width: 80%;
    height: auto;
    max-height: 50px;
}
