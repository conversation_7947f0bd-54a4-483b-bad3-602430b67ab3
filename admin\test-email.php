<?php
/**
 * Email Configuration Test Script
 *
 * This script tests the email configuration and PHPMailer setup
 */

session_start();
require_once 'config.php';
require_once 'lib/Mailer.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Set content type to HTML
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Email Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Email Configuration Test</h1>";

// Test 1: Check if Mailer class can be instantiated
echo "<div class='section'>
    <h2>Test 1: Mailer Class Instantiation</h2>";

try {
    $mailer = new Mailer($conn);
    echo "<div class='success'>✓ Mailer class instantiated successfully</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Failed to instantiate Mailer class: " . $e->getMessage() . "</div>";
    exit;
}

echo "</div>";

// Test 2: Check email settings
echo "<div class='section'>
    <h2>Test 2: Email Settings</h2>";

$settings = $mailer->getSettings();
echo "<pre>";
echo "Current Email Settings:\n";
echo "- Use SMTP: " . ($settings['use_smtp'] ? 'Yes' : 'No') . "\n";
echo "- SMTP Host: " . ($settings['smtp_host'] ?: 'Not set') . "\n";
echo "- SMTP Port: " . ($settings['smtp_port'] ?: 'Not set') . "\n";
echo "- SMTP Username: " . ($settings['smtp_username'] ?: 'Not set') . "\n";
echo "- SMTP Password: " . (empty($settings['smtp_password']) ? 'Not set' : '***hidden***') . "\n";
echo "- SMTP Encryption: " . ($settings['smtp_encryption'] ?: 'Not set') . "\n";
echo "- From Email: " . ($settings['from_email'] ?: 'Not set') . "\n";
echo "- From Name: " . ($settings['from_name'] ?: 'Not set') . "\n";
echo "- Reply To: " . ($settings['reply_to'] ?: 'Not set') . "\n";
echo "</pre>";

// Check if essential settings are configured
$essential_missing = [];
if (empty($settings['from_email'])) $essential_missing[] = 'from_email';
if (empty($settings['from_name'])) $essential_missing[] = 'from_name';

if (!empty($essential_missing)) {
    echo "<div class='error'>✗ Missing essential settings: " . implode(', ', $essential_missing) . "</div>";
} else {
    echo "<div class='success'>✓ Essential email settings are configured</div>";
}

echo "</div>";

// Test 3: Check PHPMailer library
echo "<div class='section'>
    <h2>Test 3: PHPMailer Library</h2>";

$phpmailer_paths = [
    __DIR__ . '/lib/PHPMailer/PHPMailer.php',
    __DIR__ . '/lib/PHPMailer/real/PHPMailer-6.8.1/src/PHPMailer.php',
    $_SERVER['DOCUMENT_ROOT'] . '/vendor/phpmailer/phpmailer/src/PHPMailer.php'
];

$phpmailer_found = false;
foreach ($phpmailer_paths as $path) {
    if (file_exists($path)) {
        echo "<div class='success'>✓ PHPMailer found at: $path</div>";
        $phpmailer_found = true;
        break;
    }
}

if (!$phpmailer_found) {
    echo "<div class='error'>✗ PHPMailer library not found in any expected location</div>";
    echo "<div class='info'>Checked paths:<br>";
    foreach ($phpmailer_paths as $path) {
        echo "- $path<br>";
    }
    echo "</div>";
}

echo "</div>";

// Test 4: Test email sending (if form submitted)
if (isset($_POST['test_email'])) {
    echo "<div class='section'>
        <h2>Test 4: Email Sending Test</h2>";

    $test_email = $_POST['test_email'];
    $test_subject = "Email Configuration Test - " . date('Y-m-d H:i:s');
    $test_message = "
    <html>
    <body>
        <h2>Email Configuration Test</h2>
        <p>This is a test email to verify that your email configuration is working correctly.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>Sent at: " . date('Y-m-d H:i:s') . "</li>
            <li>From: " . $settings['from_name'] . " &lt;" . $settings['from_email'] . "&gt;</li>
            <li>Using SMTP: " . ($settings['use_smtp'] ? 'Yes' : 'No') . "</li>
        </ul>
        <p>If you received this email, your configuration is working correctly!</p>
    </body>
    </html>";

    echo "<div class='info'>Attempting to send test email to: $test_email</div>";

    $result = $mailer->sendEmail($test_email, $test_subject, $test_message);

    if ($result['success']) {
        echo "<div class='success'>✓ Test email sent successfully!</div>";
    } else {
        echo "<div class='error'>✗ Failed to send test email: " . $result['message'] . "</div>";
    }

    echo "</div>";
}

// Test form
echo "<div class='section'>
    <h2>Send Test Email</h2>
    <form method='post'>
        <label for='test_email'>Email Address:</label><br>
        <input type='email' id='test_email' name='test_email' required style='width: 300px; padding: 5px;'><br><br>
        <button type='submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>Send Test Email</button>
    </form>
</div>";

// Test 6: Contact Form Integration Test
echo "<div class='section'>
    <h2>Test 6: Contact Form Integration</h2>";

echo "<h3>Contact Form Endpoints:</h3>";
echo "<ul>";
echo "<li><strong>Main Contact Form:</strong> contact-us.html → process-contact.php</li>";
echo "<li><strong>Footer Contact Form:</strong> All pages → process-contact.php</li>";
echo "</ul>";

echo "<h3>Test Contact Form Submission:</h3>";
echo "<form method='post' action='../process-contact.php' style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9;'>";
echo "<input type='hidden' name='source' value='Admin Test Form'>";
echo "<label>Name:</label><br>";
echo "<input type='text' name='name' value='Test User' required style='width: 200px; padding: 5px; margin-bottom: 10px;'><br>";
echo "<label>Email:</label><br>";
echo "<input type='email' name='email' value='<EMAIL>' required style='width: 200px; padding: 5px; margin-bottom: 10px;'><br>";
echo "<label>Message:</label><br>";
echo "<textarea name='message' required style='width: 300px; height: 80px; padding: 5px; margin-bottom: 10px;'>This is a test message from the admin panel to verify contact form email functionality.</textarea><br>";
echo "<button type='submit' style='padding: 8px 16px; background: #28a745; color: white; border: none; cursor: pointer;'>Test Contact Form</button>";
echo "</form>";

echo "<div class='info'>📝 This will test the actual contact form processing with the same email configuration used by the website forms.</div>";

echo "</div>";

// Test 5: Check system_settings table for email settings
echo "<div class='section'>
    <h2>Test 5: Database Email Settings</h2>";

$email_keys = ['use_smtp', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_security', 'from_email', 'from_name', 'reply_to'];
$placeholders = str_repeat('?,', count($email_keys) - 1) . '?';
$sql = "SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ($placeholders)";
$stmt = $conn->prepare($sql);
$stmt->bind_param(str_repeat('s', count($email_keys)), ...$email_keys);
$stmt->execute();
$result = $stmt->get_result();

echo "<h3>Email Settings in system_settings table:</h3>";
echo "<pre>";
$found_settings = [];
while ($row = $result->fetch_assoc()) {
    $found_settings[$row['setting_key']] = $row['setting_value'];
    $display_value = ($row['setting_key'] === 'smtp_password' && !empty($row['setting_value'])) ? '***hidden***' : $row['setting_value'];
    echo $row['setting_key'] . ": " . $display_value . "\n";
}
echo "</pre>";

if (empty($found_settings)) {
    echo "<div class='error'>✗ No email settings found in system_settings table</div>";
    echo "<div class='info'>You may need to configure email settings in the admin panel.</div>";
} else {
    echo "<div class='success'>✓ Found " . count($found_settings) . " email settings in database</div>";
}

echo "</div>";

echo "<div class='section'>
    <h2>Recommendations</h2>";

if (!$settings['use_smtp']) {
    echo "<div class='info'>📝 SMTP is disabled. The system will use PHP's mail() function, which may not work reliably on all hosting providers.</div>";
}

if ($settings['use_smtp'] && (empty($settings['smtp_host']) || empty($settings['smtp_username']))) {
    echo "<div class='error'>📝 SMTP is enabled but missing required configuration. Please configure SMTP settings in the admin panel.</div>";
}

if (empty($settings['from_email']) || empty($settings['from_name'])) {
    echo "<div class='error'>📝 Missing essential email settings (from_email, from_name). Please configure these in the admin panel.</div>";
}

echo "<p><a href='settings.php?category=email'>→ Go to Email Settings</a></p>";
echo "</div>";

echo "</body></html>";
?>
