<?php
/**
 * Add Notification Page
 *
 * This page allows administrators to create and send notifications
 * to users in the system.
 */

session_start();
require_once 'config.php';
require_once 'lib/Notification.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: index.php');
    exit;
}

$notification = new Notification($conn);
$success_message = '';
$error_message = '';

// Get all users for the dropdown
$users_query = "SELECT id, username FROM users ORDER BY username";
$users_result = $conn->query($users_query);
$users = [];

if ($users_result && $users_result->num_rows > 0) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = isset($_POST['user_id']) && $_POST['user_id'] !== 'all' ? (int)$_POST['user_id'] : null;
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';
    $type = isset($_POST['type']) ? $_POST['type'] : 'info';
    $icon = isset($_POST['icon']) ? $_POST['icon'] : 'fas fa-bell';
    $link = isset($_POST['link']) && !empty($_POST['link']) ? $_POST['link'] : null;

    // Validate input
    if (empty($title)) {
        $error_message = 'Title is required';
    } elseif (empty($message)) {
        $error_message = 'Message is required';
    } else {
        // Add notification
        $notification_id = $notification->add($user_id, $title, $message, $type, $icon, $link);

        if ($notification_id) {
            $success_message = 'Notification added successfully';

            // Clear form data after successful submission
            $_POST = array();
        } else {
            $error_message = 'Failed to add notification';
        }
    }
}

// Page title and metadata
$page_title = "Add Notification";
$page_icon = "fas fa-bell-plus";
$page_subtitle = "Create and send notifications to users";
?>

<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
        <div class="admin-content-actions">
            <a href="all_notifications.php" class="admin-btn secondary">
                <i class="fas fa-list"></i> View All Notifications
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 class="admin-card-title">Create New Notification</h3>
                <p class="admin-card-subtitle">Fill out the form below to send a notification</p>
            </div>
            <div class="admin-card-body">
                <form method="post" action="add_notification.php" class="admin-form">
                    <div class="admin-form-row">
                        <div class="admin-form-group col-md-6">
                            <label for="user_id" class="required">Recipient</label>
                            <select name="user_id" id="user_id" class="form-control">
                                <option value="all">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo isset($_POST['user_id']) && $_POST['user_id'] == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['username']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="admin-form-hint">Select a specific user or "All Users" to send to everyone</div>
                        </div>

                        <div class="admin-form-group col-md-6">
                            <label for="type" class="required">Notification Type</label>
                            <select name="type" id="type" class="form-control">
                                <option value="info" <?php echo isset($_POST['type']) && $_POST['type'] == 'info' ? 'selected' : ''; ?>>Information</option>
                                <option value="success" <?php echo isset($_POST['type']) && $_POST['type'] == 'success' ? 'selected' : ''; ?>>Success</option>
                                <option value="warning" <?php echo isset($_POST['type']) && $_POST['type'] == 'warning' ? 'selected' : ''; ?>>Warning</option>
                                <option value="danger" <?php echo isset($_POST['type']) && $_POST['type'] == 'danger' ? 'selected' : ''; ?>>Danger</option>
                            </select>
                            <div class="admin-form-hint">Choose the type of notification to send</div>
                        </div>
                    </div>

                    <div class="admin-form-row">
                        <div class="admin-form-group col-md-12">
                            <label for="title" class="required">Title</label>
                            <input type="text" name="title" id="title" class="form-control" value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" required>
                            <div class="admin-form-hint">Enter a clear, concise title for the notification</div>
                        </div>
                    </div>

                    <div class="admin-form-row">
                        <div class="admin-form-group col-md-12">
                            <label for="message" class="required">Message</label>
                            <textarea name="message" id="message" class="form-control" rows="4" required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                            <div class="admin-form-hint">Enter the detailed message for the notification</div>
                        </div>
                    </div>

                    <div class="admin-form-row">
                        <div class="admin-form-group col-md-6">
                            <label for="icon">Icon</label>
                            <div class="icon-selector">
                                <div class="icon-preview">
                                    <i class="<?php echo isset($_POST['icon']) ? htmlspecialchars($_POST['icon']) : 'fas fa-bell'; ?>"></i>
                                </div>
                                <input type="text" name="icon" id="icon" class="form-control" value="<?php echo isset($_POST['icon']) ? htmlspecialchars($_POST['icon']) : 'fas fa-bell'; ?>">
                            </div>
                            <div class="admin-form-hint">Enter a FontAwesome icon class (e.g., fas fa-bell)</div>
                        </div>

                        <div class="admin-form-group col-md-6">
                            <label for="link">Link (Optional)</label>
                            <input type="text" name="link" id="link" class="form-control" placeholder="e.g., dashboard.php" value="<?php echo isset($_POST['link']) ? htmlspecialchars($_POST['link']) : ''; ?>">
                            <div class="admin-form-hint">URL to navigate to when notification is clicked</div>
                        </div>
                    </div>

                    <div class="admin-form-row">
                        <div class="admin-form-actions">
                            <button type="submit" class="admin-btn primary">
                                <i class="fas fa-paper-plane"></i> Send Notification
                            </button>
                            <button type="reset" class="admin-btn secondary">
                                <i class="fas fa-undo"></i> Reset Form
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icon preview
    initIconPreview();

    // Initialize type selector
    initTypeSelector();

    // Auto-dismiss alerts
    initAlertDismissal();
});

/**
 * Initialize icon preview functionality
 */
function initIconPreview() {
    const iconInput = document.getElementById('icon');
    const iconPreview = document.querySelector('.icon-preview i');

    if (iconInput && iconPreview) {
        // Update preview on input
        iconInput.addEventListener('input', function() {
            const iconClass = this.value.trim();
            iconPreview.className = iconClass;
        });

        // Show icon picker on click
        iconPreview.addEventListener('click', function() {
            showIconPicker();
        });
    }
}

/**
 * Initialize notification type selector
 */
function initTypeSelector() {
    const typeSelect = document.getElementById('type');
    const iconInput = document.getElementById('icon');
    const iconPreview = document.querySelector('.icon-preview i');

    if (typeSelect) {
        typeSelect.addEventListener('change', function() {
            const type = this.value;
            let iconClass = 'fas fa-bell';

            // Set appropriate icon based on notification type
            switch (type) {
                case 'info':
                    iconClass = 'fas fa-info-circle';
                    break;
                case 'success':
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'warning':
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
                case 'danger':
                    iconClass = 'fas fa-exclamation-circle';
                    break;
            }

            if (iconInput && iconPreview) {
                iconInput.value = iconClass;
                iconPreview.className = iconClass;
            }
        });
    }
}

/**
 * Show icon picker modal (placeholder for future implementation)
 */
function showIconPicker() {
    // This would be implemented with a modal showing common FontAwesome icons
    alert('Icon picker will be implemented in a future update. For now, please enter the icon class manually.');
}

/**
 * Initialize alert auto-dismissal (handled by global script)
 */
function initAlertDismissal() {
    // Alert auto-dismissal is now handled by the global alert-auto-dismiss.js script
    // This function is kept for backward compatibility but does nothing
}
</script>

<style>
/* Form layout */
.admin-form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px 20px;
}

.admin-form-group {
    padding: 0 10px;
    margin-bottom: 20px;
    box-sizing: border-box;
}

.col-md-6 {
    width: 50%;
}

.col-md-12 {
    width: 100%;
}

/* Form elements */
.admin-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.admin-form label.required:after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.admin-form-hint {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.admin-form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Icon selector */
.icon-selector {
    display: flex;
    align-items: center;
    gap: 15px;
}

.icon-preview {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #f1ca2f;
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-preview:hover {
    background-color: #f1ca2f;
    color: #fff;
    transform: scale(1.05);
}

/* Responsive styles */
@media (max-width: 768px) {
    .col-md-6 {
        width: 100%;
    }

    .admin-form-actions {
        flex-direction: column;
    }

    .admin-form-actions button {
        width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
