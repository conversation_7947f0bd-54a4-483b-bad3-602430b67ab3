<?php
/**
 * Simple Email Test
 * Quick test to verify the unified email system works
 */

// Include necessary files
require_once 'config.php';

// Check if user is logged in and is admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: index.php');
    exit;
}

require_once 'includes/email-functions.php';

// Simple test
echo "<h1>Email System Test</h1>";

// Test get_setting function
echo "<h2>Testing get_setting function:</h2>";
$from_email = get_setting('from_email', '<EMAIL>');
$from_name = get_setting('from_name', 'Default Name');
$use_smtp = get_setting('use_smtp', '0');

echo "From Email: " . htmlspecialchars($from_email) . "<br>";
echo "From Name: " . htmlspecialchars($from_name) . "<br>";
echo "Use SMTP: " . ($use_smtp === '1' ? 'Yes' : 'No') . "<br>";

// Test email settings function
echo "<h2>Testing get_email_settings function:</h2>";
$settings = get_email_settings();
echo "<pre>";
print_r($settings);
echo "</pre>";

// Test basic email sending (only if email is provided)
if (isset($_GET['test_email']) && !empty($_GET['test_email'])) {
    $test_email = $_GET['test_email'];

    if (filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        echo "<h2>Testing email sending to: " . htmlspecialchars($test_email) . "</h2>";

        $subject = "Test Email from Unified System";
        $message = "This is a test email from the unified email system. Current time: " . date('Y-m-d H:i:s');

        $result = send_email($test_email, $subject, $message);

        if ($result) {
            echo "<div style='color: green; font-weight: bold;'>✅ Email sent successfully!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ Email sending failed!</div>";
        }
    } else {
        echo "<div style='color: red;'>Invalid email address provided.</div>";
    }
}

echo "<h2>Test Email Sending:</h2>";
echo "<form method='get'>";
echo "<input type='email' name='test_email' placeholder='Enter email to test' required>";
echo "<button type='submit'>Send Test Email</button>";
echo "</form>";

echo "<p><a href='dashboard.php'>← Back to Dashboard</a></p>";
?>
