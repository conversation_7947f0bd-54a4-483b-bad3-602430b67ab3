<?php
/**
 * HTML Editor
 *
 * Allows editing HTML files with read-only header and footer sections
 */

// Start session
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';
require_once 'lib/FileVersions.php';
require_once 'lib/CollaborativeEditing.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view and edit HTML files
if (!$permissions->hasPermission('view_files') || !$permissions->hasPermission('edit_html_files')) {
    $_SESSION['error_message'] = "You do not have permission to access the HTML Editor.";
    redirect('dashboard.php');
}

// Initialize file versions and collaborative editing
$file_versions = new FileVersions($conn, $_SESSION['user_id']);
$collaborative = new CollaborativeEditing($conn, $_SESSION['user_id']);

// Set page title
$page_title = "HTML Editor";

// Add page-specific CSS and JS
$extra_css = '
<link rel="stylesheet" href="assets/css/pages/html-editor.css?v=' . time() . '">
<!-- CodeMirror CSS from CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/theme/material.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/foldgutter.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/show-hint.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/dialog/dialog.min.css">
<!-- Quill WYSIWYG Editor CSS -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
';

$extra_js = '
<!-- CodeMirror JS from CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/edit/matchbrackets.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/edit/closebrackets.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/edit/closetag.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/foldcode.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/foldgutter.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/brace-fold.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/xml-fold.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/indent-fold.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/fold/comment-fold.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/show-hint.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/html-hint.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/css-hint.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/javascript-hint.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/search/search.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/search/searchcursor.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/dialog/dialog.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/selection/active-line.min.js"></script>

<!-- HTML Beautifier for code formatting -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify-html.min.js"></script>
<!-- Quill WYSIWYG Editor JS -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<!-- Custom JS -->
<script src="js/html-editor-locks.js?v=' . time() . '"></script>
<script src="js/html-editor-versions.js?v=' . time() . '"></script>
<script src="js/html-editor.js?v=' . time() . '"></script>
';

// Define allowed directories and file extensions
$allowed_dirs = [
    '../' => 'Root'
];

// Only allow HTML files for editing
$allowed_extensions = [
    'html' => 'HTML Files'
];

// Define prohibited files (header and footer)
$prohibited_files = [
    '../header.html',
    '../footer.html',
    './header.html',
    './footer.html',
    'header.html',
    'footer.html'
];

// Function to check if a file is a header or footer file
function isHeaderOrFooterFile($filename) {
    $basename = strtolower(basename($filename));
    return $basename === 'header.html' || $basename === 'footer.html';
}

// Function to check if an HTML file contains dynamic content
function containsDynamicContent($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }

    $content = file_get_contents($filepath);

    // Check for PHP-style dynamic content markers
    if (strpos($content, '<?php') !== false ||
        strpos($content, '<?=') !== false ||
        strpos($content, '<%') !== false) {
        return true;
    }

    // Check for server-side includes
    if (strpos($content, '<!--#include') !== false) {
        return true;
    }

    // Check for template syntax
    if (strpos($content, '{{') !== false ||
        strpos($content, '}}') !== false ||
        strpos($content, '{%') !== false ||
        strpos($content, '%}') !== false ||
        strpos($content, '@if') !== false ||
        strpos($content, '@foreach') !== false) {
        return true;
    }

    return false;
}

// Initialize variables
$current_dir = isset($_GET['dir']) ? $_GET['dir'] : '../';
$current_file = isset($_GET['file']) ? $_GET['file'] : '';
$file_content = '';
$file_type = '';
$success_message = '';
$error_message = '';

// Handle release lock action
if (isset($_GET['action']) && $_GET['action'] === 'release_lock' && isset($_GET['file_path'])) {
    $file_path = $_GET['file_path'];

    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($file_path, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    if ($valid_path && file_exists($file_path)) {
        // Release the lock
        $result = $collaborative->releaseLock($file_path);
        if ($result['success']) {
            $success_message = "Lock released successfully.";
        } else {
            $error_message = "Failed to release lock: " . $result['message'];
        }
    } else {
        $error_message = "Invalid file path.";
    }

    // Redirect back to the editor with the same file
    if (!empty($file_path)) {
        $current_file = $file_path;
    }
}

// Validate current directory
if (!array_key_exists($current_dir, $allowed_dirs)) {
    $current_dir = '../';
}

// Handle file save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_file'])) {
    $file_path = $_POST['file_path'];

    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($file_path, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    // Check if file is prohibited (header or footer)
    if (in_array($file_path, $prohibited_files) || isHeaderOrFooterFile($file_path)) {
        $error_message = "This file is protected and cannot be edited.";
    }
    else if (!$valid_path) {
        $error_message = "Invalid file path.";
    } else {
        // Get file extension
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if file extension is allowed (only HTML)
        if ($file_extension !== 'html') {
            $error_message = "Only HTML files can be edited in this interface.";
        } else {
            // Check if file contains dynamic content
            if (containsDynamicContent($file_path)) {
                $error_message = "This file contains dynamic content and cannot be edited in this interface.";
            } else {
                // Handle file content update
                if (isset($_POST['file_content'])) {
                    // Check if user has write permission for this file
                    if (!$permissions->canWriteFile($file_path)) {
                        $error_message = "You do not have permission to edit this file.";
                    } else {
                        $new_content = $_POST['file_content'];
                        $comment = isset($_POST['version_comment']) ? $_POST['version_comment'] : '';

                        // Check if file is locked by another user
                        $lock = $collaborative->getLock($file_path);
                        if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                            $error_message = "This file is currently being edited by {$lock['username']}. Please try again later.";
                        } else {
                            // Write content to file
                            if (file_put_contents($file_path, $new_content) !== false) {
                                // Save version history
                                $file_versions->saveVersion($file_path, $new_content, $comment);

                                // Extend lock
                                if ($lock && $lock['user_id'] == $_SESSION['user_id']) {
                                    $collaborative->extendLock($file_path);
                                }

                                $success_message = "File saved successfully.";
                                $current_file = $file_path;
                                $file_content = $new_content;
                            } else {
                                $error_message = "Failed to save file. Check file permissions.";
                            }
                        }
                    }
                }
            }
        }
    }
}

// Get file content if a file is selected
if (!empty($current_file)) {
    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($current_file, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    // Check if file is prohibited (header or footer)
    if (in_array($current_file, $prohibited_files) || isHeaderOrFooterFile($current_file)) {
        $error_message = "This file is protected and cannot be edited.";
        $current_file = '';
    }
    // Check if file is not HTML
    else if (strtolower(pathinfo($current_file, PATHINFO_EXTENSION)) !== 'html') {
        $error_message = "Only HTML files can be edited in this interface.";
        $current_file = '';
    }
    // Check if file contains dynamic content
    else if (containsDynamicContent($current_file)) {
        $error_message = "This file contains dynamic content and cannot be edited in this interface.";
        $current_file = '';
    }
    else if ($valid_path && file_exists($current_file)) {
        // Check if user has permission to read this file
        if (!$permissions->canReadFile($current_file)) {
            $error_message = "You do not have permission to access this file.";
            $current_file = '';
        } else {
            // Get file content
            $file_content = file_get_contents($current_file);
            $file_type = strtolower(pathinfo($current_file, PATHINFO_EXTENSION));

            // Check if file is locked by another user
            $lock = $collaborative->getLock($current_file);
            if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                $lock_message = "This file is currently being edited by {$lock['username']}. You can view it, but you cannot save changes.";
                $is_locked_by_other = true;
            } else {
                // Acquire lock for editing
                if (!$lock && $permissions->canWriteFile($current_file)) {
                    $collaborative->acquireLock($current_file);
                }
                $is_locked_by_other = false;
            }

            // Get file versions
            if ($permissions->hasPermission('manage_file_versions')) {
                $file_versions_list = $file_versions->getAllVersions($current_file);
            }
        }
    } else {
        $error_message = "Invalid file or file does not exist.";
        $current_file = '';
    }
}

// Get files in current directory
$files = [];
if (is_dir($current_dir)) {
    $dir_contents = scandir($current_dir);
    foreach ($dir_contents as $item) {
        if ($item != '.' && $item != '..') {
            $full_path = $current_dir . $item;
            $is_dir = is_dir($full_path);
            $extension = $is_dir ? '' : strtolower(pathinfo($item, PATHINFO_EXTENSION));

            // Skip prohibited files (header and footer)
            if (in_array($full_path, $prohibited_files) || isHeaderOrFooterFile($full_path)) {
                continue;
            }

            // Skip files with dynamic content
            if (!$is_dir && $extension === 'html' && containsDynamicContent($full_path)) {
                continue;
            }

            // Only include directories and HTML files
            if ($is_dir || $extension === 'html') {
                $files[] = [
                    'name' => $item,
                    'path' => $full_path,
                    'is_dir' => $is_dir,
                    'extension' => $extension,
                    'size' => $is_dir ? '' : filesize($full_path),
                    'modified' => date("Y-m-d H:i:s", filemtime($full_path))
                ];
            }
        }
    }
}

// Sort files: directories first, then files
usort($files, function($a, $b) {
    if ($a['is_dir'] && !$b['is_dir']) {
        return -1;
    } elseif (!$a['is_dir'] && $b['is_dir']) {
        return 1;
    } else {
        return strcasecmp($a['name'], $b['name']);
    }
});

include 'includes/header.php';
?>

<div class="admin-container" data-is-admin="<?php echo isset($_SESSION['is_admin']) && $_SESSION['is_admin'] ? 'true' : 'false'; ?>" data-user-id="<?php echo $_SESSION['user_id']; ?>" data-username="<?php echo htmlspecialchars($_SESSION['username']); ?>">
    <?php
    // Set up variables for content header
    $page_icon = 'fas fa-code';
    $page_title = 'HTML Editor';
    $page_description = 'Edit HTML files with read-only header and footer sections';
    $stats_data = [];

    // Include the content header
    include 'includes/content-header.php';
    ?>

    <!-- File Selector Dropdown -->
    <div class="file-selector-container">
        <div class="file-selector-wrapper">
            <label for="fileSelector" class="file-selector-label">Select HTML File:</label>
            <select id="fileSelector" class="file-selector" onchange="if (this.value) window.location.href=this.value;">
                <option value="">-- Select a file to edit --</option>
                <?php foreach ($files as $file): ?>
                    <?php if (!$file['is_dir']): ?>
                    <option value="html_editor.php?file=<?php echo urlencode($file['path']); ?>" <?php echo ($current_file == $file['path']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($file['name']); ?>
                    </option>
                    <?php endif; ?>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="file-selector-info">
            <small><i class="fas fa-info-circle"></i> HTML files with dynamic content, header files, and footer files are excluded.</small>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible" id="successAlert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Success</div>
                <div class="alert-text"><?php echo $success_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <script>
            // Auto-hide success message after 5 seconds
            setTimeout(function() {
                const alert = document.getElementById('successAlert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                }
            }, 5000);
        </script>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible" id="errorAlert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Error</div>
                <div class="alert-text"><?php echo $error_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <?php if (!empty($lock_message)): ?>
        <div class="alert alert-warning alert-dismissible" id="lockAlert">
            <div class="alert-icon">
                <i class="fas fa-lock"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">File Locked</div>
                <div class="alert-text"><?php echo $lock_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <!-- Main Editor Layout -->
    <div class="editor-container">
        <!-- Editor Area -->
        <div class="editor-area">
            <?php if (!empty($current_file)): ?>
            <form method="post" action="html_editor.php" id="fileEditForm" class="editor-form">
            <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($current_file); ?>">

                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Saving...</span>
                    </div>
                </div>

                <div class="editor-header">
                    <h3 class="editor-title">
                        <i class="fas fa-file-code"></i>
                        <?php echo htmlspecialchars(basename($current_file)); ?>
                    </h3>
                    <div class="editor-actions">
                        <div id="lockStatus" class="lock-status not-locked">
                            <i class="fas fa-unlock"></i> File is not locked
                        </div>
                        <!-- Release lock button -->
                        <a href="?action=release_lock&file_path=<?php echo urlencode($current_file); ?>" id="unlockFileBtn" class="btn btn-success btn-sm" title="Release Lock" style="display: none;">
                            <i class="fas fa-unlock"></i> Release Lock
                        </a>
                        <?php if (isset($is_locked_by_other) && $is_locked_by_other && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1): ?>
                        <button type="button" id="forceUnlockBtn" class="btn btn-danger btn-sm" title="Force Unlock">
                            <i class="fas fa-unlock"></i> Force Unlock
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="editor-content" id="editor-content-container" style="min-height: 600px; height: auto !important; max-height: none !important;">
                    <div class="alert alert-info alert-subtle py-1">
                        <div class="alert-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-text small">Header and footer sections are read-only. You can only edit the content between them.</div>
                        </div>
                    </div>

                    <!-- Editor Toolbar -->
                    <div class="editor-toolbar">
                        <div class="editor-toolbar-group">
                            <button type="button" id="viewCodeBtn" class="btn btn-sm btn-primary active" title="Code View">
                                <i class="fas fa-code"></i> Code
                            </button>
                            <button type="button" id="viewVisualBtn" class="btn btn-sm btn-outline-primary" title="Visual Editor">
                                <i class="fas fa-edit"></i> Visual
                            </button>
                        </div>
                        <div class="editor-toolbar-group code-tools">
                            <button type="button" id="formatCodeBtn" class="btn btn-sm btn-outline-secondary" title="Format Code">
                                <i class="fas fa-align-left"></i> Format
                            </button>
                            <button type="button" id="findReplaceBtn" class="btn btn-sm btn-outline-secondary" title="Find & Replace">
                                <i class="fas fa-search"></i> Find
                            </button>
                        </div>
                    </div>

                    <!-- WYSIWYG Toolbar (only visible in Visual mode) -->
                    <div id="wysiwyg-toolbar" class="wysiwyg-toolbar" style="display: none;">
                        <div class="editor-toolbar-group">
                            <button type="button" data-command="bold" class="btn btn-sm btn-outline-secondary" title="Bold">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" data-command="italic" class="btn btn-sm btn-outline-secondary" title="Italic">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" data-command="underline" class="btn btn-sm btn-outline-secondary" title="Underline">
                                <i class="fas fa-underline"></i>
                            </button>
                            <button type="button" data-command="strikeThrough" class="btn btn-sm btn-outline-secondary" title="Strike Through">
                                <i class="fas fa-strikethrough"></i>
                            </button>
                        </div>
                        <div class="editor-toolbar-group">
                            <button type="button" data-command="justifyLeft" class="btn btn-sm btn-outline-secondary" title="Align Left">
                                <i class="fas fa-align-left"></i>
                            </button>
                            <button type="button" data-command="justifyCenter" class="btn btn-sm btn-outline-secondary" title="Align Center">
                                <i class="fas fa-align-center"></i>
                            </button>
                            <button type="button" data-command="justifyRight" class="btn btn-sm btn-outline-secondary" title="Align Right">
                                <i class="fas fa-align-right"></i>
                            </button>
                            <button type="button" data-command="justifyFull" class="btn btn-sm btn-outline-secondary" title="Justify">
                                <i class="fas fa-align-justify"></i>
                            </button>
                        </div>
                        <div class="editor-toolbar-group">
                            <button type="button" data-command="insertUnorderedList" class="btn btn-sm btn-outline-secondary" title="Bullet List">
                                <i class="fas fa-list-ul"></i>
                            </button>
                            <button type="button" data-command="insertOrderedList" class="btn btn-sm btn-outline-secondary" title="Numbered List">
                                <i class="fas fa-list-ol"></i>
                            </button>
                            <button type="button" data-command="outdent" class="btn btn-sm btn-outline-secondary" title="Decrease Indent">
                                <i class="fas fa-outdent"></i>
                            </button>
                            <button type="button" data-command="indent" class="btn btn-sm btn-outline-secondary" title="Increase Indent">
                                <i class="fas fa-indent"></i>
                            </button>
                        </div>
                        <div class="editor-toolbar-group">
                            <button type="button" data-command="createLink" class="btn btn-sm btn-outline-secondary" title="Insert Link">
                                <i class="fas fa-link"></i>
                            </button>
                            <button type="button" data-command="unlink" class="btn btn-sm btn-outline-secondary" title="Remove Link">
                                <i class="fas fa-unlink"></i>
                            </button>
                            <button type="button" data-command="insertImage" class="btn btn-sm btn-outline-secondary" title="Insert Image">
                                <i class="fas fa-image"></i>
                            </button>
                            <button type="button" data-command="insertTable" class="btn btn-sm btn-outline-secondary" title="Insert Table">
                                <i class="fas fa-table"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Editor Containers -->
                    <div id="editor-container" style="height: 600px;">
                        <textarea name="file_content" id="editor-textarea" class="editor-textarea" style="height: 600px;"><?php echo htmlspecialchars($file_content); ?></textarea>
                    </div>

                    <!-- Visual Editor Container -->
                    <div id="visual-editor-container" style="display: none; height: 600px;">
                        <div id="visual-editor" class="visual-editor" style="height: 600px;"></div>
                    </div>
                </div>

                <div class="editor-footer">
                    <div class="editor-info">
                        <div class="editor-info-item">
                            <i class="fas fa-folder"></i>
                            <span><?php echo htmlspecialchars(dirname($current_file)); ?></span>
                        </div>
                        <div class="editor-info-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date("Y-m-d H:i:s", filemtime($current_file)); ?></span>
                        </div>
                        <div class="editor-info-item">
                            <i class="fas fa-weight"></i>
                            <span><?php echo round(filesize($current_file) / 1024, 2); ?> KB</span>
                        </div>
                    </div>

                    <div class="editor-save-actions">
                        <input type="text" name="version_comment" id="version_comment" class="form-control version-comment" placeholder="Version comment (optional)">

                        <?php if (!isset($is_locked_by_other) || !$is_locked_by_other): ?>
                        <button type="submit" name="save_file" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <?php endif; ?>
                        <a href="html_editor.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>

                <?php if (isset($file_versions_list) && !empty($file_versions_list)): ?>
                <div class="version-history-container">
                    <div class="version-history-toggle" id="versionHistoryToggle">
                        <h4><i class="fas fa-history"></i> Version History <i class="fas fa-chevron-down toggle-icon"></i></h4>
                    </div>
                    <div class="version-history-content" id="versionHistoryContent" style="display: none;">
                        <table class="table" id="versionHistoryTable">
                            <thead>
                                <tr>
                                    <th>Version</th>
                                    <th>Date</th>
                                    <th>User</th>
                                    <th>Comment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($file_versions_list as $version): ?>
                                <tr>
                                    <td><?php echo $version['version']; ?></td>
                                    <td><?php echo date("Y-m-d H:i:s", strtotime($version['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($version['username']); ?></td>
                                    <td><?php echo htmlspecialchars($version['comment'] ?: 'No comment'); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary view-version-btn" data-version="<?php echo $version['version']; ?>">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning restore-version-btn" data-version="<?php echo $version['version']; ?>">
                                            <i class="fas fa-undo"></i> Restore
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </form>
            <?php else: ?>
            <div class="editor-header">
                <h3 class="editor-title">
                    <i class="fas fa-code"></i> HTML Editor
                </h3>
            </div>
            <div class="editor-content">
                <div class="no-file-selected">
                    <div class="no-file-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h4>No File Selected</h4>
                    <p>Please select a file from the list to edit.</p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>



    <!-- Version History Modal -->
    <div id="versionHistoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Version History</h4>
                <button type="button" class="close" id="closeVersionsBtn">&times;</button>
            </div>
            <div class="modal-body">
                <table class="table" id="versionHistoryTable">
                    <thead>
                        <tr>
                            <th>Version</th>
                            <th>Date</th>
                            <th>User</th>
                            <th>Comment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Version history will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Version Preview Modal -->
    <div id="versionPreviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Version Preview</h4>
                <button type="button" class="close" id="closePreviewBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="version-info">
                    <div class="version-info-item">
                        <strong>Version:</strong> <span id="previewVersion"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>Date:</strong> <span id="previewDate"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>User:</strong> <span id="previewUser"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>Comment:</strong> <span id="previewComment"></span>
                    </div>
                </div>
                <div class="version-preview">
                    <textarea id="previewContent" class="preview-content"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" id="restorePreviewBtn">
                    <i class="fas fa-undo"></i> Restore This Version
                </button>
                <button type="button" class="btn btn-secondary" id="closePreviewFooterBtn">Close</button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>