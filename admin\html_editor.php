<?php
/**
 * HTML Editor
 *
 * Allows editing HTML files with read-only header and footer sections
 */

// Start session
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';
require_once 'lib/FileVersions.php';
require_once 'lib/CollaborativeEditing.php';
require_once 'lib/ContentExtractor.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view and edit HTML files
if (!$permissions->hasPermission('view_files') || !$permissions->hasPermission('edit_html_files')) {
    $_SESSION['error_message'] = "You do not have permission to access the HTML Editor.";
    redirect('dashboard.php');
}

// Initialize file versions and collaborative editing
$file_versions = new FileVersions($conn, $_SESSION['user_id']);
$collaborative = new CollaborativeEditing($conn, $_SESSION['user_id']);

// Set page title
$page_title = "HTML Editor";

// Add page-specific CSS and JS
$extra_css = '
<link rel="stylesheet" href="assets/css/pages/html-editor.css?v=' . time() . '">
<!-- TinyMCE CSS -->
<style>
.html-editor-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    min-height: 500px;
}
.tox-tinymce {
    border: none !important;
    border-radius: 4px !important;
}
.tox-editor-container {
    border-radius: 4px !important;
}
.tox-edit-area {
    border: none !important;
}
/* Remove TinyMCE scrollbars */
.tox-edit-area iframe {
    overflow: hidden !important;
}
/* Clean toolbar styling */
.tox-toolbar-overlord {
    background: #f5f5f5 !important;
    border-bottom: 1px solid #ddd !important;
}
.tox-toolbar {
    background: transparent !important;
}
/* Editor title warning styling */
.editor-title small {
    color: #6c757d !important;
    font-size: 12px !important;
    font-weight: normal !important;
    margin-left: 8px;
}
.editor-title small i {
    margin-right: 4px;
    font-size: 11px;
}
/* File selector styling */
.file-selector option:disabled {
    color: #999 !important;
    font-style: italic !important;
    background-color: #f8f9fa !important;
}
.file-selector-info {
    margin-top: 8px;
}
.file-selector-info small {
    color: #6c757d;
    font-size: 12px;
}
</style>
';

$extra_js = '
<!-- TinyMCE from CDN (completely free) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>

<!-- HTML Beautifier for code formatting -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify-html.min.js"></script>

<!-- Custom JS -->
<script src="js/html-editor-locks.js?v=' . time() . '"></script>
<script src="js/html-editor-versions.js?v=' . time() . '"></script>
<script src="js/tinymce-html-editor.js?v=' . time() . '"></script>
<script>
function handleFileSelection(select) {
    if (select.value) {
        // Check if there are unsaved changes
        if (typeof isModified !== "undefined" && isModified) {
            if (confirm("You have unsaved changes. Are you sure you want to leave?")) {
                window.location.href = select.value;
            } else {
                // Reset to current selection
                select.selectedIndex = 0;
                const currentFile = "' . htmlspecialchars($current_file ?? '') . '";
                if (currentFile) {
                    const currentOption = select.querySelector(`option[value*="${encodeURIComponent(currentFile)}"]`);
                    if (currentOption) {
                        currentOption.selected = true;
                    }
                }
            }
        } else {
            window.location.href = select.value;
        }
    }
}

// Simple function to sync TinyMCE content before form submission
function syncTinyMCEContent() {
    console.log("=== CONTENT EXTRACTOR SAVE: Syncing TinyMCE content ===");

    if (typeof tinymce !== "undefined" && tinymce.activeEditor) {
        try {
            // Force TinyMCE to save its content to the textarea
            tinymce.activeEditor.save();

            // Get the content from TinyMCE (this is only the editable content area)
            const editorContent = tinymce.activeEditor.getContent();
            console.log("Editor content length:", editorContent.length);
            console.log("Editor content preview:", editorContent.substring(0, 200) + "...");

            // Get the textarea and update it with the editor content
            const textarea = document.getElementById("tinymce-editor");
            if (textarea) {
                // Simply set the textarea to the editor content
                // The ContentExtractor class on the server will handle injecting this
                // back into the original HTML structure preserving header/footer
                textarea.value = editorContent;
                console.log("✅ Textarea updated with editor content");
                console.log("✅ Server-side ContentExtractor will handle header/footer preservation");
            } else {
                console.error("❌ Textarea not found");
            }
        } catch (error) {
            console.error("Error syncing content:", error);
        }
    } else {
        console.log("TinyMCE not available, form will submit as-is");
    }

    // Allow form to submit normally
    return true;
}
</script>
';

// Define allowed directories and file extensions
$allowed_dirs = [
    '../' => 'Root'
];

// Only allow HTML files for editing
$allowed_extensions = [
    'html' => 'HTML Files'
];

// Define prohibited files (header and footer)
$prohibited_files = [
    '../header.html',
    '../footer.html',
    './header.html',
    './footer.html',
    'header.html',
    'footer.html'
];

// Function to check if a file is a header or footer file
function isHeaderOrFooterFile($filename) {
    $basename = strtolower(basename($filename));
    return $basename === 'header.html' || $basename === 'footer.html';
}

// Function to check if an HTML file contains dynamic content
function containsDynamicContent($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }

    $content = file_get_contents($filepath);

    // Check for PHP-style dynamic content markers
    if (strpos($content, '<?php') !== false ||
        strpos($content, '<?=') !== false ||
        strpos($content, '<%') !== false) {
        return true;
    }

    // Check for server-side includes
    if (strpos($content, '<!--#include') !== false) {
        return true;
    }

    // Check for template syntax
    if (strpos($content, '{{') !== false ||
        strpos($content, '}}') !== false ||
        strpos($content, '{%') !== false ||
        strpos($content, '%}') !== false ||
        strpos($content, '@if') !== false ||
        strpos($content, '@foreach') !== false) {
        return true;
    }

    return false;
}

// Content extraction and injection is now handled by the ContentExtractor class
// This provides more robust and reliable content isolation

// Function to extract CSS files from HTML
function extractCssFiles($html_content, $current_file) {
    $css_files = [];
    $base_path = dirname($current_file);

    error_log("=== CSS EXTRACTION DEBUG ===");
    error_log("Current file: " . $current_file);
    error_log("Base path: " . $base_path);

    // Extract CSS link tags
    if (preg_match_all('/<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']+)["\'][^>]*>/i', $html_content, $matches)) {
        error_log("Found " . count($matches[1]) . " CSS link tags");
        foreach ($matches[1] as $css_url) {
            $resolved_url = resolveUrl($css_url, $base_path);
            $css_files[] = $resolved_url;
            error_log("CSS: " . $css_url . " -> " . $resolved_url);
        }
    } else {
        error_log("No CSS link tags found");
    }

    // Extract inline styles
    if (preg_match_all('/<style[^>]*>(.*?)<\/style>/is', $html_content, $matches)) {
        error_log("Found " . count($matches[1]) . " inline style blocks");
        foreach ($matches[1] as $inline_css) {
            $css_files[] = ['inline' => $inline_css];
            error_log("Inline CSS block length: " . strlen($inline_css));
        }
    } else {
        error_log("No inline style blocks found");
    }

    error_log("Total CSS files extracted: " . count($css_files));

    // Validate that CSS files are accessible
    $validated_css_files = [];
    foreach ($css_files as $css_file) {
        if (is_array($css_file) && isset($css_file['inline'])) {
            // Keep inline styles as-is
            $validated_css_files[] = $css_file;
            error_log("Keeping inline CSS block");
        } else {
            // Validate external CSS file
            $css_path = str_replace(['http://' . $_SERVER['HTTP_HOST'], 'https://' . $_SERVER['HTTP_HOST']], '', $css_file);
            $local_path = $_SERVER['DOCUMENT_ROOT'] . $css_path;

            if (file_exists($local_path)) {
                $validated_css_files[] = $css_file;
                error_log("CSS file validated: " . $css_file . " (local: " . $local_path . ")");
            } else {
                error_log("CSS file NOT FOUND: " . $css_file . " (local: " . $local_path . ")");
            }
        }
    }

    error_log("Validated CSS files: " . count($validated_css_files) . " out of " . count($css_files));
    return $validated_css_files;
}

// Function to extract JS files from HTML
function extractJsFiles($html_content, $current_file) {
    $js_files = [];
    $base_path = dirname($current_file);

    // Extract JS script tags with src
    if (preg_match_all('/<script[^>]*src=["\']([^"\']+)["\'][^>]*><\/script>/i', $html_content, $matches)) {
        foreach ($matches[1] as $js_url) {
            $js_files[] = resolveUrl($js_url, $base_path);
        }
    }

    return $js_files;
}

// Function to resolve relative URLs to absolute URLs for TinyMCE
function resolveUrl($url, $base_path) {
    // If it's already an absolute URL, return as-is
    if (preg_match('/^https?:\/\//', $url)) {
        return $url;
    }

    // Get protocol and host for full URLs
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // If it starts with /, it's relative to document root - return as-is
    if (strpos($url, '/') === 0) {
        return $protocol . '://' . $host . $url;
    }

    // Otherwise, it's relative to the current file's directory
    // The base_path is the file path relative to document root (e.g., "../pages/about.html")
    // We need to get just the directory part and normalize it
    $file_dir = dirname($base_path);

    // Remove '../' prefix if present since we want paths relative to document root
    if (strpos($file_dir, '../') === 0) {
        $file_dir = substr($file_dir, 3);
    }

    // If file_dir is just '.', the file is in document root
    if ($file_dir === '.') {
        $file_dir = '';
    }

    // Build the path relative to document root (NOT admin)
    if (empty($file_dir)) {
        $resolved_path = '/' . ltrim($url, '/');
    } else {
        $resolved_path = '/' . ltrim($file_dir, '/') . '/' . ltrim($url, '/');
    }

    // Clean up double slashes and normalize path
    $resolved_path = preg_replace('/\/+/', '/', $resolved_path);

    // Handle ../ in paths
    $parts = explode('/', $resolved_path);
    $normalized = [];
    foreach ($parts as $part) {
        if ($part === '..') {
            array_pop($normalized);
        } elseif ($part !== '.' && $part !== '') {
            $normalized[] = $part;
        }
    }

    $resolved_path = '/' . implode('/', $normalized);

    $final_url = $protocol . '://' . $host . $resolved_path;

    error_log("=== URL RESOLUTION DEBUG ===");
    error_log("Input URL: '$url'");
    error_log("Base path: '$base_path'");
    error_log("File dir (raw): '" . dirname($base_path) . "'");
    error_log("File dir (processed): '$file_dir'");
    error_log("Resolved path: '$resolved_path'");
    error_log("Final URL: '$final_url'");
    error_log("================================");

    return $final_url;
}

// Function to convert relative URLs in content to absolute URLs
function convertRelativeUrlsToAbsolute($content, $base_file_path) {
    error_log("=== CONVERTING RELATIVE URLS TO ABSOLUTE ===");
    error_log("Base file path: $base_file_path");

    // Convert image src attributes
    $content = preg_replace_callback('/(<img[^>]+src=["\'])([^"\']+)(["\']/i', function($matches) use ($base_file_path) {
        $tag_start = $matches[1];
        $url = $matches[2];
        $tag_end = $matches[3];

        // Skip if already absolute
        if (preg_match('/^https?:\/\//', $url) || strpos($url, 'data:') === 0) {
            return $matches[0];
        }

        $absolute_url = resolveUrl($url, $base_file_path);
        error_log("IMG: $url -> $absolute_url");
        return $tag_start . $absolute_url . $tag_end;
    }, $content);

    // Convert link href attributes (for CSS and other links)
    $content = preg_replace_callback('/(<link[^>]+href=["\'"])([^"\']+)(["\']/i', function($matches) use ($base_file_path) {
        $tag_start = $matches[1];
        $url = $matches[2];
        $tag_end = $matches[3];

        // Skip if already absolute
        if (preg_match('/^https?:\/\//', $url)) {
            return $matches[0];
        }

        $absolute_url = resolveUrl($url, $base_file_path);
        error_log("LINK: $url -> $absolute_url");
        return $tag_start . $absolute_url . $tag_end;
    }, $content);

    // Convert anchor href attributes
    $content = preg_replace_callback('/(<a[^>]+href=["\'"])([^"\']+)(["\']/i', function($matches) use ($base_file_path) {
        $tag_start = $matches[1];
        $url = $matches[2];
        $tag_end = $matches[3];

        // Skip if already absolute, mailto, tel, or anchor links
        if (preg_match('/^(https?:\/\/|mailto:|tel:|#)/', $url)) {
            return $matches[0];
        }

        $absolute_url = resolveUrl($url, $base_file_path);
        error_log("ANCHOR: $url -> $absolute_url");
        return $tag_start . $absolute_url . $tag_end;
    }, $content);

    // Convert script src attributes
    $content = preg_replace_callback('/(<script[^>]+src=["\'"])([^"\']+)(["\']/i', function($matches) use ($base_file_path) {
        $tag_start = $matches[1];
        $url = $matches[2];
        $tag_end = $matches[3];

        // Skip if already absolute
        if (preg_match('/^https?:\/\//', $url)) {
            return $matches[0];
        }

        $absolute_url = resolveUrl($url, $base_file_path);
        error_log("SCRIPT: $url -> $absolute_url");
        return $tag_start . $absolute_url . $tag_end;
    }, $content);

    error_log("=== URL CONVERSION COMPLETE ===");
    return $content;
}

// Initialize variables
$current_dir = isset($_GET['dir']) ? $_GET['dir'] : '../';
$current_file = '';
if (isset($_GET['file']) && !empty($_GET['file'])) {
    // Normalize the file path - ensure it starts with ../
    $file_param = $_GET['file'];
    if (!str_starts_with($file_param, '../')) {
        $current_file = '../' . ltrim($file_param, './');
    } else {
        $current_file = $file_param;
    }
    error_log("File parameter received: " . $file_param);
    error_log("Normalized current file: " . $current_file);
}
$file_content = '';
$file_type = '';
$success_message = '';
$error_message = '';

// Handle release lock action
if (isset($_GET['action']) && $_GET['action'] === 'release_lock' && isset($_GET['file_path'])) {
    $file_path = $_GET['file_path'];

    // Normalize file path
    if (!str_starts_with($file_path, '../')) {
        $file_path = '../' . ltrim($file_path, './');
    }

    // Validate file path - ensure it's within allowed directories
    $valid_path = false;
    $real_file_path = realpath($file_path);
    $real_allowed_path = realpath('../');

    if ($real_file_path && $real_allowed_path && strpos($real_file_path, $real_allowed_path) === 0) {
        $valid_path = true;
    }

    if ($valid_path && file_exists($file_path)) {
        // Release the lock
        $result = $collaborative->releaseLock($file_path);
        if ($result['success']) {
            $success_message = "Lock released successfully.";
        } else {
            $error_message = "Failed to release lock: " . $result['message'];
        }
    } else {
        $error_message = "Invalid file path.";
    }

    // Redirect back to the editor with the same file
    if (!empty($file_path)) {
        $current_file = $file_path;
    }
}

// Validate current directory
if (!array_key_exists($current_dir, $allowed_dirs)) {
    $current_dir = '../';
}

// Handle file save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_file'])) {
    // DEEP DEBUG: Log EVERYTHING about the request
    error_log("=== SAVE REQUEST RECEIVED ===");
    error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
    error_log("POST save_file isset: " . (isset($_POST['save_file']) ? 'YES' : 'NO'));
    error_log("POST keys: " . implode(', ', array_keys($_POST)));
    error_log("POST count: " . count($_POST));
    error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'NOT SET'));
    error_log("Content-Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'NOT SET'));

    // Log each POST variable in detail
    foreach ($_POST as $key => $value) {
        if ($key === 'file_content') {
            error_log("POST[{$key}]: " . strlen($value) . " characters");
            error_log("POST[{$key}] first 500 chars: " . substr($value, 0, 500));
            error_log("POST[{$key}] last 200 chars: " . substr($value, -200));
            error_log("POST[{$key}] contains HTML tags: " . (preg_match('/<[^>]+>/', $value) ? 'YES' : 'NO'));
        } else {
            error_log("POST[{$key}]: " . $value);
        }
    }

    // Check if this is the original content or edited content
    if (isset($_POST['file_content']) && isset($_POST['file_path'])) {
        $posted_content = $_POST['file_content'];
        $posted_file_path = $_POST['file_path'];

        // Try to read the current file content to compare
        if (file_exists($posted_file_path)) {
            $current_file_content = file_get_contents($posted_file_path);
            $is_same_as_current = ($posted_content === $current_file_content);
            error_log("Posted content same as current file: " . ($is_same_as_current ? 'YES - NO CHANGES!' : 'NO - HAS CHANGES'));

            if ($is_same_as_current) {
                error_log("WARNING: Posted content is identical to current file - no actual changes detected!");
            }
        }
    }

    $file_path = $_POST['file_path'];
    error_log("File path to save: " . $file_path);

    // Normalize file path to ensure consistency
    $normalized_file_path = str_replace('\\', '/', $file_path);
    if (!str_starts_with($normalized_file_path, '../')) {
        $normalized_file_path = '../' . ltrim($normalized_file_path, './');
    }
    error_log("Normalized file path: " . $normalized_file_path);

    // Validate file path - ensure it's within allowed directories
    $valid_path = false;
    $real_file_path = realpath($normalized_file_path);
    $real_allowed_path = realpath('../');

    if ($real_file_path && $real_allowed_path && strpos($real_file_path, $real_allowed_path) === 0) {
        $valid_path = true;
        $file_path = $normalized_file_path; // Use normalized path
    }

    error_log("Real file path: " . ($real_file_path ?: 'NOT FOUND'));
    error_log("Real allowed path: " . ($real_allowed_path ?: 'NOT FOUND'));
    error_log("File path validation: " . ($valid_path ? 'VALID' : 'INVALID'));

    // Check if file is prohibited (header or footer)
    if (in_array($file_path, $prohibited_files) || isHeaderOrFooterFile($file_path)) {
        $error_message = "This file is protected and cannot be edited.";
    }
    else if (!$valid_path) {
        $error_message = "Invalid file path.";
    } else {
        // Get file extension
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if file extension is allowed (only HTML)
        if ($file_extension !== 'html') {
            $error_message = "Only HTML files can be edited in this interface.";
        } else {
            // Check if file contains dynamic content
            if (containsDynamicContent($file_path)) {
                $error_message = "This file contains dynamic content and cannot be edited in this interface.";
            } else {
                // Handle file content update
                if (isset($_POST['file_content'])) {
                    // Check if user has write permission for this file
                    if (!$permissions->canWriteFile($file_path)) {
                        $error_message = "You do not have permission to edit this file.";
                    } else {
                        $new_content = $_POST['file_content'];
                        $comment = isset($_POST['version_comment']) ? $_POST['version_comment'] : '';

                        // For HTML files, we need to inject the edited content back into the original structure
                        $content_to_save = $new_content;

                        if ($file_extension === 'html') {
                            // Get the original HTML structure from session
                            $original_html = isset($_SESSION['original_html_' . md5($file_path)])
                                ? $_SESSION['original_html_' . md5($file_path)]
                                : '';

                            // If we have the original HTML, inject the edited content
                            if (!empty($original_html)) {
                                // Use our ContentExtractor class to handle the injection
                                $content_to_save = ContentExtractor::injectContent($original_html, $new_content);
                                error_log("✅ Content injected back into original HTML structure");
                            } else {
                                error_log("⚠️ No original HTML found in session, saving content as-is");
                            }
                        }

                            // Check if file is locked by another user
                            $lock = $collaborative->getLock($file_path);
                            if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                                $error_message = "This file is currently being edited by {$lock['username']}. Please try again later.";
                            } else {
                            // Ensure we're saving to the correct file path
                            $absolute_file_path = realpath($file_path);
                            if (!$absolute_file_path) {
                                // If realpath fails, create the absolute path manually
                                $absolute_file_path = realpath(dirname($file_path)) . '/' . basename($file_path);
                            }

                            // Debug logging
                            error_log("=== FILE SAVE ATTEMPT ===");
                            error_log("Original file_path from POST: " . $_POST['file_path']);
                            error_log("Normalized file_path: " . $file_path);
                            error_log("Attempting to save file: " . $file_path);
                            error_log("Absolute path: " . $absolute_file_path);
                            error_log("File exists: " . (file_exists($file_path) ? 'YES' : 'NO'));
                            error_log("Directory exists: " . (is_dir(dirname($file_path)) ? 'YES' : 'NO'));
                            error_log("Directory writable: " . (is_writable(dirname($file_path)) ? 'YES' : 'NO'));
                            error_log("Original content length: " . strlen($new_content));
                            error_log("Final content length: " . strlen($content_to_save));
                            error_log("Content preview: " . substr($content_to_save, 0, 200) . "...");

                            // Ensure directory exists
                            $dir = dirname($file_path);
                            if (!is_dir($dir)) {
                                error_log("Directory does not exist: " . $dir);
                                $error_message = "Directory does not exist: " . $dir;
                            } else {
                                // Write content to file
                                $bytes_written = file_put_contents($file_path, $content_to_save, LOCK_EX);
                                if ($bytes_written !== false) {
                                error_log("File saved successfully. Bytes written: " . $bytes_written);

                                // Save version history
                                try {
                                    $version_saved = $file_versions->saveVersion($file_path, $content_to_save, $comment);
                                    if ($version_saved) {
                                        error_log("Version history saved successfully");
                                    } else {
                                        error_log("Failed to save version history");
                                    }
                                } catch (Exception $e) {
                                    error_log("Error saving version history: " . $e->getMessage());
                                }

                                // Extend lock
                                if ($lock && $lock['user_id'] == $_SESSION['user_id']) {
                                    $collaborative->extendLock($file_path);
                                }

                                $success_message = "File saved successfully. Content area updated while preserving header and footer.";
                                $current_file = $file_path;
                                // Update the displayed content to show only the content area again
                                $file_content = $new_content;

                                // Reload version history after save
                                if ($permissions->hasPermission('manage_file_versions')) {
                                    $file_versions_list = $file_versions->getAllVersions($current_file);
                                    error_log("Reloaded version history: " . count($file_versions_list) . " versions found");
                                }
                                } else {
                                    $error_message = "Failed to save file. Check file permissions.";
                                    error_log("Failed to save file: " . $file_path . " - " . error_get_last()['message']);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// Get file content if a file is selected
if (!empty($current_file)) {
    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($current_file, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    // Check if file is prohibited (header or footer)
    if (in_array($current_file, $prohibited_files) || isHeaderOrFooterFile($current_file)) {
        $error_message = "This file is protected and cannot be edited.";
        $current_file = '';
    }
    // Check if file is not HTML
    else if (strtolower(pathinfo($current_file, PATHINFO_EXTENSION)) !== 'html') {
        $error_message = "Only HTML files can be edited in this interface.";
        $current_file = '';
    }
    // Check if file contains dynamic content
    else if (containsDynamicContent($current_file)) {
        $error_message = "This file contains dynamic content and cannot be edited in this interface.";
        $current_file = '';
    }
    else if ($valid_path && file_exists($current_file)) {
        // Check if user has permission to read this file
        if (!$permissions->canReadFile($current_file)) {
            $error_message = "You do not have permission to access this file.";
            $current_file = '';
        } else {
            // Get file content
            $original_html = file_get_contents($current_file);
            $file_type = strtolower(pathinfo($current_file, PATHINFO_EXTENSION));

            // Extract only the content area from HTML files
            if ($file_type === 'html') {
                // Store the original HTML for later use when saving
                $_SESSION['original_html_' . md5($current_file)] = $original_html;
                // Extract only the content area
                $file_content = ContentExtractor::extractContent($original_html);

                // Pre-process content to convert relative URLs to absolute URLs
                $file_content = convertRelativeUrlsToAbsolute($file_content, $current_file);
                // Extract CSS and JS files for TinyMCE styling
                $css_files = extractCssFiles($original_html, $current_file);
                $js_files = extractJsFiles($original_html, $current_file);

                // Debug: Log what was extracted
                error_log("=== CSS/JS EXTRACTION RESULTS ===");
                error_log("File: " . $current_file);
                error_log("CSS files found: " . count($css_files));
                error_log("JS files found: " . count($js_files));
                foreach ($css_files as $i => $css) {
                    if (is_array($css)) {
                        error_log("CSS $i: INLINE (" . strlen($css['inline']) . " chars)");
                    } else {
                        error_log("CSS $i: $css");
                    }
                }

                // Store these for TinyMCE configuration
                $_SESSION['css_files_' . md5($current_file)] = $css_files;
                $_SESSION['js_files_' . md5($current_file)] = $js_files;
            } else {
                $file_content = $original_html;
            }

            // Check if file is locked by another user
            $lock = $collaborative->getLock($current_file);
            if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                $lock_message = "This file is currently being edited by {$lock['username']}. You can view it, but you cannot save changes.";
                $is_locked_by_other = true;
            } else {
                // Acquire lock for editing
                if (!$lock && $permissions->canWriteFile($current_file)) {
                    $collaborative->acquireLock($current_file);
                }
                $is_locked_by_other = false;
            }

            // Get file versions
            if ($permissions->hasPermission('manage_file_versions')) {
                $file_versions_list = $file_versions->getAllVersions($current_file);
                // Debug: Log version history data
                error_log("Version history for {$current_file}: " . count($file_versions_list) . " versions found");
            } else {
                // Debug: Log permission issue
                error_log("User does not have manage_file_versions permission");
                $file_versions_list = [];
            }
        }
    } else {
        $error_message = "Invalid file or file does not exist.";
        $current_file = '';
    }
}

// Get files in current directory
$files = [];
if (is_dir($current_dir)) {
    $dir_contents = scandir($current_dir);
    foreach ($dir_contents as $item) {
        if ($item != '.' && $item != '..') {
            $full_path = $current_dir . $item;
            $is_dir = is_dir($full_path);
            $extension = $is_dir ? '' : strtolower(pathinfo($item, PATHINFO_EXTENSION));

            // We'll show all files in dropdown but mark non-editable ones as disabled

            // Only include directories and HTML files
            if ($is_dir || $extension === 'html') {
                $has_dynamic_content = false;
                $is_prohibited = false;

                if (!$is_dir && $extension === 'html') {
                    $has_dynamic_content = containsDynamicContent($full_path);
                    $is_prohibited = in_array($full_path, $prohibited_files) || isHeaderOrFooterFile($full_path);
                }

                $files[] = [
                    'name' => $item,
                    'path' => $full_path,
                    'is_dir' => $is_dir,
                    'extension' => $extension,
                    'size' => $is_dir ? '' : filesize($full_path),
                    'modified' => date("Y-m-d H:i:s", filemtime($full_path)),
                    'has_dynamic_content' => $has_dynamic_content,
                    'is_prohibited' => $is_prohibited,
                    'is_editable' => !$is_dir && !$has_dynamic_content && !$is_prohibited
                ];
            }
        }
    }
}

// Sort files: directories first, then files
usort($files, function($a, $b) {
    if ($a['is_dir'] && !$b['is_dir']) {
        return -1;
    } elseif (!$a['is_dir'] && $b['is_dir']) {
        return 1;
    } else {
        return strcasecmp($a['name'], $b['name']);
    }
});

include 'includes/header.php';
?>

<div class="admin-container" data-is-admin="<?php echo isset($_SESSION['is_admin']) && $_SESSION['is_admin'] ? 'true' : 'false'; ?>" data-user-id="<?php echo $_SESSION['user_id']; ?>" data-username="<?php echo htmlspecialchars($_SESSION['username']); ?>">
    <?php
    // Set up variables for content header
    $page_icon = 'fas fa-code';
    $page_title = 'HTML Editor';
    $page_description = 'Edit HTML files with read-only header and footer sections';
    $stats_data = [];

    // Include the content header
    include 'includes/content-header.php';
    ?>

    <!-- File Selector Dropdown -->
    <div class="file-selector-container">
        <div class="file-selector-wrapper">
            <label for="fileSelector" class="file-selector-label">Select HTML File:</label>
            <select id="fileSelector" class="file-selector" onchange="handleFileSelection(this);">
                <option value="">-- Select a file to edit --</option>
                <?php foreach ($files as $file): ?>
                    <?php if (!$file['is_dir']): ?>
                        <?php if ($file['is_editable']): ?>
                            <?php
                            // Create clean file path for URL
                            $clean_path = str_replace('../', '', $file['path']);
                            ?>
                            <option value="html_editor.php?file=<?php echo urlencode($clean_path); ?>" <?php echo ($current_file == $file['path']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($file['name']); ?>
                            </option>
                        <?php else: ?>
                            <option value="" disabled style="color: #999; font-style: italic; background-color: #f8f9fa;">
                                <?php echo htmlspecialchars($file['name']); ?>
                                <?php if ($file['has_dynamic_content']): ?>
                                    🔧 (Dynamic Content - Cannot Edit)
                                <?php elseif ($file['is_prohibited']): ?>
                                    🔒 (Protected File - Cannot Edit)
                                <?php endif; ?>
                            </option>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="file-selector-info">
            <small><i class="fas fa-info-circle"></i>
                Editable files are shown normally.
                🔧 = Dynamic content files (cannot edit).
                🔒 = Protected files (cannot edit).
            </small>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible" id="successAlert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Success</div>
                <div class="alert-text"><?php echo $success_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <script>
            // Auto-hide success message after 5 seconds
            setTimeout(function() {
                const alert = document.getElementById('successAlert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                }
            }, 5000);
        </script>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible" id="errorAlert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Error</div>
                <div class="alert-text"><?php echo $error_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <?php if (!empty($lock_message)): ?>
        <div class="alert alert-warning alert-dismissible" id="lockAlert">
            <div class="alert-icon">
                <i class="fas fa-lock"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">File Locked</div>
                <div class="alert-text"><?php echo $lock_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <!-- Main Editor Layout -->
    <div class="editor-container">
        <!-- Editor Area -->
        <div class="editor-area">
            <?php if (!empty($current_file)): ?>
            <form method="post" action="html_editor.php" id="fileEditForm" class="editor-form">
            <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($current_file); ?>">

                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Saving...</span>
                    </div>
                </div>

                <div class="editor-header">
                    <h3 class="editor-title">
                        <i class="fas fa-file-code"></i>
                        <?php echo htmlspecialchars(basename($current_file)); ?>
                        <small class="text-muted ms-2" style="font-size: 12px; font-weight: normal;">
                            <i class="fas fa-shield-alt"></i> Header/footer sections are protected
                        </small>
                    </h3>
                    <div class="editor-actions">
                        <div id="lockStatus" class="lock-status not-locked">
                            <i class="fas fa-unlock"></i> File is not locked
                        </div>
                        <!-- Release lock button -->
                        <?php
                        $clean_current_file = str_replace('../', '', $current_file);
                        ?>
                        <a href="?action=release_lock&file_path=<?php echo urlencode($current_file); ?>&file=<?php echo urlencode($clean_current_file); ?>" id="unlockFileBtn" class="btn btn-success btn-sm" title="Release Lock" style="display: none;">
                            <i class="fas fa-unlock"></i> Release Lock
                        </a>
                        <?php if (isset($is_locked_by_other) && $is_locked_by_other && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1): ?>
                        <button type="button" id="forceUnlockBtn" class="btn btn-danger btn-sm" title="Force Unlock">
                            <i class="fas fa-unlock"></i> Force Unlock
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="editor-content" id="editor-content-container" style="min-height: 600px; height: auto !important; max-height: none !important;">
                    <!-- TinyMCE HTML Editor Container -->
                    <div class="html-editor-container">
                        <textarea name="file_content" id="tinymce-editor"><?php echo htmlspecialchars($file_content); ?></textarea>
                    </div>
                </div>

                <div class="editor-footer">
                    <div class="editor-info">
                        <div class="editor-info-item">
                            <i class="fas fa-folder"></i>
                            <span><?php echo htmlspecialchars(dirname($current_file)); ?></span>
                        </div>
                        <div class="editor-info-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date("Y-m-d H:i:s", filemtime($current_file)); ?></span>
                        </div>
                        <div class="editor-info-item">
                            <i class="fas fa-weight"></i>
                            <span><?php echo round(filesize($current_file) / 1024, 2); ?> KB</span>
                        </div>
                    </div>

                    <div class="editor-save-actions">
                        <input type="text" name="version_comment" id="version_comment" class="form-control version-comment" placeholder="Version comment (optional)">

                        <?php if (!isset($is_locked_by_other) || !$is_locked_by_other): ?>
                        <button type="submit" name="save_file" class="btn btn-primary" onclick="syncTinyMCEContent()">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <?php endif; ?>
                        <a href="html_editor.php?file=<?php echo urlencode($clean_current_file); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>

                <?php if (isset($file_versions_list) && !empty($file_versions_list)): ?>
                <!-- Debug: Version history section is being rendered -->
                <script>console.log('Version history section rendered with <?php echo count($file_versions_list); ?> versions');</script>
                <div class="version-history-container">
                    <div class="version-history-toggle" id="versionHistoryToggle">
                        <h4><i class="fas fa-history"></i> Version History (<?php echo count($file_versions_list); ?>) <i class="fas fa-chevron-down toggle-icon"></i></h4>
                    </div>
                    <div class="version-history-content" id="versionHistoryContent" style="display: none;">
                        <table class="table" id="versionHistoryTable">
                            <thead>
                                <tr>
                                    <th>Version</th>
                                    <th>Date</th>
                                    <th>User</th>
                                    <th>Comment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($file_versions_list as $version): ?>
                                <tr>
                                    <td><?php echo $version['version']; ?></td>
                                    <td><?php echo date("Y-m-d H:i:s", strtotime($version['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($version['username']); ?></td>
                                    <td><?php echo htmlspecialchars($version['comment'] ?: 'No comment'); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary view-version-btn" data-version="<?php echo $version['version']; ?>">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning restore-version-btn" data-version="<?php echo $version['version']; ?>">
                                            <i class="fas fa-undo"></i> Restore
                                        </button>
                                        <?php if ($permissions->hasPermission('manage_file_versions')): ?>
                                        <button type="button" class="btn btn-sm btn-danger delete-version-btn" data-version="<?php echo $version['version']; ?>" data-file="<?php echo htmlspecialchars($current_file); ?>">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php else: ?>
                <!-- Show version history section even if no versions exist yet -->
                <?php if ($permissions->hasPermission('manage_file_versions')): ?>
                <div class="version-history-container">
                    <div class="version-history-toggle" id="versionHistoryToggle">
                        <h4><i class="fas fa-history"></i> Version History (0) <i class="fas fa-chevron-down toggle-icon"></i></h4>
                    </div>
                    <div class="version-history-content" id="versionHistoryContent" style="display: none;">
                        <div class="no-versions-message" style="padding: 20px; text-align: center; color: #6c757d;">
                            <i class="fas fa-history" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <p>No version history available yet.</p>
                            <p><small>Versions will appear here after you save changes to this file.</small></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </form>
            <?php else: ?>
            <div class="editor-header">
                <h3 class="editor-title">
                    <i class="fas fa-code"></i> HTML Editor
                </h3>
            </div>
            <div class="editor-content">
                <div class="no-file-selected">
                    <div class="no-file-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h4>No File Selected</h4>
                    <p>Please select a file from the list to edit.</p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>



    <!-- Version History Modal -->
    <div id="versionHistoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Version History</h4>
                <button type="button" class="close" id="closeVersionsBtn">&times;</button>
            </div>
            <div class="modal-body">
                <table class="table" id="versionHistoryTable">
                    <thead>
                        <tr>
                            <th>Version</th>
                            <th>Date</th>
                            <th>User</th>
                            <th>Comment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Version history will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Version Preview Modal -->
    <div id="versionPreviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Version Preview</h4>
                <button type="button" class="close" id="closePreviewBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="version-info">
                    <div class="version-info-item">
                        <strong>Version:</strong> <span id="previewVersion"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>Date:</strong> <span id="previewDate"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>User:</strong> <span id="previewUser"></span>
                    </div>
                    <div class="version-info-item">
                        <strong>Comment:</strong> <span id="previewComment"></span>
                    </div>
                </div>
                <div class="version-preview">
                    <textarea id="previewContent" class="preview-content"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" id="restorePreviewBtn">
                    <i class="fas fa-undo"></i> Restore This Version
                </button>
                <button type="button" class="btn btn-secondary" id="closePreviewFooterBtn">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Pass extracted CSS and JS files to TinyMCE for proper styling
<?php
// Pass extracted CSS files to JavaScript for TinyMCE configuration
$css_files_json = '[]';
$js_files_json = '[]';
if (!empty($current_file)) {
    $css_files = isset($_SESSION['css_files_' . md5($current_file)]) ? $_SESSION['css_files_' . md5($current_file)] : [];
    $js_files = isset($_SESSION['js_files_' . md5($current_file)]) ? $_SESSION['js_files_' . md5($current_file)] : [];
    $css_files_json = json_encode($css_files);
    $js_files_json = json_encode($js_files);
}
?>
window.extractedCssFiles = <?php echo $css_files_json; ?>;
window.extractedJsFiles = <?php echo $js_files_json; ?>;

console.log('=== EXTRACTED FILES FOR TINYMCE ===');
console.log('CSS files:', window.extractedCssFiles);
console.log('JS files:', window.extractedJsFiles);

// Test CSS file accessibility and provide detailed debugging
if (window.extractedCssFiles && window.extractedCssFiles.length > 0) {
    console.log('=== CSS FILE ACCESSIBILITY TEST ===');
    window.extractedCssFiles.forEach((cssFile, index) => {
        if (typeof cssFile === 'string') {
            console.log(`Testing CSS file ${index}: ${cssFile}`);

            // Test with fetch
            fetch(cssFile, { method: 'HEAD' })
                .then(response => {
                    console.log(`✅ CSS file ${index} (${cssFile}): ${response.ok ? 'ACCESSIBLE' : 'NOT ACCESSIBLE'} - Status: ${response.status}`);
                    if (!response.ok) {
                        console.log(`   Response headers:`, [...response.headers.entries()]);
                    }
                })
                .catch(error => {
                    console.log(`❌ CSS file ${index} (${cssFile}): ERROR - ${error.message}`);

                    // Try alternative test - create a link element
                    const testLink = document.createElement('link');
                    testLink.rel = 'stylesheet';
                    testLink.href = cssFile;
                    testLink.onload = () => {
                        console.log(`   Alternative test: CSS file ${index} loaded successfully via link element`);
                        document.head.removeChild(testLink);
                    };
                    testLink.onerror = () => {
                        console.log(`   Alternative test: CSS file ${index} failed to load via link element`);
                        if (document.head.contains(testLink)) {
                            document.head.removeChild(testLink);
                        }
                    };
                    document.head.appendChild(testLink);
                });
        } else {
            console.log(`📝 CSS file ${index}: INLINE STYLES (${Object.keys(cssFile).join(', ')})`);
            if (cssFile.inline) {
                console.log(`   Inline CSS length: ${cssFile.inline.length} characters`);
                console.log(`   Inline CSS preview: ${cssFile.inline.substring(0, 100)}...`);
            }
        }
    });
} else {
    console.log('❌ No CSS files extracted or passed to JavaScript');
    console.log('This means either:');
    console.log('1. The HTML file has no CSS references');
    console.log('2. CSS extraction failed');
    console.log('3. CSS files were not passed from PHP to JavaScript');
}
</script>

<style>
/* Enhanced dropdown styling for file indicators */
.file-selector select option:disabled {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    font-style: italic !important;
}

.file-selector select option[value=""]:disabled {
    background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%) !important;
    border-left: 3px solid #ffc107 !important;
}

.file-selector-info {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #e7f3ff;
    border-left: 3px solid #007bff;
    border-radius: 4px;
}

.file-selector-info small {
    color: #495057;
    font-size: 0.875rem;
}
</style>

<?php include 'includes/footer.php'; ?>