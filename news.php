<?php
// Include database connection
require_once 'admin/config.php';

// Include frontend settings helper
require_once 'includes/FrontendSettings.php';

// Initialize frontend settings
$frontendSettings = new FrontendSettings($conn);
$newsSettings = $frontendSettings->getNewsSettings();
$metaTags = $frontendSettings->getNewsMetaTags();

// Function to safely escape HTML
function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

// Function to truncate text
function truncateText($text, $length = 150) {
    if (!$text) return '';
    // Remove HTML tags
    $text = strip_tags($text);
    return (strlen($text) > $length) ? substr($text, 0, $length) . '...' : $text;
}

// Get news items from database with pagination
function getNewsItems($conn, $page = 1, $items_per_page = 9) {
    $items = [];
    $error = null;
    $total_items = 0;

    try {
        // Check which image column exists (image or featured_image)
        $image_column = 'image'; // Default
        $column_check = $conn->query("SHOW COLUMNS FROM news LIKE 'featured_image'");
        if ($column_check && $column_check->num_rows > 0) {
            $image_column = 'featured_image';
        }

        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) as total FROM news";
        $count_result = $conn->query($count_sql);

        if (!$count_result) {
            throw new Exception("Count query failed: " . $conn->error);
        }

        $count_row = $count_result->fetch_assoc();
        $total_items = $count_row['total'];

        // Calculate offset for pagination
        $offset = ($page - 1) * $items_per_page;

        // Get paginated news items
        $sql = "SELECT n.id, n.title, n.content, n.$image_column as image, n.slug, n.created_at,
                       IFNULL(c.name, 'Uncategorized') as category_name
                FROM news n
                LEFT JOIN categories c ON n.category_id = c.id
                ORDER BY n.created_at DESC
                LIMIT ?, ?";

        $stmt = $conn->prepare($sql);

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        $stmt->bind_param("ii", $offset, $items_per_page);
        $stmt->execute();
        $result = $stmt->get_result();

        if (!$result) {
            throw new Exception("Query failed: " . $conn->error);
        }

        while ($row = $result->fetch_assoc()) {
            // Format the date
            $row['formatted_date'] = date('F j, Y', strtotime($row['created_at']));
            $items[] = $row;
        }

        $result->free();
        $stmt->close();
    } catch (Exception $e) {
        $error = $e->getMessage();
    }

    // Calculate total pages
    $total_pages = ceil($total_items / $items_per_page);

    return [
        'items' => $items,
        'count' => count($items),
        'total_items' => $total_items,
        'current_page' => $page,
        'items_per_page' => $items_per_page,
        'total_pages' => $total_pages,
        'error' => $error
    ];
}

// Get current page from URL
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page); // Ensure page is at least 1

// Get items per page from settings
$items_per_page = $newsSettings['news_per_page'];
$excerpt_length = $newsSettings['news_excerpt_length'];

// Get news data with pagination
$newsData = getNewsItems($conn, $page, $items_per_page);

// Add settings to news data
$newsData['show_date'] = $newsSettings['show_news_date'];
$newsData['show_category'] = $newsSettings['show_news_category'];
$newsData['pagination_position'] = $newsSettings['pagination_position'];
?>
<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="description" content="<?php echo escapeHtml($metaTags['description']); ?>">
    <title><?php echo escapeHtml($metaTags['title']); ?></title>
    <link rel="stylesheet" href="css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/carousel.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/submenu.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/news-fixes.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Ensure mobile menu toggle is visible */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block !important;
            }

            .main-nav {
                display: none !important;
            }

            .main-nav.active {
                display: block !important;
            }

            .logo {
                text-align: left !important;
            }

            .main-header .container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .news-page-banner {
                margin-top: 70px !important;
                padding: 40px 0 !important;
            }

            .news-grid {
                grid-template-columns: 1fr !important;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="Manage Incorporated - Established in 1995">
                </a>
            </div>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html">HOME</a></li>
                    <li><a href="cloud.html">CLOUD</a></li>
                    <li><a href="managed-services.html">MANAGED SERVICES</a></li>
                    <li><a href="infrastructure.html">INFRASTRUCTURE</a></li>
                    <li><a href="services.html">SERVICES</a></li>
                    <li class="active"><a href="news.php">NEWS</a></li>
                    <li><a href="contact-us.html">CONTACT US</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="news-page-banner" style="margin-top: 95px;">
            <div class="container">
                <h1>News</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <?php if (!empty($newsData['error'])): ?>
                    <div style="color: #e74c3c; background-color: rgba(231, 76, 60, 0.1); border: 1px solid #e74c3c; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                        <p>Error: <?php echo $newsData['error']; ?></p>
                    </div>
                <?php endif; ?>

                <?php if (empty($newsData['items'])): ?>
                    <div style="text-align: center; padding: 50px 0;">
                        <h2>No news posts found</h2>
                        <p>Check back later for updates.</p>
                    </div>
                <?php else: ?>
                    <div class="news-grid">
                        <?php foreach ($newsData['items'] as $news): ?>
                            <div class="news-item">
                                <div class="news-image">
                                    <img src="images/news/<?php echo escapeHtml($news['image']); ?>"
                                         alt="<?php echo escapeHtml($news['title']); ?>"
                                         onerror="this.src='images/news/news-banner.jpg'">
                                </div>
                                <div class="news-content">
                                    <h2><?php echo escapeHtml($news['title']); ?></h2>
                                    <?php if ($newsData['show_date']): ?>
                                    <div class="news-date">
                                        <i class="far fa-calendar-alt"></i>
                                        <span class="news-date-formatted"><?php echo $news['formatted_date']; ?></span>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($newsData['show_category']): ?>
                                    <div class="news-category">
                                        <?php echo escapeHtml($news['category_name']); ?>
                                    </div>
                                    <?php endif; ?>
                                    <p>
                                        <?php echo truncateText($news['content'], $excerpt_length); ?>
                                    </p>
                                </div>
                                <div class="news-footer">
                                    <div class="news-footer-text">
                                        SHOWING ARTICLE WITH CATEGORY: <?php echo strtoupper(escapeHtml($news['category_name'])); ?>
                                    </div>
                                    <a href="news-detail.html?slug=<?php echo $news['slug'] ? $news['slug'] : $news['id']; ?>" class="read-more-btn">read more</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php
                    // Check if we should show pagination at the top
                    if ($newsData['pagination_position'] === 'top' || $newsData['pagination_position'] === 'both'):
                    ?>
                    <!-- Top Pagination -->
                    <?php if ($newsData['total_pages'] > 1): ?>
                    <div class="pagination-container pagination-top">
                        <div class="pagination-info">
                            Showing <?php echo ($newsData['current_page'] - 1) * $newsData['items_per_page'] + 1; ?> to
                            <?php echo min($newsData['current_page'] * $newsData['items_per_page'], $newsData['total_items']); ?>
                            of <?php echo $newsData['total_items']; ?> results
                        </div>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php
                    // Check if we should show pagination at the bottom
                    if ($newsData['pagination_position'] === 'bottom' || $newsData['pagination_position'] === 'both'):
                    ?>
                    <?php if ($newsData['total_pages'] > 1): ?>
                    <!-- Bottom Pagination -->
                    <div class="pagination-container">
                        <div class="pagination-info">
                            Showing <?php echo ($newsData['current_page'] - 1) * $newsData['items_per_page'] + 1; ?> to
                            <?php echo min($newsData['current_page'] * $newsData['items_per_page'], $newsData['total_items']); ?>
                            of <?php echo $newsData['total_items']; ?> results
                        </div>
                        <ul class="pagination">
                            <?php if ($newsData['current_page'] > 1): ?>
                                <li><a href="?page=1" title="First Page"><i class="fas fa-angle-double-left"></i></a></li>
                                <li><a href="?page=<?php echo $newsData['current_page'] - 1; ?>" title="Previous Page"><i class="fas fa-angle-left"></i></a></li>
                            <?php else: ?>
                                <li><span class="disabled"><i class="fas fa-angle-double-left"></i></span></li>
                                <li><span class="disabled"><i class="fas fa-angle-left"></i></span></li>
                            <?php endif; ?>

                            <?php
                            // Calculate range of page numbers to show
                            $range = 2; // Show 2 pages before and after current page
                            $start_page = max(1, $newsData['current_page'] - $range);
                            $end_page = min($newsData['total_pages'], $newsData['current_page'] + $range);

                            // Always show first page
                            if ($start_page > 1) {
                                echo '<li><a href="?page=1">1</a></li>';
                                if ($start_page > 2) {
                                    echo '<li><span class="disabled">...</span></li>';
                                }
                            }

                            // Show page numbers
                            for ($i = $start_page; $i <= $end_page; $i++) {
                                if ($i == $newsData['current_page']) {
                                    echo '<li><span class="active">' . $i . '</span></li>';
                                } else {
                                    echo '<li><a href="?page=' . $i . '">' . $i . '</a></li>';
                                }
                            }

                            // Always show last page
                            if ($end_page < $newsData['total_pages']) {
                                if ($end_page < $newsData['total_pages'] - 1) {
                                    echo '<li><span class="disabled">...</span></li>';
                                }
                                echo '<li><a href="?page=' . $newsData['total_pages'] . '">' . $newsData['total_pages'] . '</a></li>';
                            }
                            ?>

                            <?php if ($newsData['current_page'] < $newsData['total_pages']): ?>
                                <li><a href="?page=<?php echo $newsData['current_page'] + 1; ?>" title="Next Page"><i class="fas fa-angle-right"></i></a></li>
                                <li><a href="?page=<?php echo $newsData['total_pages']; ?>" title="Last Page"><i class="fas fa-angle-double-right"></i></a></li>
                            <?php else: ?>
                                <li><span class="disabled"><i class="fas fa-angle-right"></i></span></li>
                                <li><span class="disabled"><i class="fas fa-angle-double-right"></i></span></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>

                <style>
                /* Add styles for pagination positioning */
                .pagination-top {
                    margin-bottom: 30px;
                }
                </style>
            </div>
        </section>
    </main>

    <footer class="footer">
        <!-- Footer Top Section -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-top-content">
                    <div class="footer-info">
                        <h2>BUILD A FUTURE-READY IT INFRASTRUCTURE</h2>
                        <p>Leverage cutting-edge technology to optimize performance, security, and scalability.</p>
                    </div>
                    <div class="footer-form">
                        <form method="post" action="process-contact.php" id="footer-contact-form">
                            <input type="hidden" name="source" value="Footer Form">
                            <div class="form-row">
                                <input type="text" name="name" placeholder="Name" required>
                                <input type="email" name="email" placeholder="Email Address" required>
                            </div>
                            <textarea name="message" placeholder="Message" required></textarea>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section -->
        <div class="footer-middle">
            <div class="container">
                <div class="footer-middle-content">
                    <div class="footer-logo">
                        <img src="images/footer/manage-inc-logo.png" alt="Manage Incorporated">
                    </div>
                    <div class="footer-services">
                        <h4>SERVICES</h4>
                        <ul>
                            <li><a href="cloud.html">Cloud</a></li>
                            <li><a href="managed-services.html">Managed Services</a></li>
                            <li><a href="infrastructure.html">Infrastructure</a></li>
                            <li><a href="services.html">Services</a></li>
                        </ul>
                    </div>
                    <div class="footer-contact">
                        <h4>Seattle, WA Office</h4>
                        <p><img src="images/footer/phone-icon.svg" alt="Phone"> (*************</p>
                        <p><img src="images/footer/fax-icon.svg" alt="Fax"> (*************</p>
                        <p><img src="images/footer/email-icon.svg" alt="Email"> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><img src="images/footer/location-icon.svg" alt="Location"> 600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <p>© Copyright 2025. Manage Inc. all Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/carousel.js?v=<?php echo time(); ?>"></script>
    <script src="js/mobile-menu.js?v=<?php echo time(); ?>"></script>
    <script>
        // Ensure mobile menu is working
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.toggle('active');
                    document.getElementById('mainNav').classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
