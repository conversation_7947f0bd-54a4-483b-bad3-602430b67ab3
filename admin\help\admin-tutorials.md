# Manage Inc Admin Panel Tutorials

## Step-by-Step Tutorials

### Creating and Publishing a News Post

1. **Access the News Creation Page**
   - Log in to the admin panel
   - Navigate to **News > Add News** in the sidebar menu

2. **Enter Basic Information**
   - Enter a descriptive title for your news post
   - Select an appropriate category from the dropdown menu
   - If needed, create a new category by clicking the "+" icon

3. **Add Content Using the WYSIWYG Editor**
   - Use the formatting toolbar to style your text
   - Add headings, lists, and links as needed
   - Insert images by clicking the image icon and uploading or selecting from library
   - Format text using the font selector for consistent styling

4. **Upload a Featured Image**
   - Click the "Choose File" button in the Featured Image section
   - Select an image from your computer (recommended size: 1200x800px)
   - The image will be displayed as a thumbnail once uploaded

5. **Set SEO-Friendly URL**
   - The slug field will auto-generate based on your title
   - Edit the slug if needed for better SEO (use hyphens between words)
   - Avoid special characters and keep it concise

6. **Review and Publish**
   - Preview your post by clicking the "Preview" button
   - Make any necessary adjustments
   - Click "Save" to publish the post immediately

### Managing User Accounts and Permissions

1. **Creating a New User**
   - Navigate to the **Users** section in the sidebar
   - Click the "Add New User" button
   - Fill in the required fields (username, email, password)
   - Select the appropriate role based on needed permissions
   - Click "Create User" to add the account

2. **Editing User Permissions**
   - Find the user in the users list
   - Click the "Edit" button next to their name
   - Change their role to adjust permissions:
     - **Administrator**: Full access to all features
     - **Editor**: Can manage content but not system settings
     - **Viewer**: Read-only access to dashboard and content
   - Save changes to apply new permissions

3. **Resetting a User's Password**
   - Go to the Users section
   - Find the user and click "Edit"
   - Check the "Change Password" box
   - Enter and confirm the new password
   - Click "Update User" to save changes

4. **Deactivating a User Account**
   - Navigate to the Users section
   - Find the user you want to deactivate
   - Click the "Delete" button (this will remove their access)
   - Confirm the deletion when prompted

### Customizing Email Templates

1. **Accessing Email Templates**
   - Go to **Settings** in the sidebar
   - Select the "Email" tab
   - Scroll down to the "Email Templates" section

2. **Editing a Template**
   - Find the template you want to modify
   - Click the "Edit" button to open the template editor
   - The template will open in a modal window

3. **Customizing the Content**
   - Modify the subject line as needed
   - Edit the email body using the WYSIWYG editor
   - Use template variables to include dynamic content:
     - `{contact_name}`: The recipient's name
     - `{company_name}`: Your company name
     - `{contact_message}`: The original message content
     - See the variables dropdown for all available options

4. **Previewing Your Changes**
   - Click the "Preview" tab to see how your email will look
   - Check that all variables are properly placed
   - Ensure formatting looks correct

5. **Saving the Template**
   - Click "Save Changes" to update the template
   - The new template will be used for all future emails

### Responding to Contact Form Submissions

1. **Checking Your Inbox**
   - Click on **Inbox** in the sidebar menu
   - New/unread messages will be highlighted
   - The number of unread messages appears as a badge on the Inbox icon

2. **Reading a Message**
   - Click on a message to view its full content
   - The message will be marked as read automatically
   - You'll see the sender's information and complete message

3. **Replying to a Message**
   - While viewing a message, click the "Reply" button
   - Choose a template or start with a blank message
   - Customize your response as needed
   - Click "Send" to deliver your reply

4. **Managing Multiple Messages**
   - Use the checkbox column to select multiple messages
   - Use the bulk actions dropdown to mark as read/unread or delete
   - Filter messages by date or status using the filter options

### Editing Website Content with the Frontend Editor

1. **Accessing the Frontend Editor**
   - Click on **Frontend Editor** in the sidebar
   - Navigate to the directory containing the file you want to edit
   - Select a file from the list to open it in the editor

2. **Understanding the Editor Interface**
   - The top section shows the file path and navigation
   - The main area contains the WYSIWYG editor for HTML files
   - For other file types, a code editor will be displayed
   - The bottom section has save and view options

3. **Making Content Changes**
   - For HTML files, use the WYSIWYG tools to format text
   - Add or modify images, links, and other elements
   - For code files, edit directly in the code editor
   - Use the "View in Browser" button to preview changes

4. **Saving Your Changes**
   - Add an optional version comment to track your changes
   - Click "Save Changes" to update the file
   - The system will create a backup version automatically

5. **Viewing File History**
   - Click the "Versions" button to see previous versions
   - Compare changes between versions
   - Restore a previous version if needed

### Configuring System Settings

1. **Accessing Settings**
   - Click on **Settings** in the sidebar menu
   - Navigate between tabs to access different setting categories

2. **Updating General Settings**
   - Set your admin email address
   - Configure timezone and date format
   - Save changes by clicking "Save Settings"

3. **Configuring Email Settings**
   - Choose between PHP mail() or SMTP
   - If using SMTP, enter your server details:
     - SMTP Host (e.g., smtp.gmail.com)
     - SMTP Port (typically 587 for TLS)
     - Username and password
     - Encryption type (TLS/SSL)
   - Set default "From" name and email
   - Configure Reply-To address
   - Test settings by sending a test email

4. **Customizing Appearance**
   - Set primary and secondary colors
   - Enable or disable dark mode
   - Save changes to update the admin interface

## Frequently Asked Questions

### General Questions

**Q: How do I reset my admin password?**  
A: Use the "Forgot Password" link on the login page. You'll receive an email with instructions to reset your password. If you don't have access to your email, contact your system administrator.

**Q: Can multiple users edit the same content simultaneously?**  
A: The system has collaborative editing protection. If someone is editing a file, others will see it's locked. This prevents conflicting changes.

**Q: How do I know if there are new contact submissions?**  
A: New messages are indicated by a notification badge on the Inbox icon in the sidebar. You'll also see notifications in the admin header.

### Content Management

**Q: Why can't I edit certain files in the Frontend Editor?**  
A: Some files (like header.html and footer.html) are protected to prevent accidental changes that could break the site structure. These files require direct server access to modify.

**Q: How do I create a new page for my website?**  
A: Create a new HTML file in the Pages directory using the Frontend Editor. Make sure to include proper header and footer references.

**Q: Can I schedule news posts to publish later?**  
A: The current version doesn't support scheduled publishing. All posts are published immediately when saved.

### User Management

**Q: What's the difference between Administrator and Editor roles?**  
A: Administrators have full access to all features, including user management and system settings. Editors can manage content (news, pages) but cannot change system settings or manage users.

**Q: Can I create custom user roles with specific permissions?**  
A: The current version has three predefined roles (Administrator, Editor, Viewer). Custom roles are not supported in this version.

**Q: How do I change my profile picture?**  
A: Go to your profile page by clicking your username in the top-right corner and selecting "Profile." Then upload a new image in the profile section.

### Technical Issues

**Q: Why aren't emails being sent from the contact form?**  
A: Check your email settings in the Settings > Email tab. Ensure SMTP credentials are correct if using SMTP, or that your server supports PHP mail() if using that option.

**Q: How do I increase the maximum upload file size?**  
A: This is controlled by your server configuration. Contact your hosting provider to increase the PHP upload limits if needed.

**Q: What should I do if I get a "500 Internal Server Error"?**  
A: Check your server's error logs for specific details. Common causes include incorrect file permissions, PHP errors, or server configuration issues.

### Best Practices

**Q: How often should I back up my website content?**  
A: Regular backups are recommended, especially before making significant changes. Weekly backups are a good minimum frequency.

**Q: What image sizes work best for news posts?**  
A: Featured images should be 1200x800 pixels for optimal display across devices. Always compress images before uploading to improve page load times.

**Q: How can I improve my website's SEO?**  
A: Use descriptive titles and slugs for news posts, include relevant keywords in your content, and ensure all images have appropriate alt text.
