/**
 * Modern Inbox Page Styles
 * Complete overhaul of the contact submissions page
 */

/* Inbox Wrapper */
.inbox-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Inbox Toolbar */
.inbox-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.inbox-filters {
    display: flex;
    align-items: center;
    flex: 1;
}

.inbox-filter-form {
    display: flex;
    gap: 15px;
    width: 100%;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    min-width: 150px;
}

.filter-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-group select:hover {
    border-color: #ccc;
}

.filter-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
    outline: none;
}

.search-group {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.search-group input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    transition: all 0.2s;
}

.search-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
    outline: none;
}

.search-btn {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: none;
    border: none;
    padding: 0 15px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.search-btn:hover {
    color: var(--primary-color);
}

.clear-search {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.clear-search:hover {
    color: var(--danger-color);
}

.inbox-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

#bulk-action {
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
}

#bulk-action:focus {
    border-color: var(--primary-color);
    outline: none;
}

#apply-bulk-action {
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s;
}

#apply-bulk-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.per-page-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.per-page-selector label {
    font-size: 14px;
    color: #666;
}

.per-page-selector select {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
}

.per-page-selector select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Messages Table */
.inbox-messages {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.messages-table-container {
    width: 100%;
    overflow-x: auto;
}

.messages-table {
    width: 100%;
    border-collapse: collapse;
}

.messages-table th {
    background-color: #f5f7fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.messages-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.message-row {
    transition: background-color 0.2s;
}

.message-row:hover {
    background-color: #f9f9f9;
}

.message-row.unread {
    background-color: rgba(var(--primary-rgb), 0.05);
    font-weight: 500;
}

.message-row.unread:hover {
    background-color: rgba(var(--primary-rgb), 0.08);
}

.checkbox-column {
    width: 40px;
    text-align: center;
}

.checkbox-column input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.name-column {
    min-width: 150px;
    font-weight: 500;
}

.email-column {
    min-width: 200px;
    color: #666;
}

.source-column {
    min-width: 120px;
    color: #666;
}

.date-column {
    min-width: 180px;
    color: #666;
    white-space: nowrap;
}

.status-column {
    min-width: 100px;
}

.actions-column {
    min-width: 120px;
    text-align: right;
}

.message-link {
    color: var(--primary-color);
    text-decoration: none;
    display: block;
    transition: color 0.2s;
}

.message-link:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.status-badge.read {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-badge.unread {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

.message-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background-color: #f5f5f5;
    color: #555;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: #e0e0e0;
}

.view-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.mark-btn:hover {
    background-color: #f57c00;
    color: white;
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* Empty Inbox */
.empty-inbox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-inbox-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-inbox h2 {
    margin-bottom: 10px;
    color: #333;
    font-size: 24px;
}

.empty-inbox p {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
}

/* Message View Layout */
.message-view-layout {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message-view-header {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.message-view-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.message-action-buttons {
    display: flex;
    gap: 10px;
}

.message-view-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.message-view-sidebar {
    flex: 1;
    min-width: 300px;
    max-width: 350px;
}

.message-view-content {
    flex: 3;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Contact Card */
.message-contact-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.contact-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eee;
}

.contact-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.contact-name h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    color: #333;
}

.contact-source {
    color: #666;
    font-size: 14px;
}

.contact-details {
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.contact-detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.contact-detail-item:last-child {
    margin-bottom: 0;
}

.contact-detail-item i {
    width: 20px;
    color: #666;
}

.contact-detail-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s;
}

.contact-detail-item a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.contact-actions {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Message Card */
.message-card, .reply-card, .reply-history-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.message-card-header, .reply-card-header, .reply-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eee;
}

.message-card-header h3, .reply-card-header h3, .reply-history-header h3 {
    margin: 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
}

.message-date {
    color: #666;
    font-size: 14px;
}

.message-card-body, .reply-card-body, .reply-history-body {
    padding: 20px;
}

.message-text {
    line-height: 1.6;
    white-space: pre-wrap;
    color: #333;
}

/* Reply Form */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    transition: all 0.2s;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
    outline: none;
}

.reply-toolbar {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.reply-toolbar-btn {
    background: none;
    border: none;
    padding: 6px 12px;
    cursor: pointer;
    border-radius: 4px;
    color: #555;
    transition: all 0.2s;
}

.reply-toolbar-btn:hover {
    background-color: #e0e0e0;
    color: #333;
}

.reply-toolbar-btn.active {
    background-color: #e0e0e0;
    color: var(--primary-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Reply History */
.reply-count {
    background-color: #f0f0f0;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 12px;
    color: #666;
}

.reply-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.reply-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.reply-item-header {
    margin-bottom: 15px;
}

.reply-user-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.reply-user-name {
    font-weight: 600;
    color: #333;
}

.reply-date {
    color: #666;
    font-size: 14px;
}

.reply-subject {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    font-size: 16px;
}

.reply-item-content {
    line-height: 1.6;
    white-space: pre-wrap;
    color: #333;
}

/* Preview Modal */
.admin-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.admin-modal-content {
    background-color: #fff;
    margin: 50px auto;
    width: 90%;
    max-width: 700px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.admin-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.admin-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.admin-modal-close:hover {
    color: var(--danger-color);
}

.admin-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.preview-subject {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.preview-message {
    line-height: 1.6;
}

.admin-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    background-color: #f5f7fa;
    border-top: 1px solid #eee;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* Error View */
.message-error-view {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 40px 20px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.error-icon {
    font-size: 48px;
    color: var(--danger-color);
    margin-bottom: 20px;
}

.message-error-view h2 {
    margin-bottom: 15px;
    color: #333;
    font-size: 24px;
}

.message-error-view p {
    color: #666;
    margin-bottom: 25px;
    font-size: 16px;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .message-view-container {
        flex-direction: column;
    }

    .message-view-sidebar {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .inbox-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .inbox-filters, .inbox-actions {
        width: 100%;
    }

    .inbox-filter-form {
        flex-direction: column;
    }

    .filter-group, .search-group {
        width: 100%;
    }

    .inbox-actions {
        flex-direction: column;
    }

    .message-view-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .message-action-buttons {
        flex-direction: column;
    }

    .email-column, .source-column {
        display: none;
    }

    .admin-modal-content {
        width: 95%;
        margin: 20px auto;
    }
}
