<?php
/**
 * Email Helper Functions
 *
 * This file contains functions for sending emails using either PHP mail or SMTP.
 */

// Disable error reporting for production
error_reporting(0);
ini_set('display_errors', 0);

// Use centralized PHPMailer loader
require_once __DIR__ . '/../lib/PHPMailerLoader.php';
$phpmailer_available = PHPMailerLoader::isAvailable();

// Log the PHPMailer availability
error_log('PHPMailer availability: ' . ($phpmailer_available ? 'Available' : 'Not available'));

/**
 * Send an email using the configured email settings
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message body
 * @param array $settings Email settings from database
 * @param array $attachments Optional array of attachments
 * @return array Result with success status and error message if applicable
 */
function sendEmail($to, $subject, $message, $settings = null, $attachments = []) {
    // If settings not provided, get from database
    if ($settings === null) {
        global $conn;
        if (!isset($conn)) {
            require_once __DIR__ . '/db_connect.php';
        }

        $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'email'";
        $result = $conn->query($sql);

        $settings = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
    }

    // Check if SMTP is enabled
    $use_smtp = isset($settings['use_smtp']) && $settings['use_smtp'] == '1';

    // Log the email delivery method
    error_log('Email delivery method: ' . ($use_smtp ? 'SMTP' : 'PHP mail()'));

    // We'll try to use PHPMailer for all emails, but with different transport methods
    // This ensures consistent behavior and better error handling

    // Check if PHPMailer is available
    $phpmailer_available = class_exists('PHPMailer\\PHPMailer\\PHPMailer');

    // PHPMailer availability is already determined by the centralized loader

    // If PHPMailer is not available, return an error
    if (!$phpmailer_available) {
        error_log('PHPMailer not available. Cannot send email.');
        return [
            'success' => false,
            'error' => 'PHPMailer library is required for sending emails but is not available. Please check your installation.'
        ];
    }

    // Use PHPMailer for all email sending (with or without SMTP)
    error_log('Using PHPMailer for email delivery (SMTP ' . ($use_smtp ? 'enabled' : 'disabled') . ')');
    try {
        // Create PHPMailer instance using centralized loader
        $mail = PHPMailerLoader::createInstance(true);

        if (!$mail) {
            throw new Exception('Failed to create PHPMailer instance');
        }

        error_log('PHPMailer instance created successfully');

        // Set timeout to avoid long waits
        $mail->Timeout = 30;

        // Enable debug mode for troubleshooting in development
        // Set debug level directly - works with simplified PHPMailer
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            $mail->SMTPDebug = 2; // Server connection and responses
        } else {
            $mail->SMTPDebug = 0; // No debug output
        }

        // Suppress deprecation warnings for email sending
        $original_error_level = error_reporting();
        error_reporting($original_error_level & ~E_DEPRECATED);

        // Configure mail transport based on settings
        if ($use_smtp) {
            // Use SMTP transport
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'] ?? 'localhost';
            $mail->Port = $settings['smtp_port'] ?? 25;

            // Set longer timeout for testing in various hosting environments
            $mail->Timeout = 30; // 30 seconds timeout
            $mail->SMTPKeepAlive = true; // Keep connection alive for multiple emails

            // Add connection options to handle various hosting environments
            $mail->SMTPOptions = [
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ];

            // Log SMTP settings for debugging
            error_log("SMTP Host: " . $mail->Host);
            error_log("SMTP Port: " . $mail->Port);
            error_log("SMTP Timeout: " . $mail->Timeout);

            // Gmail-specific settings
            if (stripos($mail->Host, 'gmail.com') !== false) {
                error_log("Gmail SMTP detected. Applying Gmail-specific settings.");

                // Gmail requires these settings
                $mail->SMTPAuth = true;
                $mail->SMTPKeepAlive = true; // SMTP connection will not close after each email sent

                // If port is 587, force TLS
                if ($mail->Port == 587 && empty($settings['smtp_security'])) {
                    $mail->SMTPSecure = 'tls';
                    error_log("Gmail on port 587: Forcing TLS security");
                }
            }

            // Log additional debug info for troubleshooting
            error_log("PHP Version: " . PHP_VERSION);
            error_log("Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'));
            error_log("Operating System: " . PHP_OS);

            if (!empty($settings['smtp_username'])) {
                $mail->SMTPAuth = true;
                $mail->Username = $settings['smtp_username'];
                $mail->Password = $settings['smtp_password'] ?? '';
                error_log("SMTP Auth: Enabled with username " . $mail->Username);
            } else {
                error_log("SMTP Auth: Disabled (no username provided)");
            }

            if (!empty($settings['smtp_security'])) {
                if ($settings['smtp_security'] === 'tls') {
                    $mail->SMTPSecure = 'tls';
                    error_log("SMTP Security: TLS");
                } elseif ($settings['smtp_security'] === 'ssl') {
                    $mail->SMTPSecure = 'ssl';
                    error_log("SMTP Security: SSL");
                }
            } else {
                $mail->SMTPAutoTLS = false;
                error_log("SMTP Security: None");
            }
        } else {
            // Use PHP's mail() function as transport
            // Older versions of PHPMailer don't have isMail() method
            $mail->Mailer = 'mail';
            error_log("Using PHPMailer with mail() transport");

            // Add additional debugging for mail() transport
            error_log("PHP mail() configuration:");
            error_log("sendmail_path: " . ini_get('sendmail_path'));
            error_log("SMTP: " . ini_get('SMTP'));
            error_log("smtp_port: " . ini_get('smtp_port'));

            // On Windows, mail() often doesn't work without SMTP configuration
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                error_log("WARNING: Running on Windows. PHP mail() function may not work without proper SMTP configuration in php.ini");
            }
        }

        // Debug mode is already set above

        // Sender
        $from_email = $settings['from_email'] ?? '<EMAIL>';
        $from_name = $settings['from_name'] ?? 'System Mailer';
        $mail->setFrom($from_email, $from_name);

        // Reply to
        if (!empty($settings['reply_to'])) {
            $mail->addReplyTo($settings['reply_to'], $from_name);
        }

        // Recipients
        $mail->addAddress($to);

        // Content
        $mail->isHTML(false); // Set to true if message is HTML
        $mail->Subject = $subject;
        $mail->Body = $message;

        // Attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (is_array($attachment) && isset($attachment['path']) && isset($attachment['name'])) {
                    $mail->addAttachment($attachment['path'], $attachment['name']);
                } elseif (is_string($attachment) && file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // Send the email
        $mail->send();
        return ['success' => true];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $mail->ErrorInfo
        ];
    } catch (\Error $e) {
        // Catch PHP 7+ errors
        return [
            'success' => false,
            'error' => 'PHP Error: ' . $e->getMessage()
        ];
    } catch (\Throwable $e) {
        // Catch any other throwable
        return [
            'success' => false,
            'error' => 'Unexpected error: ' . $e->getMessage()
        ];
    }
}
