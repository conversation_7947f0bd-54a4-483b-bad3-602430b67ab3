<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center | Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #f1ca2f;
            --primary-dark: #e0b929;
            --secondary: #333;
            --text: #333;
            --text-light: #666;
            --bg: #f8f9fa;
            --bg-card: #fff;
            --border: #e9ecef;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --radius: 8px;
            --transition: all 0.3s ease;
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text);
            background-color: var(--bg);
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .help-sidebar {
            width: var(--sidebar-width);
            background: var(--bg-card);
            box-shadow: var(--shadow);
            padding: 20px;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: var(--transition);
        }

        .help-sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 20px 0;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar-header h2 i {
            color: var(--primary);
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 5px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            color: var(--text);
            text-decoration: none;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: rgba(241, 202, 47, 0.1);
            color: var(--primary-dark);
        }

        .sidebar-nav a i {
            width: 20px;
            text-align: center;
            color: var(--primary);
        }

        .sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary);
            color: var(--secondary);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .sidebar-toggle:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            flex: 1;
            transition: var(--transition);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        .content-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border-radius: var(--radius);
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            color: var(--secondary);
        }

        .content-header h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .content-header p {
            font-size: 18px;
            opacity: 0.8;
        }

        /* Content Sections */
        .content-section {
            background: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            padding: 25px 30px;
            background: rgba(241, 202, 47, 0.05);
            border-bottom: 1px solid var(--border);
        }

        .section-header h2 {
            font-size: 24px;
            font-weight: 600;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .section-header h2 i {
            color: var(--primary);
        }

        .section-header p {
            color: var(--text-light);
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        /* Feature Cards */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: var(--bg);
            border-radius: var(--radius);
            padding: 25px;
            border: 1px solid var(--border);
            transition: var(--transition);
        }

        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: var(--primary);
        }

        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: rgba(241, 202, 47, 0.15);
            color: var(--primary);
            border-radius: 10px;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--secondary);
        }

        .feature-desc {
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.6;
        }

        /* Screenshot Mockups */
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .screenshot-card {
            background: var(--bg);
            border-radius: var(--radius);
            padding: 15px;
            border: 1px solid var(--border);
            text-align: center;
        }

        .screenshot-mockup {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        .screenshot-mockup.dashboard {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .screenshot-mockup.editor {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .screenshot-mockup.inbox {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .screenshot-mockup.users {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .screenshot-mockup.settings {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .screenshot-title {
            font-weight: 600;
            color: var(--secondary);
            margin-bottom: 5px;
        }

        .screenshot-desc {
            font-size: 12px;
            color: var(--text-light);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            background: var(--primary);
            color: var(--secondary);
            text-decoration: none;
            border-radius: var(--radius);
            font-weight: 500;
            transition: var(--transition);
            text-align: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .action-btn i {
            font-size: 16px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .help-sidebar {
                transform: translateX(-100%);
            }

            .help-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: flex;
            }

            .content-header h1 {
                font-size: 28px;
            }

            .content-header p {
                font-size: 16px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .screenshot-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 769px) {
            .sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Help Sidebar -->
    <aside class="help-sidebar" id="helpSidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-question-circle"></i> Help Center</h2>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview" class="active"><i class="fas fa-home"></i> Overview</a></li>
                <li><a href="#dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="#content"><i class="fas fa-newspaper"></i> Content Management</a></li>
                <li><a href="#inbox"><i class="fas fa-inbox"></i> Inbox & Messages</a></li>
                <li><a href="#users"><i class="fas fa-users"></i> User Management</a></li>
                <li><a href="#settings"><i class="fas fa-cog"></i> System Settings</a></li>
                <li><a href="#activity"><i class="fas fa-history"></i> Activity Log</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-tools"></i> Troubleshooting</a></li>
                <li><a href="#support"><i class="fas fa-life-ring"></i> Support</a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Header -->
        <div class="content-header">
            <h1><i class="fas fa-question-circle"></i> Admin Panel Help Center</h1>
            <p>Complete guide to using your content management system</p>
        </div>

        <!-- Overview Section -->
        <section class="content-section" id="overview">
            <div class="section-header">
                <h2><i class="fas fa-home"></i> System Overview</h2>
                <p>Welcome to your admin panel - your central hub for managing your website</p>
            </div>
            <div class="section-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3 class="feature-title">Dashboard</h3>
                        <p class="feature-desc">Monitor your website's performance with real-time analytics and quick action cards.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="feature-title">Content Management</h3>
                        <p class="feature-desc">Create and edit content with our professional WYSIWYG editor featuring 20+ tools.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h3 class="feature-title">Inbox</h3>
                        <p class="feature-desc">Manage contact form submissions and communications in one centralized location.</p>
                    </div>
                </div>
                <div class="quick-actions">
                    <a href="dashboard.php" class="action-btn">
                        <i class="fas fa-tachometer-alt"></i>
                        Go to Dashboard
                    </a>
                    <a href="create_news.php" class="action-btn">
                        <i class="fas fa-plus"></i>
                        Create Content
                    </a>
                    <a href="inbox.php" class="action-btn">
                        <i class="fas fa-inbox"></i>
                        Check Inbox
                    </a>
                </div>
            </div>
        </section>

        <!-- Dashboard Section -->
        <section class="content-section" id="dashboard">
            <div class="section-header">
                <h2><i class="fas fa-tachometer-alt"></i> Dashboard Features</h2>
                <p>Your central command center for monitoring and managing your website</p>
            </div>
            <div class="section-content">
                <div class="screenshot-grid">
                    <div class="screenshot-card">
                        <div class="screenshot-mockup dashboard">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="screenshot-title">Analytics Overview</div>
                        <div class="screenshot-desc">Real-time visitor statistics and performance metrics</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup dashboard">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="screenshot-title">Quick Actions</div>
                        <div class="screenshot-desc">One-click access to common administrative tasks</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup dashboard">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="screenshot-title">Recent Activity</div>
                        <div class="screenshot-desc">Timeline of recent system events and user actions</div>
                    </div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">Real-time Statistics</h3>
                        <p class="feature-desc">Monitor visitor counts, page views, and system performance in real-time.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="feature-title">Quick Actions</h3>
                        <p class="feature-desc">Access frequently used features with one-click action cards.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Management Section -->
        <section class="content-section" id="content">
            <div class="section-header">
                <h2><i class="fas fa-newspaper"></i> Content Management</h2>
                <p>Professional content creation and editing tools</p>
            </div>
            <div class="section-content">
                <div class="screenshot-grid">
                    <div class="screenshot-card">
                        <div class="screenshot-mockup editor">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="screenshot-title">WYSIWYG Editor</div>
                        <div class="screenshot-desc">Professional editor with 20+ formatting tools</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup editor">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="screenshot-title">HTML Code View</div>
                        <div class="screenshot-desc">Switch between visual and code editing modes</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup editor">
                            <i class="fas fa-expand"></i>
                        </div>
                        <div class="screenshot-title">Fullscreen Mode</div>
                        <div class="screenshot-desc">Distraction-free editing experience</div>
                    </div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h3 class="feature-title">Rich Text Formatting</h3>
                        <p class="feature-desc">Bold, italic, colors, fonts, lists, tables, and more formatting options.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-save"></i>
                        </div>
                        <h3 class="feature-title">Auto-save</h3>
                        <p class="feature-desc">Never lose your work with automatic saving and version control.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">Mobile Responsive</h3>
                        <p class="feature-desc">Edit content on any device with our responsive interface.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Inbox Section -->
        <section class="content-section" id="inbox">
            <div class="section-header">
                <h2><i class="fas fa-inbox"></i> Inbox & Messages</h2>
                <p>Centralized communication management</p>
            </div>
            <div class="section-content">
                <div class="screenshot-grid">
                    <div class="screenshot-card">
                        <div class="screenshot-mockup inbox">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="screenshot-title">Message List</div>
                        <div class="screenshot-desc">View all contact form submissions in one place</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup inbox">
                            <i class="fas fa-reply"></i>
                        </div>
                        <div class="screenshot-title">Quick Reply</div>
                        <div class="screenshot-desc">Respond to messages directly from the admin panel</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup inbox">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="screenshot-title">Smart Filtering</div>
                        <div class="screenshot-desc">Filter messages by read status, date, or sender</div>
                    </div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                        <h3 class="feature-title">Message Management</h3>
                        <p class="feature-desc">Mark as read/unread, delete, and organize your messages efficiently.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="feature-title">Search & Filter</h3>
                        <p class="feature-desc">Find specific messages quickly with powerful search and filtering tools.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Management Section -->
        <section class="content-section" id="users">
            <div class="section-header">
                <h2><i class="fas fa-users"></i> User Management</h2>
                <p>Manage user accounts, roles, and permissions</p>
            </div>
            <div class="section-content">
                <div class="screenshot-grid">
                    <div class="screenshot-card">
                        <div class="screenshot-mockup users">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="screenshot-title">User Creation</div>
                        <div class="screenshot-desc">Create new user accounts with role assignments</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup users">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="screenshot-title">Role Management</div>
                        <div class="screenshot-desc">Assign roles and manage user permissions</div>
                    </div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h3 class="feature-title">Permission System</h3>
                        <p class="feature-desc">Role-based access control with granular permissions for different user types.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <h3 class="feature-title">Profile Management</h3>
                        <p class="feature-desc">Edit user profiles, upload profile pictures, and manage account settings.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section class="content-section" id="settings">
            <div class="section-header">
                <h2><i class="fas fa-cog"></i> System Settings</h2>
                <p>Configure your website and system preferences</p>
            </div>
            <div class="section-content">
                <div class="screenshot-grid">
                    <div class="screenshot-card">
                        <div class="screenshot-mockup settings">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="screenshot-title">General Settings</div>
                        <div class="screenshot-desc">Site name, description, and basic configuration</div>
                    </div>
                    <div class="screenshot-card">
                        <div class="screenshot-mockup settings">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="screenshot-title">Email Configuration</div>
                        <div class="screenshot-desc">SMTP settings and email template management</div>
                    </div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="feature-title">System Configuration</h3>
                        <p class="feature-desc">Configure system-wide settings, maintenance mode, and performance options.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">Security Settings</h3>
                        <p class="feature-desc">Manage security policies, password requirements, and access controls.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Activity Log Section -->
        <section class="content-section" id="activity">
            <div class="section-header">
                <h2><i class="fas fa-history"></i> Activity Log</h2>
                <p>Monitor user activities and system events</p>
            </div>
            <div class="section-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <h3 class="feature-title">Comprehensive Logging</h3>
                        <p class="feature-desc">Track all user actions, system events, and administrative changes with detailed logs.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="feature-title">Advanced Filtering</h3>
                        <p class="feature-desc">Filter logs by user, action type, date range, and other criteria for easy analysis.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Support Section -->
        <section class="content-section" id="support">
            <div class="section-header">
                <h2><i class="fas fa-life-ring"></i> Support & Resources</h2>
                <p>Get help and additional resources</p>
            </div>
            <div class="section-content">
                <div class="quick-actions">
                    <a href="dashboard.php" class="action-btn">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                    <a href="settings.php" class="action-btn">
                        <i class="fas fa-cog"></i>
                        System Settings
                    </a>
                    <a href="activity_log.php" class="action-btn">
                        <i class="fas fa-history"></i>
                        View Activity Log
                    </a>
                    <a href="profile.php" class="action-btn">
                        <i class="fas fa-user"></i>
                        Edit Profile
                    </a>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('helpSidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }

        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.sidebar-nav a');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Get target section
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        // Smooth scroll to section
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Update active navigation based on scroll position
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.content-section');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === '#' + sectionId) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>