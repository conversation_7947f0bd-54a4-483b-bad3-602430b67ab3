<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center | Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #f1ca2f;
            --primary-dark: #e0b929;
            --secondary: #333;
            --text: #333;
            --text-light: #666;
            --bg: #f8f9fa;
            --bg-card: #fff;
            --border: #e9ecef;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text);
            background-color: var(--bg);
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border-radius: var(--radius);
            color: var(--secondary);
        }

        .header h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.8;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 30px;
            transition: var(--transition);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: rgba(241, 202, 47, 0.15);
            color: var(--primary);
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--secondary);
        }

        .feature-desc {
            color: var(--text-light);
            margin-bottom: 20px;
            line-height: 1.7;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            color: var(--text);
        }

        .feature-list li i {
            color: var(--primary);
            font-size: 14px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: var(--primary);
            color: var(--secondary);
            text-decoration: none;
            border-radius: var(--radius);
            font-weight: 500;
            transition: var(--transition);
        }

        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .quick-links {
            background: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 30px;
            margin-bottom: 40px;
        }

        .quick-links h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-links h2 i {
            color: var(--primary);
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .link-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: var(--bg);
            border-radius: var(--radius);
            text-decoration: none;
            color: var(--text);
            transition: var(--transition);
        }

        .link-item:hover {
            background: rgba(241, 202, 47, 0.1);
            color: var(--primary-dark);
        }

        .link-item i {
            font-size: 18px;
            color: var(--primary);
            width: 24px;
            text-align: center;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-top: 40px;
        }

        .footer p {
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .footer .btn {
            margin: 0 10px;
        }

        @media (max-width: 768px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .links-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 28px;
            }
            
            .header p {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-question-circle"></i> Admin Panel Help Center</h1>
            <p>Complete guide to using your content management system</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3 class="feature-title">Dashboard Overview</h3>
                <p class="feature-desc">Your central hub for monitoring website activity and quick actions.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Real-time statistics and analytics</li>
                    <li><i class="fas fa-check"></i> Quick action cards for common tasks</li>
                    <li><i class="fas fa-check"></i> Recent activity timeline</li>
                    <li><i class="fas fa-check"></i> System status indicators</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h3 class="feature-title">Content Management</h3>
                <p class="feature-desc">Create, edit, and manage your website content with our enhanced editor.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Professional WYSIWYG editor with 20+ tools</li>
                    <li><i class="fas fa-check"></i> Visual and HTML code editing modes</li>
                    <li><i class="fas fa-check"></i> Auto-save and version control</li>
                    <li><i class="fas fa-check"></i> Fullscreen editing experience</li>
                    <li><i class="fas fa-check"></i> Mobile-responsive interface</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <h3 class="feature-title">Inbox & Communications</h3>
                <p class="feature-desc">Manage contact form submissions and user communications.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Centralized message inbox</li>
                    <li><i class="fas fa-check"></i> Contact form submissions</li>
                    <li><i class="fas fa-check"></i> Email template management</li>
                    <li><i class="fas fa-check"></i> SMTP configuration support</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title">User Management</h3>
                <p class="feature-desc">Manage user accounts, permissions, and profile settings.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> User account creation and editing</li>
                    <li><i class="fas fa-check"></i> Role-based permissions system</li>
                    <li><i class="fas fa-check"></i> Profile picture uploads</li>
                    <li><i class="fas fa-check"></i> Password security management</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h3 class="feature-title">System Settings</h3>
                <p class="feature-desc">Configure your website settings and preferences.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> General site configuration</li>
                    <li><i class="fas fa-check"></i> Email and SMTP settings</li>
                    <li><i class="fas fa-check"></i> Appearance customization</li>
                    <li><i class="fas fa-check"></i> Security configurations</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-history"></i>
                </div>
                <h3 class="feature-title">Activity Monitoring</h3>
                <p class="feature-desc">Track user activities and system events with detailed logging.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Comprehensive activity log</li>
                    <li><i class="fas fa-check"></i> User action tracking</li>
                    <li><i class="fas fa-check"></i> Timeline view of events</li>
                    <li><i class="fas fa-check"></i> Pagination for large datasets</li>
                </ul>
            </div>
        </div>

        <div class="quick-links">
            <h2><i class="fas fa-link"></i> Quick Access Links</h2>
            <div class="links-grid">
                <a href="dashboard.php" class="link-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="create_news.php" class="link-item">
                    <i class="fas fa-plus"></i>
                    <span>Create Content</span>
                </a>
                <a href="inbox.php" class="link-item">
                    <i class="fas fa-inbox"></i>
                    <span>Inbox</span>
                </a>
                <a href="users.php" class="link-item">
                    <i class="fas fa-users"></i>
                    <span>Manage Users</span>
                </a>
                <a href="settings.php" class="link-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <a href="activity_log.php" class="link-item">
                    <i class="fas fa-history"></i>
                    <span>Activity Log</span>
                </a>
                <a href="profile.php" class="link-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="user_settings.php" class="link-item">
                    <i class="fas fa-user-cog"></i>
                    <span>User Settings</span>
                </a>
            </div>
        </div>

        <div class="footer">
            <p>Need additional help? Contact your system administrator or check the documentation.</p>
            <a href="dashboard.php" class="btn">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
            <a href="settings.php" class="btn">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
        </div>
    </div>
</body>
</html>
