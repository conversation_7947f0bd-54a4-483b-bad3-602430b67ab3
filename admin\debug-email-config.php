<?php
/**
 * Debug Email Configuration
 * 
 * This script provides detailed debugging information about the email configuration
 */

session_start();
require_once 'config.php';
require_once 'lib/Mailer.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Email Configuration Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; margin: 10px 0; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; margin: 10px 0; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; margin: 10px 0; }
        .warning { color: orange; background: #fff8f0; padding: 10px; border: 1px solid orange; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Email Configuration Debug</h1>";

// Test 1: Check Mailer class instantiation
echo "<h2>1. Mailer Class Test</h2>";
try {
    $mailer = new Mailer($conn);
    echo "<div class='success'>✓ Mailer class instantiated successfully</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Failed to instantiate Mailer class: " . $e->getMessage() . "</div>";
    exit;
}

// Test 2: Get email settings
echo "<h2>2. Email Settings</h2>";
$settings = $mailer->getSettings();
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$required_settings = ['from_email', 'from_name'];
$smtp_settings = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption'];

foreach ($settings as $key => $value) {
    $display_value = ($key === 'smtp_password' && !empty($value)) ? '***hidden***' : $value;
    $status = '';
    
    if (in_array($key, $required_settings) && empty($value)) {
        $status = '<span style="color: red;">MISSING</span>';
    } elseif ($key === 'use_smtp' && $value) {
        $status = '<span style="color: green;">ENABLED</span>';
    } elseif ($key === 'use_smtp' && !$value) {
        $status = '<span style="color: orange;">DISABLED</span>';
    } elseif (in_array($key, $smtp_settings) && $settings['use_smtp'] && empty($value)) {
        $status = '<span style="color: red;">MISSING (SMTP ENABLED)</span>';
    } elseif (!empty($value)) {
        $status = '<span style="color: green;">OK</span>';
    }
    
    echo "<tr><td>$key</td><td>$display_value</td><td>$status</td></tr>";
}
echo "</table>";

// Test 3: Check database settings
echo "<h2>3. Database Settings Check</h2>";

// Check system_settings table
$email_keys = ['use_smtp', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_security', 'from_email', 'from_name', 'reply_to'];
$placeholders = str_repeat('?,', count($email_keys) - 1) . '?';
$sql = "SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ($placeholders)";
$stmt = $conn->prepare($sql);
$stmt->bind_param(str_repeat('s', count($email_keys)), ...$email_keys);
$stmt->execute();
$result = $stmt->get_result();

echo "<h3>System Settings Table:</h3>";
echo "<table>";
echo "<tr><th>Key</th><th>Value</th></tr>";
$found_settings = [];
while ($row = $result->fetch_assoc()) {
    $found_settings[$row['setting_key']] = $row['setting_value'];
    $display_value = ($row['setting_key'] === 'smtp_password' && !empty($row['setting_value'])) ? '***hidden***' : $row['setting_value'];
    echo "<tr><td>" . $row['setting_key'] . "</td><td>" . $display_value . "</td></tr>";
}
echo "</table>";

if (empty($found_settings)) {
    echo "<div class='error'>✗ No email settings found in system_settings table</div>";
} else {
    echo "<div class='success'>✓ Found " . count($found_settings) . " email settings in system_settings table</div>";
}

// Test 4: Test email sending
echo "<h2>4. Email Sending Test</h2>";

if (isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    $test_subject = "Email Configuration Test - " . date('Y-m-d H:i:s');
    $test_message = "
    <html>
    <body>
        <h2>Email Configuration Test</h2>
        <p>This is a test email to verify that your email configuration is working correctly.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>Sent at: " . date('Y-m-d H:i:s') . "</li>
            <li>From: " . $settings['from_name'] . " &lt;" . $settings['from_email'] . "&gt;</li>
            <li>Using SMTP: " . ($settings['use_smtp'] ? 'Yes' : 'No') . "</li>
        </ul>
        <p>If you received this email, your configuration is working correctly!</p>
    </body>
    </html>";
    
    echo "<div class='info'>Attempting to send test email to: $test_email</div>";
    
    // Enable error reporting for this test
    $old_error_reporting = error_reporting(E_ALL);
    $old_display_errors = ini_get('display_errors');
    ini_set('display_errors', 1);
    
    $result = $mailer->sendEmail($test_email, $test_subject, $test_message);
    
    // Restore error reporting
    error_reporting($old_error_reporting);
    ini_set('display_errors', $old_display_errors);
    
    if ($result['success']) {
        echo "<div class='success'>✓ Test email sent successfully!</div>";
    } else {
        echo "<div class='error'>✗ Failed to send test email: " . $result['message'] . "</div>";
    }
} else {
    echo "<form method='post'>";
    echo "<label for='test_email'>Email Address:</label><br>";
    echo "<input type='email' id='test_email' name='test_email' required style='width: 300px; padding: 5px; margin: 5px 0;'><br>";
    echo "<button type='submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>Send Test Email</button>";
    echo "</form>";
}

// Test 5: PHP Mail Configuration
echo "<h2>5. PHP Mail Configuration</h2>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>sendmail_path</td><td>" . ini_get('sendmail_path') . "</td></tr>";
echo "<tr><td>SMTP</td><td>" . ini_get('SMTP') . "</td></tr>";
echo "<tr><td>smtp_port</td><td>" . ini_get('smtp_port') . "</td></tr>";
echo "<tr><td>mail.add_x_header</td><td>" . ini_get('mail.add_x_header') . "</td></tr>";
echo "<tr><td>Operating System</td><td>" . PHP_OS . "</td></tr>";
echo "</table>";

if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "<div class='warning'>⚠ Running on Windows. PHP mail() function may not work without proper SMTP configuration in php.ini</div>";
}

// Test 6: PHPMailer Library Check
echo "<h2>6. PHPMailer Library Check</h2>";
$phpmailer_paths = [
    __DIR__ . '/lib/PHPMailer/PHPMailer.php',
    __DIR__ . '/lib/PHPMailer/real/PHPMailer-6.8.1/src/PHPMailer.php',
    $_SERVER['DOCUMENT_ROOT'] . '/vendor/phpmailer/phpmailer/src/PHPMailer.php'
];

$phpmailer_found = false;
foreach ($phpmailer_paths as $path) {
    if (file_exists($path)) {
        echo "<div class='success'>✓ PHPMailer found at: $path</div>";
        $phpmailer_found = true;
    } else {
        echo "<div class='info'>✗ PHPMailer not found at: $path</div>";
    }
}

if (!$phpmailer_found) {
    echo "<div class='error'>✗ PHPMailer library not found in any expected location</div>";
}

// Test 7: Contact Form Simulation
echo "<h2>7. Contact Form Simulation</h2>";

if (isset($_POST['test_contact'])) {
    echo "<div class='info'>Simulating contact form submission...</div>";
    
    // Simulate the contact form process
    $name = "Test User";
    $email = $_POST['contact_email'];
    $message = "This is a test message from the contact form debug script.";
    $source = "Debug Script";
    
    // Create HTML message like the contact form does
    $subject = "Contact Form Submission from " . $name;
    $htmlMessage = "
    <html>
    <head>
        <title>Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h2 { color: #3c3c45; border-bottom: 2px solid #f1ca2f; padding-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th { text-align: left; padding: 8px; background-color: #f2f2f2; }
            td { padding: 8px; border-top: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <div class='container'>
            <h2>New Contact Form Submission</h2>
            <table>
                <tr><th>Name:</th><td>$name</td></tr>
                <tr><th>Email:</th><td>$email</td></tr>
                <tr><th>Message:</th><td>$message</td></tr>
                <tr><th>Source:</th><td>$source</td></tr>
                <tr><th>Date:</th><td>" . date('Y-m-d H:i:s') . "</td></tr>
            </table>
        </div>
    </body>
    </html>";
    
    // Determine recipient
    $recipient = !empty($settings['reply_to']) ? $settings['reply_to'] : $settings['from_email'];
    
    echo "<div class='info'>Sending to: $recipient</div>";
    
    $result = $mailer->sendEmail($recipient, $subject, $htmlMessage);
    
    if ($result['success']) {
        echo "<div class='success'>✓ Contact form simulation email sent successfully!</div>";
    } else {
        echo "<div class='error'>✗ Contact form simulation failed: " . $result['message'] . "</div>";
    }
} else {
    echo "<form method='post'>";
    echo "<label for='contact_email'>Your Email Address:</label><br>";
    echo "<input type='email' id='contact_email' name='contact_email' required style='width: 300px; padding: 5px; margin: 5px 0;'><br>";
    echo "<button type='submit' name='test_contact' style='padding: 10px 20px; background: #28a745; color: white; border: none; cursor: pointer;'>Test Contact Form</button>";
    echo "</form>";
}

echo "<h2>Summary</h2>";
echo "<div class='info'>Check the error logs for detailed debugging information during email sending attempts.</div>";
echo "<p><a href='settings.php?category=email'>→ Go to Email Settings</a></p>";
echo "<p><a href='dashboard.php'>→ Return to Dashboard</a></p>";

echo "</body></html>";
?>
