/**
 * Admin Alerts CSS
 *
 * Modern, clean design for alert messages in the admin panel.
 */

/* Base Alert Styles */
.alert {
  position: relative;
  padding: var(--spacing-4) var(--spacing-5);
  margin-bottom: var(--spacing-5);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  overflow: hidden;
  line-height: 1.5;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  border-radius: 2px 0 0 2px;
}

.alert:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Alert Icon */
.alert-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

/* Alert Content */
.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

/* Alert Title */
.alert-title {
  font-weight: var(--font-weight-bold);
  margin: 0;
  font-size: var(--font-size-md);
  line-height: 1.4;
}

/* Alert Text */
.alert-text {
  margin: 0;
  opacity: 0.9;
}

/* Alert Link */
.alert-link {
  font-weight: var(--font-weight-semibold);
  text-decoration: underline;
  transition: opacity 0.2s ease;
}

.alert-link:hover {
  opacity: 0.8;
}

/* Alert Dismissible */
.alert-dismissible {
  padding-right: var(--spacing-12);
}

.alert-dismiss {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  appearance: none;
  font-size: var(--font-size-sm);
  line-height: 1;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s ease;
  color: inherit;
}

.alert-dismiss:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* Alert Variants */
.alert-primary {
  color: var(--white);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.alert-primary::before {
  background-color: var(--primary-light);
}

.alert-secondary {
  color: var(--white);
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
}

.alert-secondary::before {
  background-color: var(--secondary-light);
}

.alert-success {
  color: var(--white);
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
}

.alert-success::before {
  background-color: var(--success-light);
}

.alert-danger {
  color: var(--white);
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
}

.alert-danger::before {
  background-color: var(--danger-light);
}

.alert-warning {
  color: var(--white);
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
}

.alert-warning::before {
  background-color: var(--warning-light);
}

.alert-info {
  color: var(--white);
  background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
}

.alert-info::before {
  background-color: var(--info-light);
}

/* Subtle Alert Variants */
.alert-subtle {
  color: var(--text-dark);
  background-color: var(--white);
  border: 1px solid var(--border-color);
}

.alert-subtle.alert-primary::before {
  background-color: var(--primary-color);
}

.alert-subtle.alert-secondary::before {
  background-color: var(--secondary-color);
}

.alert-subtle.alert-success::before {
  background-color: var(--success-color);
}

.alert-subtle.alert-danger::before {
  background-color: var(--danger-color);
}

.alert-subtle.alert-warning::before {
  background-color: var(--warning-color);
}

.alert-subtle.alert-info::before {
  background-color: var(--info-color);
}

.alert-subtle .alert-icon {
  background-color: var(--background-light);
}

.alert-subtle.alert-primary .alert-icon,
.alert-subtle.alert-primary .alert-title {
  color: var(--primary-color);
}

.alert-subtle.alert-secondary .alert-icon,
.alert-subtle.alert-secondary .alert-title {
  color: var(--secondary-color);
}

.alert-subtle.alert-success .alert-icon,
.alert-subtle.alert-success .alert-title {
  color: var(--success-color);
}

.alert-subtle.alert-danger .alert-icon,
.alert-subtle.alert-danger .alert-title {
  color: var(--danger-color);
}

.alert-subtle.alert-warning .alert-icon,
.alert-subtle.alert-warning .alert-title {
  color: var(--warning-color);
}

.alert-subtle.alert-info .alert-icon,
.alert-subtle.alert-info .alert-title {
  color: var(--info-color);
}

/* Alert with Actions */
.alert-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
}

.alert-actions .btn {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.2);
  color: inherit;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-actions .btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.alert-subtle .alert-actions .btn {
  background-color: var(--background-light);
  color: var(--text-dark);
}

.alert-subtle .alert-actions .btn:hover {
  background-color: var(--background-hover);
}

/* Toast Alerts (Floating Notifications) */
.toast-container {
  position: fixed;
  top: var(--spacing-5);
  right: var(--spacing-5);
  z-index: var(--z-index-toast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  max-width: 400px;
  pointer-events: none;
}

.toast {
  width: 100%;
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  pointer-events: auto;
  animation: toast-in 0.5s forwards;
}

.toast.hide {
  animation: toast-out 0.5s forwards;
}

@keyframes toast-in {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes toast-out {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(30px);
  }
}

/* Alert Positions */
.alert-container {
  position: fixed;
  z-index: var(--z-index-toast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  max-width: 400px;
  pointer-events: none;
}

.alert-container .alert {
  pointer-events: auto;
}

.alert-container-top-right {
  top: var(--spacing-5);
  right: var(--spacing-5);
}

.alert-container-top-left {
  top: var(--spacing-5);
  left: var(--spacing-5);
}

.alert-container-bottom-right {
  bottom: var(--spacing-5);
  right: var(--spacing-5);
}

.alert-container-bottom-left {
  bottom: var(--spacing-5);
  left: var(--spacing-5);
}

.alert-container-top-center {
  top: var(--spacing-5);
  left: 50%;
  transform: translateX(-50%);
}

.alert-container-bottom-center {
  bottom: var(--spacing-5);
  left: 50%;
  transform: translateX(-50%);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .alert {
    padding: var(--spacing-3) var(--spacing-4);
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
  }

  .alert-icon {
    font-size: 1.25rem;
    width: 28px;
    height: 28px;
  }

  .toast-container,
  .alert-container {
    max-width: calc(100% - var(--spacing-6));
  }

  .alert-container-top-right,
  .alert-container-top-left,
  .alert-container-top-center {
    top: var(--spacing-3);
    left: var(--spacing-3);
    right: var(--spacing-3);
    transform: none;
  }

  .alert-container-bottom-right,
  .alert-container-bottom-left,
  .alert-container-bottom-center {
    bottom: var(--spacing-3);
    left: var(--spacing-3);
    right: var(--spacing-3);
    transform: none;
  }
}
