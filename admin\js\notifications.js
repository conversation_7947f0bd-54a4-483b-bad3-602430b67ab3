/**
 * Notifications handling for the admin panel
 */
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const notificationsToggle = document.getElementById('notificationsToggle');
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const notificationsList = document.getElementById('notificationList');
    const notificationsBadge = document.getElementById('notificationsBadge');
    const markAllReadBtn = document.getElementById('markAllRead');

    // Variables
    let isNotificationsOpen = false;
    let notifications = [];
    let unreadCount = 0;

    // Initialize
    loadNotifications();

    // Set up event listeners
    if (notificationsToggle) {
        notificationsToggle.addEventListener('click', toggleNotifications);
    }

    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', markAllAsRead);
    }

    // Close notifications when clicking outside
    document.addEventListener('click', function(event) {
        if (isNotificationsOpen &&
            !notificationsDropdown.contains(event.target) &&
            !notificationsToggle.contains(event.target)) {
            closeNotifications();
        }
    });

    /**
     * Toggle notifications dropdown
     */
    function toggleNotifications(event) {
        event.preventDefault();

        if (isNotificationsOpen) {
            closeNotifications();
        } else {
            openNotifications();
        }
    }

    /**
     * Open notifications dropdown
     */
    function openNotifications() {
        notificationsDropdown.classList.add('show');
        isNotificationsOpen = true;
        loadNotifications(); // Refresh notifications when opening
    }

    /**
     * Close notifications dropdown
     */
    function closeNotifications() {
        notificationsDropdown.classList.remove('show');
        isNotificationsOpen = false;
    }

    /**
     * Load notifications from the server
     */
    function loadNotifications() {
        // Show loading state
        notificationsList.innerHTML = `
            <li class="notification-item loading">
                <div class="notification-content">
                    <p class="notification-text">Loading notifications...</p>
                </div>
            </li>
        `;

        // Fetch notifications from the server with absolute path
        fetch('ajax/get_notifications.php?limit=5')
            .then(response => {
                // Check if response is valid JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    throw new Error('Invalid response format: ' + contentType);
                }
            })
            .then(data => {
                if (data.success) {
                    notifications = data.notifications;
                    unreadCount = data.unread_count;
                    updateNotificationsBadge();
                    renderNotifications();
                } else {
                    showError('Failed to load notifications');
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                showError('Failed to load notifications');
                // Use empty notifications array to prevent further errors
                notifications = [];
                unreadCount = 0;
                updateNotificationsBadge();
            });
    }

    /**
     * Render notifications in the dropdown
     */
    function renderNotifications() {
        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <li class="notification-item empty">
                    <div class="notification-content">
                        <p class="notification-text">No notifications</p>
                    </div>
                </li>
            `;
            return;
        }

        notificationsList.innerHTML = '';

        notifications.forEach(notification => {
            const notificationItem = document.createElement('li');
            notificationItem.className = `notification-item ${notification.is_read ? '' : 'unread'}`;
            notificationItem.dataset.id = notification.id;

            // Determine icon based on notification type
            let iconClass = 'fas fa-bell';
            switch (notification.type) {
                case 'success':
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'warning':
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
                case 'error':
                    iconClass = 'fas fa-exclamation-circle';
                    break;
                case 'info':
                default:
                    iconClass = 'fas fa-info-circle';
                    break;
            }

            notificationItem.innerHTML = `
                <div class="notification-icon ${notification.type}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="notification-content">
                    <h4 class="notification-title">${notification.title}</h4>
                    <p class="notification-text">${notification.message}</p>
                    <span class="notification-time">${notification.time_ago}</span>
                </div>
            `;

            // Add click event to mark as read and navigate if there's a link
            notificationItem.addEventListener('click', function() {
                if (!notification.is_read) {
                    markAsRead(notification.id);
                }

                if (notification.link) {
                    // Log original link
                    console.log('Original notification link:', notification.link);

                    // Use the link as is if it's an absolute URL
                    let link = notification.link;

                    // If it's not an absolute URL (doesn't start with http or https)
                    if (!link.startsWith('http://') && !link.startsWith('https://')) {
                        // Remove any 'admin/' prefix
                        if (link.startsWith('admin/')) {
                            console.log('Removing admin/ prefix');
                            link = link.substring(6); // Remove 'admin/'
                        }

                        // If it's a relative path, make it absolute
                        if (!link.startsWith('/')) {
                            // Get the current path
                            const currentPath = window.location.pathname;
                            const adminBasePath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
                            console.log('Current admin base path:', adminBasePath);

                            // Construct absolute path
                            link = adminBasePath + link;
                            console.log('Constructed absolute path:', link);
                        }
                    }

                    console.log('Final link to navigate to:', link);
                    window.location.href = link;
                } else if (notification.message.includes('contact submission')) {
                    // If it's a contact submission notification, redirect to contact submissions page
                    console.log('Contact submission notification without link, redirecting to contact_submissions.php');
                    window.location.href = 'contact_submissions.php';
                }
            });

            notificationsList.appendChild(notificationItem);
        });
    }

    /**
     * Mark a notification as read
     */
    function markAsRead(notificationId) {
        const formData = new FormData();
        formData.append('action', 'mark_read');
        formData.append('notification_id', notificationId);

        fetch('ajax/mark_notification_read.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // Check if response is valid JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Invalid response format: ' + contentType);
            }
        })
        .then(data => {
            if (data.success) {
                // Update local notification state
                notifications = notifications.map(notification => {
                    if (notification.id === notificationId) {
                        notification.is_read = true;
                    }
                    return notification;
                });

                // Update UI
                const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
                if (notificationItem) {
                    notificationItem.classList.remove('unread');
                }

                // Update unread count
                unreadCount = data.unread_count;
                updateNotificationsBadge();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
            // Mark as read locally anyway to prevent UI issues
            const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('unread');
            }
        });
    }

    /**
     * Mark all notifications as read
     */
    function markAllAsRead(event) {
        event.preventDefault();

        const formData = new FormData();
        formData.append('action', 'mark_all_read');

        fetch('ajax/mark_notification_read.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update local notification state
                notifications = notifications.map(notification => {
                    notification.is_read = true;
                    return notification;
                });

                // Update UI
                document.querySelectorAll('.notification-item.unread').forEach(item => {
                    item.classList.remove('unread');
                });

                // Update unread count
                unreadCount = 0;
                updateNotificationsBadge();
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });

        /* The code below is commented out because it's not working properly
        console.log('Marking all notifications as read...');

        // Create a form data object for the POST request
        const formData = new FormData();
        formData.append('action', 'mark_all_read');

        fetch('api/notifications.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
        */
                /* This is part of the commented out code
                // Update local notification state
                notifications = notifications.map(notification => {
                    notification.is_read = true;
                    return notification;
                });

                // Update UI
                document.querySelectorAll('.notification-item.unread').forEach(item => {
                    item.classList.remove('unread');
                });

                // Update unread count
                unreadCount = 0;
                updateNotificationsBadge();

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'notification-success-message';
                successMessage.textContent = 'All notifications marked as read';

                const notificationsHeader = document.querySelector('.notifications-header');
                if (notificationsHeader) {
                    notificationsHeader.appendChild(successMessage);

                    // Remove after 3 seconds
                    setTimeout(() => {
                        successMessage.style.opacity = '0';
                        setTimeout(() => {
                            successMessage.remove();
                        }, 300);
                    }, 3000);
                }
            } else {
                console.error('Error marking all notifications as read:', data.error || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });
        */
    }

    /**
     * Update the notifications badge
     */
    function updateNotificationsBadge() {
        if (notificationsBadge) {
            if (unreadCount > 0) {
                notificationsBadge.textContent = unreadCount;
                notificationsBadge.style.display = 'flex';
            } else {
                notificationsBadge.style.display = 'none';
            }
        }
    }

    /**
     * Show error message in the notifications list
     */
    function showError(message) {
        notificationsList.innerHTML = `
            <li class="notification-item error">
                <div class="notification-content">
                    <p class="notification-text">${message}</p>
                </div>
            </li>
        `;
    }

    // Set up periodic refresh (every 60 seconds)
    setInterval(function() {
        if (!isNotificationsOpen) {
            // Only update the badge when dropdown is closed
            fetch(window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'api/notifications.php?action=get&limit=1&unread_only=true')
                .then(response => {
                    // Check if response is valid JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        throw new Error('Invalid response format: ' + contentType);
                    }
                })
                .then(data => {
                    if (data.success) {
                        unreadCount = data.unread_count;
                        updateNotificationsBadge();
                    }
                })
                .catch(error => {
                    console.error('Error checking notifications:', error);
                    // Don't update the badge on error to avoid flickering
                });

            // Also sync with actual inbox unread count
            syncInboxNotificationCount();
        }
    }, 60000); // 60 seconds

    // Initial sync with inbox count
    syncInboxNotificationCount();

    /**
     * Sync notification count with actual inbox unread count
     */
    function syncInboxNotificationCount() {
        fetch(window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'ajax/sync_notification_count.php')
            .then(response => {
                // Check if response is valid JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    throw new Error('Invalid response format: ' + contentType);
                }
            })
            .then(data => {
                if (data.success) {
                    // Only update if there's a discrepancy
                    if (unreadCount !== data.unread_count) {
                        console.log('Syncing notification count: ' + unreadCount + ' -> ' + data.unread_count);
                        unreadCount = data.unread_count;
                        updateNotificationsBadge();
                    }
                }
            })
            .catch(error => {
                console.error('Error syncing notification count:', error);
                // Don't update the badge on error to avoid flickering
            });
    }
});
