<?php
/**
 * Simple Mailer Class
 * 
 * A lightweight email class that uses PHP's built-in mail() function
 * and supports SMTP via stream sockets for better compatibility
 */

class SimpleMailer {
    private $conn;
    private $settings;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }
    
    /**
     * Load email settings from database
     */
    private function loadSettings() {
        $this->settings = [
            'use_smtp' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_security' => 'tls',
            'from_email' => '<EMAIL>',
            'from_name' => 'Manage Inc.',
            'reply_to' => ''
        ];
        
        // Load settings from database
        $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'email'";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $key = $row['setting_key'];
                $value = $row['setting_value'];
                
                if (isset($this->settings[$key])) {
                    // Convert boolean values
                    if ($key === 'use_smtp') {
                        $this->settings[$key] = ($value === '1' || $value === 'true');
                    } elseif ($key === 'smtp_port') {
                        $this->settings[$key] = (int)$value;
                    } else {
                        $this->settings[$key] = $value;
                    }
                }
            }
        }
    }
    
    /**
     * Send email
     */
    public function send($to, $subject, $message, $attachments = []) {
        try {
            if ($this->settings['use_smtp'] && !empty($this->settings['smtp_host'])) {
                return $this->sendWithSMTP($to, $subject, $message, $attachments);
            } else {
                return $this->sendWithPHPMail($to, $subject, $message, $attachments);
            }
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Send email using PHP's built-in mail() function
     */
    private function sendWithPHPMail($to, $subject, $message, $attachments = []) {
        // Prepare headers
        $headers = $this->buildHeaders($attachments);
        
        // If we have attachments, we need to build a multipart message
        if (!empty($attachments)) {
            $boundary = uniqid('boundary_');
            $headers .= "Content-Type: multipart/mixed; boundary=\"$boundary\"\r\n";
            
            $body = $this->buildMultipartMessage($message, $attachments, $boundary);
        } else {
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $body = $message;
        }
        
        // Send email
        $result = mail($to, $subject, $body, $headers);
        
        if ($result) {
            error_log("Email sent successfully to $to using PHP mail()");
            return [
                'success' => true,
                'message' => 'Email sent successfully using PHP mail()'
            ];
        } else {
            $error = error_get_last();
            $error_message = $error ? $error['message'] : 'Unknown error';
            error_log("Failed to send email using PHP mail(): $error_message");
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $error_message
            ];
        }
    }
    
    /**
     * Send email using SMTP
     */
    private function sendWithSMTP($to, $subject, $message, $attachments = []) {
        $smtp_host = $this->settings['smtp_host'];
        $smtp_port = $this->settings['smtp_port'];
        $smtp_username = $this->settings['smtp_username'];
        $smtp_password = $this->settings['smtp_password'];
        $smtp_security = $this->settings['smtp_security'];
        
        // Create socket connection
        $context = stream_context_create();
        
        if ($smtp_security === 'ssl') {
            $smtp_host = 'ssl://' . $smtp_host;
        }
        
        $socket = stream_socket_client("$smtp_host:$smtp_port", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        
        if (!$socket) {
            throw new Exception("Failed to connect to SMTP server: $errstr ($errno)");
        }
        
        // Read initial response
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '220') {
            fclose($socket);
            throw new Exception("SMTP server not ready: $response");
        }
        
        // EHLO
        fwrite($socket, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
        $response = fgets($socket);
        
        // Start TLS if required
        if ($smtp_security === 'tls') {
            fwrite($socket, "STARTTLS\r\n");
            $response = fgets($socket);
            if (substr($response, 0, 3) !== '220') {
                fclose($socket);
                throw new Exception("STARTTLS failed: $response");
            }
            
            if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                fclose($socket);
                throw new Exception("Failed to enable TLS encryption");
            }
            
            // EHLO again after TLS
            fwrite($socket, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
            $response = fgets($socket);
        }
        
        // Authentication
        if (!empty($smtp_username) && !empty($smtp_password)) {
            fwrite($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket);
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                throw new Exception("AUTH LOGIN failed: $response");
            }
            
            fwrite($socket, base64_encode($smtp_username) . "\r\n");
            $response = fgets($socket);
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                throw new Exception("Username authentication failed: $response");
            }
            
            fwrite($socket, base64_encode($smtp_password) . "\r\n");
            $response = fgets($socket);
            if (substr($response, 0, 3) !== '235') {
                fclose($socket);
                throw new Exception("Password authentication failed: $response");
            }
        }
        
        // Send email
        $result = $this->sendSMTPMessage($socket, $to, $subject, $message, $attachments);
        
        // Quit
        fwrite($socket, "QUIT\r\n");
        fclose($socket);
        
        return $result;
    }
    
    /**
     * Send the actual SMTP message
     */
    private function sendSMTPMessage($socket, $to, $subject, $message, $attachments) {
        // MAIL FROM
        fwrite($socket, "MAIL FROM: <" . $this->settings['from_email'] . ">\r\n");
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '250') {
            throw new Exception("MAIL FROM failed: $response");
        }
        
        // RCPT TO
        fwrite($socket, "RCPT TO: <$to>\r\n");
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '250') {
            throw new Exception("RCPT TO failed: $response");
        }
        
        // DATA
        fwrite($socket, "DATA\r\n");
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '354') {
            throw new Exception("DATA command failed: $response");
        }
        
        // Headers
        $headers = $this->buildEmailHeaders($to, $subject);
        fwrite($socket, $headers);
        
        // Message body
        if (!empty($attachments)) {
            $boundary = uniqid('boundary_');
            fwrite($socket, "Content-Type: multipart/mixed; boundary=\"$boundary\"\r\n\r\n");
            $body = $this->buildMultipartMessage($message, $attachments, $boundary);
        } else {
            fwrite($socket, "Content-Type: text/html; charset=UTF-8\r\n\r\n");
            $body = $message;
        }
        
        fwrite($socket, $body . "\r\n.\r\n");
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '250') {
            throw new Exception("Message sending failed: $response");
        }
        
        error_log("Email sent successfully to $to using SMTP");
        return [
            'success' => true,
            'message' => 'Email sent successfully using SMTP'
        ];
    }
    
    /**
     * Build email headers for PHP mail()
     */
    private function buildHeaders($attachments = []) {
        $headers = "From: " . $this->settings['from_name'] . " <" . $this->settings['from_email'] . ">\r\n";
        
        if (!empty($this->settings['reply_to'])) {
            $headers .= "Reply-To: " . $this->settings['reply_to'] . "\r\n";
        }
        
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "X-Mailer: SimpleMailer 1.0\r\n";
        
        return $headers;
    }
    
    /**
     * Build email headers for SMTP
     */
    private function buildEmailHeaders($to, $subject) {
        $headers = "To: $to\r\n";
        $headers .= "Subject: $subject\r\n";
        $headers .= "From: " . $this->settings['from_name'] . " <" . $this->settings['from_email'] . ">\r\n";
        
        if (!empty($this->settings['reply_to'])) {
            $headers .= "Reply-To: " . $this->settings['reply_to'] . "\r\n";
        }
        
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "X-Mailer: SimpleMailer 1.0\r\n";
        $headers .= "Date: " . date('r') . "\r\n";
        
        return $headers;
    }
    
    /**
     * Build multipart message with attachments
     */
    private function buildMultipartMessage($message, $attachments, $boundary) {
        $body = "--$boundary\r\n";
        $body .= "Content-Type: text/html; charset=UTF-8\r\n";
        $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
        $body .= $message . "\r\n";
        
        foreach ($attachments as $attachment) {
            if (file_exists($attachment['path'])) {
                $body .= "--$boundary\r\n";
                $body .= "Content-Type: application/octet-stream\r\n";
                $body .= "Content-Transfer-Encoding: base64\r\n";
                $body .= "Content-Disposition: attachment; filename=\"" . basename($attachment['name']) . "\"\r\n\r\n";
                $body .= chunk_split(base64_encode(file_get_contents($attachment['path']))) . "\r\n";
            }
        }
        
        $body .= "--$boundary--\r\n";
        
        return $body;
    }
    
    /**
     * Get current settings
     */
    public function getSettings() {
        return $this->settings;
    }
    
    /**
     * Test email configuration
     */
    public function testConfiguration($test_email) {
        $subject = "Test Email from " . $this->settings['from_name'];
        $message = "<h2>Email Configuration Test</h2>";
        $message .= "<p>This is a test email to verify your email configuration is working correctly.</p>";
        $message .= "<p><strong>Sent at:</strong> " . date('Y-m-d H:i:s') . "</p>";
        $message .= "<p><strong>Method:</strong> " . ($this->settings['use_smtp'] ? 'SMTP' : 'PHP mail()') . "</p>";
        
        if ($this->settings['use_smtp']) {
            $message .= "<p><strong>SMTP Host:</strong> " . $this->settings['smtp_host'] . "</p>";
            $message .= "<p><strong>SMTP Port:</strong> " . $this->settings['smtp_port'] . "</p>";
            $message .= "<p><strong>Security:</strong> " . strtoupper($this->settings['smtp_security']) . "</p>";
        }
        
        return $this->send($test_email, $subject, $message);
    }
}
?>
