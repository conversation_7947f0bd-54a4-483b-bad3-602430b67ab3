<?php
/**
 * Email Templates Class
 *
 * Handles email templates management
 */
class EmailTemplates {
    private $conn;
    private $templates = [];

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeTemplates();
    }

    /**
     * Initialize templates
     */
    private function initializeTemplates() {
        // Check if email_templates table exists
        $check_table = "SHOW TABLES LIKE 'email_templates'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows == 0) {
            // Table doesn't exist, create it
            $this->createTemplatesTable();
        }

        // Load templates
        $this->loadTemplates();
    }

    /**
     * Create email_templates table
     */
    private function createTemplatesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `email_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `template_key` varchar(100) NOT NULL UNIQUE,
            `template_name` varchar(255) NOT NULL,
            `subject` varchar(500) NOT NULL,
            `content` text NOT NULL,
            `variables` text DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `template_key` (`template_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        $this->conn->query($sql);

        // Insert default templates
        $this->insertDefaultTemplates();
    }

    /**
     * Insert default email templates
     */
    private function insertDefaultTemplates() {
        $default_templates = [
            [
                'template_key' => 'contact_auto_reply',
                'template_name' => 'Contact Form Auto-Reply',
                'subject' => 'Thank You for Contacting Us',
                'content' => "Dear {contact_name},\n\nThank you for contacting {company_name}. This is an automatic confirmation that we have received your message.\n\nOur team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.\n\nFor your reference, here is a copy of your message:\n\nMessage:\n{contact_message}\n\nIf you have any urgent matters, please call us directly at our support number.\n\nBest regards,\nThe {company_name} Team",
                'variables' => '{contact_name}, {company_name}, {contact_message}, {site_url}'
            ],
            [
                'template_key' => 'contact_notification',
                'template_name' => 'Contact Form Notification (Admin)',
                'subject' => 'New Contact Form Submission from {contact_name}',
                'content' => "You have received a new message from your website contact form.\n\nName: {contact_name}\nEmail: {contact_email}\nPhone: {contact_phone}\nMessage:\n{contact_message}\n\nSource: {contact_source}\nDate: {submission_date}\n\nPlease respond to this inquiry as soon as possible.",
                'variables' => '{contact_name}, {contact_email}, {contact_phone}, {contact_message}, {contact_source}, {submission_date}'
            ],
            [
                'template_key' => 'password_reset',
                'template_name' => 'Password Reset',
                'subject' => 'Password Reset Request - {site_name}',
                'content' => "Hello {username},\n\nWe received a request to reset your password. Please click the link below to reset your password:\n\n{reset_url}\n\nThis link will expire in 1 hour.\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe {site_name} Team",
                'variables' => '{username}, {reset_url}, {site_name}, {site_url}'
            ],
            [
                'template_key' => 'email_verification',
                'template_name' => 'Email Verification',
                'subject' => 'Verify Your Account - {site_name}',
                'content' => "Hello {username},\n\nThank you for creating an account with us. Please click the link below to verify your email address:\n\n{verification_url}\n\nThis link will expire in 24 hours.\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\nThe {site_name} Team",
                'variables' => '{username}, {verification_url}, {site_name}, {site_url}'
            ]
        ];

        foreach ($default_templates as $template) {
            $template_key = $this->conn->real_escape_string($template['template_key']);
            $template_name = $this->conn->real_escape_string($template['template_name']);
            $subject = $this->conn->real_escape_string($template['subject']);
            $content = $this->conn->real_escape_string($template['content']);
            $variables = $this->conn->real_escape_string($template['variables']);

            $sql = "INSERT IGNORE INTO `email_templates`
                    (`template_key`, `template_name`, `subject`, `content`, `variables`)
                    VALUES
                    ('$template_key', '$template_name', '$subject', '$content', '$variables')";

            $this->conn->query($sql);
        }
    }

    /**
     * Load all templates
     */
    private function loadTemplates() {
        $sql = "SELECT * FROM email_templates";
        $result = $this->conn->query($sql);

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $this->templates[$row['template_key']] = $row;
            }
        }
    }

    /**
     * Get all templates
     *
     * @return array All templates
     */
    public function getAllTemplates() {
        return $this->templates;
    }

    /**
     * Get a specific template
     *
     * @param string $key Template key
     * @return array|null Template data or null if not found
     */
    public function getTemplate($key) {
        return isset($this->templates[$key]) ? $this->templates[$key] : null;
    }

    /**
     * Get template by ID
     *
     * @param int $id Template ID
     * @return array|null Template data or null if not found
     */
    public function getTemplateById($id) {
        $sql = "SELECT * FROM email_templates WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare template select statement: " . $this->conn->error);
            return null;
        }

        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $template = $result->fetch_assoc();
        $stmt->close();

        return $template;
    }

    /**
     * Update a template
     *
     * @param mixed $key_or_id Template key (string) or ID (int)
     * @param string $subject Template subject
     * @param string $content Template content
     * @param string $description Template description
     * @return bool Success status
     */
    public function updateTemplate($key_or_id, $subject, $content, $description = null) {
        // Determine if we're working with ID or key
        $is_id = is_numeric($key_or_id);

        // Log the template data for debugging
        error_log("EmailTemplates::updateTemplate - Key/ID: " . $key_or_id . " (is_id: " . ($is_id ? 'true' : 'false') . ")");
        error_log("EmailTemplates::updateTemplate - Subject length: " . strlen($subject));
        error_log("EmailTemplates::updateTemplate - Content length: " . strlen($content));

        // Validate inputs
        if (empty($key_or_id)) {
            error_log("EmailTemplates::updateTemplate - Empty key/id");
            return false;
        }

        if (empty($subject)) {
            error_log("EmailTemplates::updateTemplate - Empty subject");
            return false;
        }

        if (empty($content)) {
            error_log("EmailTemplates::updateTemplate - Empty content");
            return false;
        }

        // Check if template exists (only for key-based lookup)
        if (!$is_id && !isset($this->templates[$key_or_id])) {
            error_log("EmailTemplates::updateTemplate - Template not found: " . $key_or_id);
            return false;
        }

        try {
            // Normalize line breaks to \n (Unix style)
            // First convert all \r\n to \n, then convert any remaining \r to \n
            $content = str_replace("\r\n", "\n", $content);
            $content = str_replace("\r", "\n", $content);

            // Log the normalized content for debugging
            error_log("EmailTemplates::updateTemplate - Normalized content length: " . strlen($content));

            // Escape strings to prevent SQL injection
            $escaped_key_or_id = $this->conn->real_escape_string($key_or_id);
            $subject = $this->conn->real_escape_string($subject);
            $content = $this->conn->real_escape_string($content);
            $description = $description ? $this->conn->real_escape_string($description) : null;

            // Build the SQL query
            $sql = "UPDATE email_templates SET
                    subject = '$subject',
                    content = '$content'";

            if ($description !== null) {
                $sql .= ", description = '$description'";
            }

            // Use appropriate WHERE clause based on whether we have ID or key
            if ($is_id) {
                $sql .= " WHERE id = '$escaped_key_or_id'";
            } else {
                $sql .= " WHERE template_key = '$escaped_key_or_id'";
            }

            // Log the SQL query for debugging
            error_log("EmailTemplates::updateTemplate - SQL: " . $sql);

            // Execute the query
            $result = $this->conn->query($sql);

            if ($result) {
                // Update local cache (only for key-based updates)
                if (!$is_id && isset($this->templates[$key_or_id])) {
                    $this->templates[$key_or_id]['subject'] = $subject;
                    $this->templates[$key_or_id]['content'] = $content;
                    if ($description !== null) {
                        $this->templates[$key_or_id]['description'] = $description;
                    }
                }
                // For ID-based updates, reload templates to update cache
                if ($is_id) {
                    $this->loadTemplates();
                }
                error_log("EmailTemplates::updateTemplate - Template updated successfully");
                return true;
            } else {
                error_log("EmailTemplates::updateTemplate - Database error: " . $this->conn->error);
                return false;
            }
        } catch (Exception $e) {
            error_log("EmailTemplates::updateTemplate - Exception: " . $e->getMessage());
            return false;
        }
    }
}
?>
