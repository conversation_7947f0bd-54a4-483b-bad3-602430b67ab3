<?php
/**
 * Content Header Component
 *
 * This file provides a standardized header for content pages.
 * It replaces the enhanced-header.php file with a cleaner implementation.
 */

// Default page icon if not set
if (!isset($page_icon)) {
    // Define default icons for common pages
    $default_icons = [
        'dashboard.php' => 'fas fa-tachometer-alt',
        'all_news.php' => 'fas fa-newspaper',
        'create_news.php' => 'fas fa-plus-circle',
        'edit_news.php' => 'fas fa-edit',
        'categories.php' => 'fas fa-tags',
        'users.php' => 'fas fa-users',
        'roles.php' => 'fas fa-user-shield',
        'settings.php' => 'fas fa-cog',
        'profile.php' => 'fas fa-user',
        'contact_submissions.php' => 'fas fa-inbox',
        'frontend_editor.php' => 'fas fa-code',
        'html_editor.php' => 'fas fa-file-code',
        'edit_template.php' => 'fas fa-file-code',
        'email_templates.php' => 'fas fa-envelope',
        'help.php' => 'fas fa-question-circle'
    ];

    // Get current page filename
    $current_page = basename($_SERVER['PHP_SELF']);

    // Set icon based on current page or default to file icon
    $page_icon = isset($default_icons[$current_page]) ? $default_icons[$current_page] : 'fas fa-file';
}
?>

<div class="admin-content-header">
    <div class="admin-content-title-group">
        <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
        <?php if (isset($page_subtitle)): ?>
        <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        <?php endif; ?>

        <?php if (isset($back_link) && !empty($back_link)): ?>
        <a href="<?php echo $back_link['url']; ?>" class="admin-btn secondary">
            <i class="fas fa-arrow-left"></i> <?php echo $back_link['text']; ?>
        </a>
        <?php endif; ?>
    </div>

    <?php if (isset($primary_action) && !empty($primary_action)): ?>
    <div class="admin-content-actions">
        <a href="<?php echo $primary_action['url']; ?>" class="admin-btn primary" <?php echo isset($primary_action['data_action']) ? 'data-action="'.$primary_action['data_action'].'"' : ''; ?>>
            <i class="<?php echo $primary_action['icon']; ?>"></i> <?php echo $primary_action['text']; ?>
        </a>
    </div>
    <?php endif; ?>

    <?php if (isset($secondary_actions) && !empty($secondary_actions)): ?>
    <div class="admin-content-secondary-actions">
        <?php foreach ($secondary_actions as $action): ?>
        <a href="<?php echo $action['url']; ?>" class="admin-btn <?php echo isset($action['class']) ? $action['class'] : 'secondary'; ?>" <?php echo isset($action['data_action']) ? 'data-action="'.$action['data_action'].'"' : ''; ?>>
            <i class="<?php echo $action['icon']; ?>"></i> <?php echo $action['text']; ?>
        </a>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</div>
