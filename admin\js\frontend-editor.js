/**
 * Frontend Editor JavaScript
 *
 * Handles the frontend editor functionality
 */

// Global variables
let siteBaseUrl = '';

document.addEventListener('DOMContentLoaded', function() {
    // Detect site base URL
    detectSiteBaseUrl();

    // Initialize the editor
    initEditor();

    // Set up event listeners
    setupEventListeners();

    // Initialize content change tracking
    initContentChangeTracking();

    // Set up force unlock functionality
    setupForceUnlock();

    // Set up automatic lock extension
    setupLockExtension();

    // Set up beforeunload warning
    setupBeforeUnloadWarning();

    // Initialize version history if available
    if (typeof initVersionHistory === 'function') {
        initVersionHistory();
        loadVersionHistory();
    }

    // Check lock status if a file is selected
    if (currentFilePath) {
        checkLock(currentFilePath);
    }
});

/**
 * Initialize the editor
 */
function initEditor() {
    const editorContent = document.getElementById('editorContent');
    if (!editorContent) return;

    // Make sure content stays within the editor
    ensureContentFitsEditor();

    // Initialize content change tracking
    window.hasContentChanged = false;

    // Add event listener to track content changes
    editorContent.addEventListener('input', function() {
        window.hasContentChanged = true;

        // Show the release lock button when content changes
        const releaseLockBtn = document.getElementById('releaseLockBtn');
        if (releaseLockBtn) {
            releaseLockBtn.style.display = 'inline-block';
        }
    });
}

/**
 * Ensure content fits within the editor
 */
function ensureContentFitsEditor() {
    const editorContent = document.getElementById('editorContent');
    if (!editorContent) return;

    // Add event listener for paste events to clean up pasted content
    editorContent.addEventListener('paste', function(e) {
        // Let the paste happen normally
        setTimeout(function() {
            // After paste, find all images and make sure they have max-width
            const images = editorContent.querySelectorAll('img');
            images.forEach(function(img) {
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
            });

            // Find all tables and make sure they don't overflow
            const tables = editorContent.querySelectorAll('table');
            tables.forEach(function(table) {
                if (!table.parentElement.classList.contains('table-wrapper')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-wrapper';
                    wrapper.style.width = '100%';
                    wrapper.style.overflowX = 'auto';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            // Find all pre elements and make sure they don't overflow
            const preElements = editorContent.querySelectorAll('pre');
            preElements.forEach(function(pre) {
                pre.style.whiteSpace = 'pre-wrap';
                pre.style.wordWrap = 'break-word';
                pre.style.maxWidth = '100%';
                pre.style.overflowX = 'auto';
            });

            // Find all iframes and make sure they don't overflow
            const iframes = editorContent.querySelectorAll('iframe');
            iframes.forEach(function(iframe) {
                iframe.style.maxWidth = '100%';
            });
        }, 0);
    });

    // Process existing content
    const images = editorContent.querySelectorAll('img');
    images.forEach(function(img) {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
    });

    const tables = editorContent.querySelectorAll('table');
    tables.forEach(function(table) {
        if (!table.parentElement.classList.contains('table-wrapper')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-wrapper';
            wrapper.style.width = '100%';
            wrapper.style.overflowX = 'auto';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });

    const preElements = editorContent.querySelectorAll('pre');
    preElements.forEach(function(pre) {
        pre.style.whiteSpace = 'pre-wrap';
        pre.style.wordWrap = 'break-word';
        pre.style.maxWidth = '100%';
        pre.style.overflowX = 'auto';
    });

    const iframes = editorContent.querySelectorAll('iframe');
    iframes.forEach(function(iframe) {
        iframe.style.maxWidth = '100%';
    });
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Force unlock button
    const forceUnlockBtn = document.getElementById('forceUnlockBtn');
    if (forceUnlockBtn) {
        forceUnlockBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to force unlock this file? This will override the current user\'s lock.')) {
                forceReleaseLock(currentFilePath);
            }
        });
    }

    // Release lock button
    const releaseLockBtn = document.getElementById('releaseLockBtn');
    if (releaseLockBtn) {
        releaseLockBtn.addEventListener('click', function() {
            releaseLock(currentFilePath);
        });
    }

    // Form submission
    const editorForm = document.getElementById('editorForm');
    if (editorForm) {
        editorForm.addEventListener('submit', function() {
            // Show loading overlay
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            // Reset content change tracking
            window.hasContentChanged = false;

            // Hide release lock button
            if (releaseLockBtn) {
                releaseLockBtn.style.display = 'none';
            }
        });
    }
}

/**
 * Initialize content change tracking
 */
function initContentChangeTracking() {
    // Initialize content change flag
    window.hasContentChanged = false;
    window.lockAcquired = false;

    // Add event listener to track content changes
    const editorContent = document.getElementById('editorContent');
    if (editorContent) {
        editorContent.addEventListener('input', function() {
            // If this is the first edit and we haven't acquired a lock yet
            if (!window.hasContentChanged && !window.lockAcquired && currentFilePath) {
                // Acquire lock immediately on first edit
                acquireLock(currentFilePath).then(success => {
                    if (success) {
                        window.lockAcquired = true;
                        console.log('Lock acquired on first edit');
                    }
                });
            }

            // Set content changed flag
            window.hasContentChanged = true;

            // Show the release lock button when content changes
            const releaseLockBtn = document.getElementById('releaseLockBtn');
            if (releaseLockBtn) {
                releaseLockBtn.style.display = 'inline-block';
            }
        });
    }
}

/**
 * Set up force unlock functionality
 */
function setupForceUnlock() {
    // Already handled in setupEventListeners
}

/**
 * Set up automatic lock extension
 */
function setupLockExtension() {
    // Extend lock every 5 minutes
    if (currentFilePath) {
        setInterval(function() {
            extendLock(currentFilePath);
        }, 5 * 60 * 1000); // 5 minutes
    }
}

/**
 * Set up beforeunload warning
 */
function setupBeforeUnloadWarning() {
    window.addEventListener('beforeunload', function(e) {
        if (window.hasContentChanged) {
            // Standard message (browser will show its own message)
            const message = 'You have unsaved changes. Are you sure you want to leave?';
            e.returnValue = message;
            return message;
        }
    });
}

/**
 * Handle images in the editor
 *
 * This function processes all images in the editor content to ensure they display correctly
 */
function handleEditorImages() {
    const editorContent = document.getElementById('editorContent');
    if (!editorContent) return;

    // Find all images in the editor
    const images = editorContent.querySelectorAll('img');

    // Process each image
    images.forEach(function(img) {
        // Add error handler to each image
        if (!img.hasAttribute('data-error-handler-added')) {
            img.setAttribute('data-error-handler-added', 'true');

            img.addEventListener('error', function() {
                console.log('Image failed to load:', this.src);

                // Try to fix relative paths
                if (this.src.indexOf('http') !== 0) {
                    // Try to convert relative path to absolute
                    const basePath = window.location.origin + '/';
                    const newSrc = basePath + this.src.replace(/^\//, '');
                    console.log('Trying with absolute path:', newSrc);
                    this.src = newSrc;
                }
            });
        }
    });
}

/**
 * Detect the base URL of the site
 */
function detectSiteBaseUrl() {
    // Try to get the base URL from a meta tag if available
    const baseUrlMeta = document.querySelector('meta[name="site-base-url"]');
    if (baseUrlMeta && baseUrlMeta.getAttribute('content')) {
        siteBaseUrl = baseUrlMeta.getAttribute('content');
        console.log('Base URL detected from meta tag:', siteBaseUrl);
        return;
    }

    // Try to determine from the current file path
    if (currentFilePath) {
        // Make an AJAX request to get the site root
        fetch('ajax/frontend_editor.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            },
            body: 'action=get_site_root'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.site_root) {
                siteBaseUrl = data.site_root;
                console.log('Base URL detected from server:', siteBaseUrl);
            } else {
                // Fallback to origin
                siteBaseUrl = window.location.origin;
                console.log('Using origin as base URL:', siteBaseUrl);
            }
        })
        .catch(error => {
            console.error('Error detecting site base URL:', error);
            siteBaseUrl = window.location.origin;
        });
    } else {
        // Fallback to origin
        siteBaseUrl = window.location.origin;
        console.log('Using origin as base URL:', siteBaseUrl);
    }
}

// Call handleEditorImages when the editor content is loaded
document.addEventListener('DOMContentLoaded', function() {
    handleEditorImages();

    // Also call it after a short delay to catch any images loaded dynamically
    setTimeout(handleEditorImages, 1000);
});
