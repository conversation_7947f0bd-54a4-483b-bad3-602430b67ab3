/**
 * HTML Editor CSS
 *
 * Styles for the HTML editor in the admin panel
 */

/* Editor Container */
.editor-container {
    display: flex;
    flex-direction: column;
    min-height: 500px;
    height: auto;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: visible;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Editor Area */
.editor-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: visible;
    min-height: 500px;
    height: auto;
}

.editor-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.editor-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.editor-title i {
    margin-right: 8px;
    color: #20c997;
}

.editor-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.editor-content {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow-y: visible;
    overflow-x: hidden;
    min-height: 400px;
    height: auto !important;
    /* Ensure this container expands to fit content */
    max-height: none !important;
}

.editor-textarea {
    width: 100%;
    height: 100%;
    border: none;
    resize: none;
    padding: 15px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #212529;
}

/* Editor Toolbar */
.editor-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    z-index: 100;
    position: sticky;
    top: 0;
}

.editor-toolbar-group {
    display: flex;
    gap: 5px;
}

/* Editor and Preview Containers */
#editor-container,
#visual-editor-container {
    flex: 1;
    position: relative;
    overflow: visible;
    min-height: 600px;
    height: auto;
    display: flex;
    flex-direction: column;
}

/* WYSIWYG Editor */
.wysiwyg-toolbar {
    display: flex;
    justify-content: flex-start;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 10px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.visual-editor {
    width: 100%;
    height: 600px; /* Fixed height */
    background-color: #fff;
    border: none;
    outline: none;
    position: relative;
}

/* Visual editor iframe */
.visual-editor iframe,
.visual-editor-iframe {
    width: 100%;
    height: 600px; /* Fixed height */
    border: none;
    outline: none;
    background-color: #fff;
    display: block;
}

/* Loading Indicator for Visual Editor */
.iframe-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.iframe-loading-indicator .spinner {
    font-size: 2rem;
    color: #f1ca2f;
    margin-bottom: 1rem;
}

.iframe-loading-indicator .loading-text {
    font-size: 1rem;
    color: #495057;
}

/* Error Message for Visual Editor */
.iframe-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 2rem;
    text-align: center;
}

.iframe-error .error-icon {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.iframe-error .error-text {
    font-size: 1rem;
    color: #495057;
    max-width: 80%;
    line-height: 1.5;
}

/* Visual editor container */
#visual-editor-container {
    height: 600px; /* Fixed height */
    position: relative;
}

/* Hide code tools when in visual mode */
body.visual-mode .editor-toolbar-group.code-tools {
    display: none;
}

/* Visual editor content container */
.visual-editor > div {
    min-height: 100%;
}

/* Make sure images display properly */
.visual-editor img,
.visual-editor iframe body img {
    max-width: 100%;
    height: auto;
    display: inline-block;
}

/* Add border to images to make them easier to select */
.visual-editor img:hover,
.visual-editor iframe body img:hover {
    outline: 2px dashed #007bff;
    cursor: pointer;
}

/* Add border to links to make them easier to select */
.visual-editor a:hover,
.visual-editor iframe body a:hover {
    outline: 1px dashed #007bff;
    cursor: pointer;
}

.visual-editor:focus,
.visual-editor iframe:focus {
    outline: none;
}

/* WYSIWYG Editor Content Styles */
.visual-editor h1,
.visual-editor h2,
.visual-editor h3,
.visual-editor h4,
.visual-editor h5,
.visual-editor h6 {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.2;
}

.visual-editor p {
    margin-top: 0;
    margin-bottom: 1rem;
}

.visual-editor table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
}

.visual-editor table,
.visual-editor th,
.visual-editor td {
    border: 1px solid #dee2e6;
}

.visual-editor th,
.visual-editor td {
    padding: 0.75rem;
    vertical-align: top;
}

.visual-editor img {
    max-width: 100%;
    height: auto;
}

.editor-footer {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    position: sticky;
    bottom: 0;
    z-index: 100;
}

.editor-info {
    display: flex;
    gap: 15px;
    color: #6c757d;
    font-size: 13px;
}

.editor-info-item {
    display: flex;
    align-items: center;
}

.editor-info-item i {
    margin-right: 5px;
}

.editor-save-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.version-comment {
    width: 250px;
    margin-right: 10px;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #007bff;
}

.loading-spinner i {
    font-size: 24px;
}

/* Version History */
.version-history-container {
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    margin-top: 20px;
}

.version-history-toggle {
    padding: 15px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
}

.version-history-toggle:hover {
    background-color: #e9ecef;
}

.version-history-toggle h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-icon {
    transition: transform 0.3s;
}

.toggle-icon.rotate {
    transform: rotate(180deg);
}

.version-history-content {
    max-height: 300px;
    overflow-y: auto;
    border-top: 1px solid #e0e0e0;
}

/* File Selector */
.file-selector-container {
    margin-bottom: 10px;
}

.file-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
    max-width: 600px;
}

.file-selector-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
    min-width: 100px;
}

.file-selector {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    color: #495057;
}

.file-selector:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.file-selector-info {
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;
}

/* Lock Status */
.lock-status {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.lock-status i {
    margin-right: 5px;
}

.locked-by-me {
    background-color: #e9f5ff;
    color: #007bff;
}

.locked-by-other {
    background-color: #fff3cd;
    color: #856404;
}

.not-locked {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* CodeMirror Customizations */
.CodeMirror {
    height: auto !important;
    position: relative;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    line-height: 1.5;
    min-height: 600px;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Ensure CodeMirror expands to fit content */
.CodeMirror-scroll {
    min-height: 600px;
    height: auto !important;
    overflow-y: hidden !important;
    overflow-x: auto !important;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.CodeMirror-sizer {
    min-height: auto !important;
    flex: 1;
}

/* Force the editor container to expand */
#editor-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: auto !important;
    min-height: 600px;
}

/* Ensure the textarea expands properly */
#editor-textarea {
    height: auto !important;
    min-height: 600px;
}

.read-only-section {
    background-color: #f8f9fa;
    opacity: 0.8;
}

/* No File Selected */
.no-file-selected {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
    padding: 20px;
}

.no-file-icon {
    font-size: 48px;
    margin-bottom: 15px;
    color: #adb5bd;
}

.no-file-selected h4 {
    margin-bottom: 10px;
    font-weight: 600;
}

/* Version History Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal.show {
    display: block;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    width: 80%;
    max-width: 800px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 15px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
}
