<?php
/**
 * Error Log Checker
 * This script helps check recent error log entries
 */

// Check if user is admin (basic check)
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    // For debugging purposes, allow access with a simple password
    if (!isset($_GET['debug']) || $_GET['debug'] !== 'contact2025') {
        die('Access denied. Add ?debug=contact2025 to access.');
    }
}

echo "<h1>🔍 Error Log Checker</h1>";

// Try to find error log files
$possible_log_files = [
    ini_get('error_log'),
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    '/var/log/php_errors.log',
    '/tmp/php_errors.log',
    'error_log',
    '../error_log',
    '../../error_log'
];

echo "<h2>📁 Checking for Error Log Files</h2>";

$found_logs = [];
foreach ($possible_log_files as $log_file) {
    if ($log_file && file_exists($log_file) && is_readable($log_file)) {
        $found_logs[] = $log_file;
        echo "<p>✅ Found: " . htmlspecialchars($log_file) . "</p>";
    }
}

if (empty($found_logs)) {
    echo "<p>❌ No accessible error log files found</p>";
    echo "<p>PHP error_log setting: " . (ini_get('error_log') ?: 'Not set') . "</p>";
}

// Check recent errors
echo "<h2>📋 Recent Error Entries</h2>";

foreach ($found_logs as $log_file) {
    echo "<h3>Log file: " . htmlspecialchars($log_file) . "</h3>";
    
    try {
        $lines = file($log_file);
        if ($lines === false) {
            echo "<p>❌ Could not read log file</p>";
            continue;
        }
        
        // Get last 50 lines
        $recent_lines = array_slice($lines, -50);
        
        // Filter for contact form related errors
        $contact_errors = [];
        foreach ($recent_lines as $line) {
            if (stripos($line, 'contact') !== false || 
                stripos($line, 'process-contact') !== false ||
                stripos($line, 'CHECKPOINT') !== false ||
                stripos($line, 'FATAL ERROR') !== false) {
                $contact_errors[] = $line;
            }
        }
        
        if (!empty($contact_errors)) {
            echo "<h4>Contact Form Related Errors:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
            foreach ($contact_errors as $error) {
                echo htmlspecialchars($error);
            }
            echo "</pre>";
        } else {
            echo "<p>No contact form related errors found in recent entries</p>";
        }
        
        // Show last 10 lines regardless
        echo "<h4>Last 10 Log Entries:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
        $last_lines = array_slice($lines, -10);
        foreach ($last_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error reading log file: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Test if we can write to error log
echo "<h2>🧪 Error Logging Test</h2>";

$test_message = "TEST ERROR LOG ENTRY - " . date('Y-m-d H:i:s');
if (error_log($test_message)) {
    echo "<p>✅ Successfully wrote test message to error log</p>";
} else {
    echo "<p>❌ Failed to write to error log</p>";
}

// Show PHP configuration
echo "<h2>⚙️ PHP Error Configuration</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";

$error_settings = [
    'error_reporting' => error_reporting(),
    'display_errors' => ini_get('display_errors'),
    'log_errors' => ini_get('log_errors'),
    'error_log' => ini_get('error_log'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'post_max_size' => ini_get('post_max_size'),
    'upload_max_filesize' => ini_get('upload_max_filesize')
];

foreach ($error_settings as $setting => $value) {
    echo "<tr><td>" . htmlspecialchars($setting) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

// Manual error trigger test
echo "<h2>🔥 Manual Error Test</h2>";
if (isset($_GET['trigger_error'])) {
    echo "<p>Triggering test error...</p>";
    error_log("MANUAL TEST ERROR from check-error-logs.php - " . date('Y-m-d H:i:s'));
    trigger_error("This is a test error", E_USER_WARNING);
    echo "<p>Test error triggered. Check logs above.</p>";
} else {
    echo "<p><a href='?debug=contact2025&trigger_error=1'>Click here to trigger a test error</a></p>";
}

?>

<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th { background: #f1ca2f; color: #333; padding: 8px; }
td { padding: 8px; }
p { margin: 10px 0; }
</style>
