<?php
/**
 * Admin Login Page
 *
 * This page handles user authentication for the admin panel.
 * It redirects to index.php which contains the actual login form.
 */

// Start session
session_start();

// Include required files
require_once 'config.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // Redirect to dashboard
    redirect('dashboard.php');
    exit;
}

// Check if installation is needed
if (!defined('DB_INSTALLED') || DB_INSTALLED !== true) {
    // Redirect to installation page
    redirect('install.php', ['reason' => 'not_installed']);
    exit;
}

// Redirect to index.php which handles the login form
redirect('index.php');
exit;
