/**
 * Profile Page CSS
 * 
 * This file contains styles specific to the user profile page.
 */

/* Profile Container */
.profile-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

/* Profile Header */
.profile-header {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

/* Profile Avatar */
.profile-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 4px solid var(--white);
  box-shadow: var(--shadow-md);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.profile-avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px solid var(--white);
  transition: all var(--transition-fast) ease;
}

.profile-avatar-edit:hover {
  background-color: var(--primary-dark);
  transform: scale(1.1);
}

/* Profile Info */
.profile-info {
  flex: 1;
}

.profile-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.profile-role {
  font-size: var(--font-size-base);
  color: var(--text-light);
  margin: 0 0 var(--spacing-3);
}

.profile-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-3);
}

.profile-meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.profile-meta-icon {
  color: var(--text-muted);
}

.profile-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
}

/* Profile Tabs */
.profile-tabs {
  margin-bottom: var(--spacing-4);
}

.profile-tabs .tab-list {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-1);
}

.profile-tabs .tab-link {
  padding: var(--spacing-3) var(--spacing-4);
}

/* Profile Content */
.profile-content {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-6);
}

/* Profile Section */
.profile-section {
  margin-bottom: var(--spacing-6);
}

.profile-section:last-child {
  margin-bottom: 0;
}

.profile-section-header {
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.profile-section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.profile-section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Profile Form */
.profile-form .form-group {
  margin-bottom: var(--spacing-4);
}

.profile-form-row {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.profile-form-col {
  flex: 1;
}

.profile-form-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

/* Profile Stats */
.profile-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.profile-stat {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: var(--spacing-4);
  text-align: center;
}

.profile-stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.profile-stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: 0;
}

/* Profile Activity */
.profile-activity {
  margin-bottom: var(--spacing-6);
}

.profile-activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.profile-activity-item:last-child {
  border-bottom: none;
}

.profile-activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.profile-activity-content {
  flex: 1;
}

.profile-activity-text {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  margin-bottom: var(--spacing-1);
}

.profile-activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Profile Security */
.profile-security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.profile-security-item:last-child {
  border-bottom: none;
}

.profile-security-info {
  display: flex;
  flex-direction: column;
}

.profile-security-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.profile-security-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Profile Notifications */
.profile-notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.profile-notification-item:last-child {
  border-bottom: none;
}

.profile-notification-info {
  display: flex;
  flex-direction: column;
}

.profile-notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-1);
}

.profile-notification-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .profile-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-4);
  }
  
  .profile-meta {
    justify-content: center;
  }
  
  .profile-content {
    padding: var(--spacing-4);
  }
  
  .profile-form-row {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .profile-stats {
    grid-template-columns: 1fr;
  }
}
