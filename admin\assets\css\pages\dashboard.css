/**
 * Dashboard CSS
 *
 * This file contains styles specific to the dashboard page.
 */

/* Dashboard Layout */
.dashboard-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
}

.dashboard-stat-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  transition: box-shadow var(--transition-fast) ease, transform var(--transition-fast) ease;
}

.dashboard-stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.dashboard-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.dashboard-stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0;
}

.dashboard-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.dashboard-stat-icon.primary {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
}

.dashboard-stat-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.dashboard-stat-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.dashboard-stat-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.dashboard-stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: var(--spacing-1) 0;
}

.dashboard-stat-change {
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.dashboard-stat-change.positive {
  color: var(--success-color);
}

.dashboard-stat-change.negative {
  color: var(--danger-color);
}

/* Dashboard Quick Actions */
.dashboard-quick-actions {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)) !important;
  gap: var(--spacing-4) !important;
}

.dashboard-quick-action {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast) ease;
  text-decoration: none;
}

.dashboard-quick-action:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-color);
}

.dashboard-quick-action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-3);
}

.dashboard-quick-action-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin: 0 0 var(--spacing-1);
}

.dashboard-quick-action-description {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin: 0;
}

/* Dashboard Charts */
.dashboard-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4);
}

.dashboard-chart-card {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dashboard-chart-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-chart-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-chart-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin: var(--spacing-1) 0 0;
}

.dashboard-chart-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-chart-body {
  padding: var(--spacing-4);
  height: 300px;
}

/* Dashboard Recent Activity */
.dashboard-activity {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dashboard-activity-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-activity-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-activity-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-activity-body {
  padding: var(--spacing-4);
  max-height: 400px;
  overflow-y: auto;
}

.dashboard-activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.dashboard-activity-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.dashboard-activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.dashboard-activity-content {
  flex: 1;
}

.dashboard-activity-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
  color: var(--text-color);
}

.dashboard-activity-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-bottom: var(--spacing-1);
}

.dashboard-activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Dashboard Recent News */
.dashboard-news {
  background-color: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dashboard-news-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-news-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dashboard-news-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dashboard-news-body {
  padding: var(--spacing-4);
}

.dashboard-news-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--border-light);
}

.dashboard-news-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.dashboard-news-image {
  width: 80px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  flex-shrink: 0;
}

.dashboard-news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dashboard-news-content {
  flex: 1;
}

.dashboard-news-item-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
  color: var(--text-dark);
}

.dashboard-news-item-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  margin-bottom: var(--spacing-1);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dashboard-news-item-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-charts {
    grid-template-columns: 1fr;
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}

@media (max-width: 992px) {
  .dashboard-quick-actions {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .dashboard-chart-body {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    gap: var(--spacing-4);
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--spacing-3) !important;
  }

  /* Ensure cards are always in two columns */
  .dashboard-quick-action {
    min-width: 0 !important;
    width: 100% !important;
  }

  .dashboard-stat-value {
    font-size: var(--font-size-xl);
  }

  .dashboard-chart-title,
  .dashboard-activity-title,
  .dashboard-news-title {
    font-size: var(--font-size-base);
  }

  .dashboard-chart-header,
  .dashboard-activity-header,
  .dashboard-news-header {
    padding: var(--spacing-3);
  }

  .dashboard-chart-body,
  .dashboard-activity-body,
  .dashboard-news-body {
    padding: var(--spacing-3);
  }
}

@media (max-width: 576px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr); /* Keep two columns for stats */
    gap: var(--spacing-2);
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(2, 1fr); /* Keep two columns for quick actions */
    gap: var(--spacing-2);
  }

  .dashboard-stat-card {
    padding: var(--spacing-3);
  }

  .dashboard-quick-action {
    padding: var(--spacing-3);
    min-width: 0;
  }

  .dashboard-quick-action-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2);
  }

  .dashboard-quick-action-title {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }

  .dashboard-quick-action-description {
    font-size: 10px;
  }

  .dashboard-chart-body {
    height: 200px;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  .dashboard-stats {
    grid-template-columns: 1fr; /* Single column for very small screens */
  }

  .dashboard-quick-actions {
    grid-template-columns: repeat(2, 1fr); /* Keep two columns for quick actions */
    gap: var(--spacing-2);
  }
}
