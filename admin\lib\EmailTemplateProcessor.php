<?php
/**
 * Email Template Processor
 *
 * This class processes email templates by wrapping them in a consistent HTML structure
 * and replacing template variables with actual values.
 */
class EmailTemplateProcessor {
    private $conn;
    private $settings;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }

    /**
     * Load settings from database
     */
    private function loadSettings() {
        // Get settings from system_settings table
        $sql = "SELECT category, setting_key, setting_value FROM system_settings
                WHERE category IN ('general', 'email', 'admin_appearance', 'appearance')";
        $result = $this->conn->query($sql);

        $this->settings = [
            'site_name' => 'Manage Inc.',
            'site_url' => 'https://manageinc.com',
            'admin_logo_path' => '../admin/images/logo.png',
            'primary_color' => '#f1ca2f',
            'secondary_color' => '#2c3e50',  // Default secondary color matching the sidebar
            'from_email' => '<EMAIL>',
            'from_name' => 'Manage Inc.'
        ];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $key = $row['category'] . '_' . $row['setting_key'];
                $this->settings[$key] = $row['setting_value'];

                // Also store some common settings with simpler keys
                if ($row['setting_key'] === 'site_name' && $row['category'] === 'general') {
                    $this->settings['site_name'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'site_url' && $row['category'] === 'general') {
                    $this->settings['site_url'] = $row['setting_value'];
                }
                // Admin logo path - check both admin_appearance and general categories
                if (($row['setting_key'] === 'admin_logo_path' && $row['category'] === 'admin_appearance') ||
                    ($row['setting_key'] === 'admin_logo' && $row['category'] === 'general')) {
                    $this->settings['admin_logo_path'] = $row['setting_value'];
                    // Store the timestamp to ensure we get the latest version of the logo
                    $this->settings['logo_timestamp'] = time();
                }

                // Email settings
                if ($row['setting_key'] === 'from_email' && $row['category'] === 'email') {
                    $this->settings['from_email'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'from_name' && $row['category'] === 'email') {
                    $this->settings['from_name'] = $row['setting_value'];
                }

                // Theme colors - prioritize appearance category
                if ($row['setting_key'] === 'primary_color' && $row['category'] === 'appearance') {
                    $this->settings['primary_color'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'secondary_color' && $row['category'] === 'appearance') {
                    $this->settings['secondary_color'] = $row['setting_value'];
                }
            }
        }
    }

    /**
     * Process an email template
     *
     * @param string $content The email content
     * @param array $variables Variables to replace in the template
     * @return string The processed email content with HTML wrapper
     */
    public function processTemplate($content, $variables = []) {
        // Replace variables in content
        $content = $this->replaceVariables($content, $variables);

        // Convert newlines to <br> tags if content is not already HTML
        if (strpos($content, '<html>') === false && strpos($content, '<body>') === false) {
            $content = nl2br($content);
        }

        // If content already has HTML structure, extract the body content
        if (strpos($content, '<body>') !== false) {
            preg_match('/<body>(.*?)<\/body>/is', $content, $matches);
            if (isset($matches[1])) {
                $content = $matches[1];
            }
        }

        // Get the logo URL
        $logo_path = $this->settings['admin_logo_path'];
        $site_url = $this->settings['site_url'];
        $site_name = $this->settings['site_name'];
        $logo_timestamp = isset($this->settings['logo_timestamp']) ? $this->settings['logo_timestamp'] : time();

        // Make sure logo path is absolute
        if (strpos($logo_path, 'http') !== 0) {
            // For testing environment, use a direct path to the logo
            if ($_SERVER['SERVER_NAME'] === 'localhost') {
                // Use a direct path for localhost
                $logo_path = 'http://localhost:8000/admin/images/logo.png?v=' . $logo_timestamp;
            } else {
                // For production, use the site URL
                $logo_path = $site_url . '/' . ltrim($logo_path, '/') . '?v=' . $logo_timestamp;
            }
        }

        // Get theme colors
        $primary_color = $this->settings['primary_color'];
        $secondary_color = $this->settings['secondary_color'];

        // Create HTML wrapper with logo
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($site_name) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 0;
            border-bottom: 1px solid #eee;
            width: 100%;
        }
        .logo-background {
            background-color: ' . $secondary_color . ';
            padding: 15px 0;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .content-wrapper {
            padding: 20px;
        }
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            text-align: center;
        }
        .logo {
            max-width: 200px;
            max-height: 50px;
            height: auto;
            display: inline-block;
            background-color: transparent;
            margin: 0 auto;
        }
        .content {
            padding: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
            text-align: center;
        }
        a { color: ' . $primary_color . '; }
        h1, h2, h3 { color: ' . $secondary_color . '; }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: ' . $primary_color . ';
            color: ' . $secondary_color . ' !important;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        @media only screen and (max-width: 620px) {
            .container { width: 100% !important; }
            .content { padding: 10px !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-background">
                <div class="logo-container">
                    <img src="http://localhost:8000/admin/images/logo.png?v=' . time() . '" alt="' . htmlspecialchars($site_name) . '" class="logo">
                </div>
            </div>
        </div>
        <div class="content-wrapper">
            <div class="content">
                ' . $content . '
            </div>
            <div class="footer">
                <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($site_name) . '. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * Get the current settings
     *
     * @return array The current settings
     */
    public function getSettings() {
        return $this->settings;
    }

    /**
     * Replace template variables with actual values
     *
     * @param string $content The template content
     * @param array $variables Variables to replace
     * @return string The content with variables replaced
     */
    private function replaceVariables($content, $variables) {
        // Add common variables
        $common_variables = [
            'company_name' => $this->settings['site_name'],
            'site_name' => $this->settings['site_name'],
            'site_url' => $this->settings['site_url'],
            'current_date' => date('F j, Y')
        ];

        // Merge with provided variables (provided variables take precedence)
        $variables = array_merge($common_variables, $variables);

        // Replace variables in {variable} format
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        // Also replace variables in {{variable}} format (for compatibility)
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return $content;
    }
}
?>
