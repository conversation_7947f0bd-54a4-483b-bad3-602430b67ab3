<?php
// Start output buffering to prevent any output before JSON
ob_start();

require_once 'admin/config.php';

// Check if this is an AJAX request for JSON data
$isAjaxRequest = isset($_GET['format']) && $_GET['format'] === 'json';

// Check if slug or ID is provided
if (isset($_GET['slug']) && !empty($_GET['slug'])) {
    $slug = $_GET['slug'];

    // Prepare the SQL statement to prevent SQL injection
    $stmt = $conn->prepare("
        SELECT n.*, IFNULL(c.name, 'News') as category_name
        FROM news n
        LEFT JOIN categories c ON n.category_id = c.id
        WHERE n.slug = ? OR n.id = ?
        LIMIT 1
    ");

    // If the slug is numeric, it might be an ID
    $isNumeric = is_numeric($slug);
    $idParam = $isNumeric ? intval($slug) : -1;

    if ($stmt) {
        $stmt->bind_param("si", $slug, $idParam);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows == 0) {
            redirect('news.php');
        }

        $news = $result->fetch_assoc();
        $stmt->close();
    } else {
        redirect('news.php');
    }
} else if (isset($_GET['id']) && !empty($_GET['id'])) {
    // Legacy support for ID parameter
    $id = intval($_GET['id']);

    // Get news post data
    $sql = "SELECT n.*, IFNULL(c.name, 'News') as category_name
            FROM news n
            LEFT JOIN categories c ON n.category_id = c.id
            WHERE n.id = $id";
    $result = $conn->query($sql);

    if ($result->num_rows == 0) {
        redirect('news.php');
    }

    $news = $result->fetch_assoc();
} else {
    redirect('news.php');
}

// Format the date
$date = date('F j, Y', strtotime($news['created_at']));

// If this is an AJAX request, return JSON data
if ($isAjaxRequest) {
    // Clean any output that might have been generated
    ob_clean();

    // Set proper JSON headers
    header('Content-Type: application/json');
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');

    // Prepare the response data
    $responseData = [
        'item' => [
            'id' => $news['id'],
            'title' => $news['title'],
            'content' => $news['content'],
            'image' => $news['image'],
            'slug' => $news['slug'] ? $news['slug'] : $news['id'],
            'category_name' => isset($news['category_name']) ? $news['category_name'] : 'News',
            'created_at' => $news['created_at'],
            'formatted_date' => $date
        ],
        'error' => null
    ];

    // Output JSON and end script execution
    echo json_encode($responseData);
    ob_end_flush();
    exit;
}
?>
<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title><?php echo $news['title']; ?> | Manage Inc.</title>

    <?php
    // Create a short description from the content
    $description = strip_tags($news['content']);
    $description = preg_replace('/\s+/', ' ', $description);
    $description = substr($description, 0, 157) . '...';

    // Get the base URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;

    // Build the canonical URL
    $canonicalUrl = $baseUrl . '/' . $news['slug'] . '.html';
    ?>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars($description); ?>">
    <meta name="keywords" content="manage inc, news, <?php echo strtolower($news['category_name']); ?>, <?php echo strtolower($news['title']); ?>">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($news['title']); ?> | Manage Inc.">
    <meta property="og:description" content="<?php echo htmlspecialchars($description); ?>">
    <meta property="og:image" content="<?php echo $baseUrl; ?>/images/news/<?php echo $news['image']; ?>">
    <meta property="og:url" content="<?php echo $canonicalUrl; ?>">
    <meta property="og:type" content="article">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($news['title']); ?> | Manage Inc.">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($description); ?>">
    <meta name="twitter:image" content="<?php echo $baseUrl; ?>/images/news/<?php echo $news['image']; ?>">

    <!-- Canonical Tag -->
    <link rel="canonical" href="<?php echo $canonicalUrl; ?>">
    <link rel="stylesheet" href="css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/carousel.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/submenu.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- News detail styles are now in the main CSS file -->
    <style>
        .news-detail-header h1 {
            font-size: 36px !important;
            color: #3c3c45;
            margin-bottom: 20px;
            font-weight: 600;
            line-height: 1.2;
        }

        .news-detail-container {
            padding: 0 40px;
        }

        .news-detail-header {
            margin-top: 20px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .news-detail-content {
            font-size: 16px !important;
            line-height: 1.8 !important;
        }

        .news-detail-meta {
            margin-bottom: 25px !important;
            font-size: 15px !important;
            color: #666;
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
        }

        /* Remove any ::before pseudo-elements */
        .news-detail-meta::before,
        .news-date::before,
        .news-category::before {
            display: none !important;
            content: none !important;
        }

        .news-date, .news-category {
            display: inline-flex;
            align-items: center;
            background-color: #f9f9f9;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }

        .news-date i {
            margin-right: 6px;
            color: #f1ca2f;
        }

        .news-category {
            color: #333;
            background-color: #f1ca2f;
            font-weight: 600;
        }

        .news-detail-image {
            margin-top: 5px !important;
            margin-bottom: 40px !important;
        }

        /* Ensure mobile menu toggle is visible */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block !important;
            }

            .main-nav {
                display: none !important;
            }

            .main-nav.active {
                display: block !important;
            }

            .logo {
                text-align: left !important;
            }

            .main-header .container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .news-detail-container {
                padding: 20px !important;
            }

            .news-detail-header {
                margin-top: 15px !important;
                margin-bottom: 10px !important;
                padding-bottom: 10px !important;
            }

            .news-detail-header h1 {
                font-size: 26px !important;
                margin-bottom: 15px !important;
            }

            .news-detail-meta {
                margin-bottom: 20px !important;
                font-size: 14px !important;
                flex-wrap: wrap;
                gap: 10px;
            }

            .news-date, .news-category {
                padding: 5px 10px !important;
                border-radius: 3px !important;
            }

            .news-detail-image {
                margin-top: 0 !important;
                margin-bottom: 25px !important;
            }

            .news-detail-image img {
                height: auto !important;
                border-radius: 0 !important;
            }
        }

        @media (max-width: 576px) {
            .news-detail-container {
                padding: 15px !important;
            }

            .news-detail-header {
                margin-top: 10px !important;
                margin-bottom: 8px !important;
                padding-bottom: 8px !important;
            }

            .news-detail-header h1 {
                font-size: 22px !important;
            }

            .news-detail-meta {
                margin-bottom: 15px !important;
                font-size: 12px !important;
                gap: 8px;
            }

            .news-date, .news-category {
                padding: 4px 8px !important;
                border-radius: 3px !important;
            }

            .news-date i {
                margin-right: 4px !important;
            }

            .news-detail-content {
                font-size: 15px !important;
            }

            .news-detail-image {
                margin-bottom: 25px !important;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="Manage Incorporated - Established in 1995">
                </a>
            </div>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html">HOME</a></li>
                    <li><a href="cloud.html">CLOUD</a></li>
                    <li><a href="managed-services.html">MANAGED SERVICES</a></li>
                    <li><a href="infrastructure.html">INFRASTRUCTURE</a></li>
                    <li><a href="services.html">SERVICES</a></li>
                    <li class="active"><a href="news.php">NEWS</a></li>
                    <li><a href="contact-us.php">CONTACT US</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="news-page-banner" style="margin-top: 95px;">
            <div class="container">
                <h1>News</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <div class="news-detail-container">
                    <div class="news-detail-header">
                        <h1><?php echo $news['title']; ?></h1>
                        <div class="news-detail-meta">
                            <span class="news-date"><i class="fas fa-calendar-alt"></i> Published on <?php echo $date; ?></span>
                            <?php
                            // Get category if available
                            if (isset($news['category_name']) && !empty($news['category_name'])) {
                                echo '<span class="news-category">' . $news['category_name'] . '</span>';
                            } else if (isset($news['category_id']) && !empty($news['category_id'])) {
                                $cat_id = $news['category_id'];
                                $cat_sql = "SELECT name FROM categories WHERE id = $cat_id";
                                $cat_result = $conn->query($cat_sql);
                                if ($cat_result && $cat_result->num_rows > 0) {
                                    $category = $cat_result->fetch_assoc();
                                    echo '<span class="news-category">' . $category['name'] . '</span>';
                                }
                            }
                            ?>
                        </div>
                    </div>
                    <div class="news-detail-image">
                        <img src="images/news/<?php echo $news['image']; ?>" alt="<?php echo $news['title']; ?>">
                    </div>
                    <div class="news-detail-content">
                        <?php
                        // Add paragraph tags to content if they don't exist
                        $content = $news['content'];
                        if (strpos($content, '<p>') === false) {
                            $paragraphs = explode("\n\n", $content);
                            $content = '';
                            foreach ($paragraphs as $paragraph) {
                                if (trim($paragraph) !== '') {
                                    $content .= '<p>' . $paragraph . '</p>';
                                }
                            }
                        }
                        echo $content;
                        ?>
                    </div>
                    <a href="news.html" class="back-to-news"><i class="back-arrow"></i> Back to News</a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <!-- Footer Top Section -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-top-content">
                    <div class="footer-info">
                        <h2>BUILD A FUTURE-READY IT INFRASTRUCTURE</h2>
                        <p>Leverage cutting-edge technology to optimize performance, security, and scalability.</p>
                    </div>
                    <div class="footer-form">
                        <form method="post" action="process-contact.php" id="footer-contact-form">
                            <input type="hidden" name="source" value="Footer Form">
                            <div class="form-row">
                                <input type="text" name="name" placeholder="Name" required>
                                <input type="email" name="email" placeholder="Email Address" required>
                            </div>
                            <textarea name="message" placeholder="Message" required></textarea>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section -->
        <div class="footer-middle">
            <div class="container">
                <div class="footer-middle-content">
                    <div class="footer-logo">
                        <img src="images/footer/manage-inc-logo.png" alt="Manage Incorporated">
                    </div>
                    <div class="footer-services">
                        <h4>SERVICES</h4>
                        <ul>
                            <li><a href="cloud.html">Cloud</a></li>
                            <li><a href="managed-services.html">Managed Services</a></li>
                            <li><a href="infrastructure.html">Infrastructure</a></li>
                            <li><a href="services.html">Services</a></li>
                        </ul>
                    </div>
                    <div class="footer-contact">
                        <h4>Seattle, WA Office</h4>
                        <p><img src="images/footer/phone-icon.svg" alt="Phone"> (*************</p>
                        <p><img src="images/footer/fax-icon.svg" alt="Fax"> (*************</p>
                        <p><img src="images/footer/email-icon.svg" alt="Email"> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><img src="images/footer/location-icon.svg" alt="Location"> 600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <p>© Copyright 2025. Manage Inc. all Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/carousel.js?v=<?php echo time(); ?>"></script>
    <script src="js/mobile-menu.js?v=<?php echo time(); ?>"></script>

    <!-- Structured Data for Article -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "<?php echo htmlspecialchars($news['title']); ?>",
        "description": "<?php echo htmlspecialchars($description); ?>",
        "image": "<?php echo $baseUrl; ?>/images/news/<?php echo $news['image']; ?>",
        "datePublished": "<?php echo date('Y-m-d', strtotime($news['created_at'])); ?>",
        "dateModified": "<?php echo date('Y-m-d', strtotime($news['updated_at'] ?? $news['created_at'])); ?>",
        "author": {
            "@type": "Organization",
            "name": "Manage Inc"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Manage Inc",
            "logo": {
                "@type": "ImageObject",
                "url": "<?php echo $baseUrl; ?>/images/logo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "<?php echo $canonicalUrl; ?>"
        }
    }
    </script>
    <script>
        // Ensure mobile menu is working
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.toggle('active');
                    document.getElementById('mainNav').classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
