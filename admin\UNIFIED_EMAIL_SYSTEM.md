# Unified Email System Implementation

## Overview
The admin panel now uses a unified email system that follows the same approach as the installation process:
- **Default**: Uses P<PERSON>'s built-in `mail()` function
- **Optional**: Uses SMTP when explicitly enabled via `use_smtp` setting
- **Fallback**: Automatically falls back to `mail()` if SMTP fails

## Key Changes Made

### 1. Core Email Functions (`admin/includes/email-functions.php`)
- **Replaced** complex PHPMailer-based system with unified approach
- **Added** `send_email()` - main function that routes to mail() or SMTP
- **Added** `send_email_mail()` - handles PHP mail() sending (like installation)
- **Added** `send_email_smtp()` - handles SMTP sending with fallback
- **Updated** `send_test_email()` to use unified system
- **Maintained** all template and helper functions

### 2. Contact Form Processing (`process-contact.php`)
- **Updated** to use unified `send_email()` function
- **Removed** dependencies on EmailSender and Mailer classes
- **Simplified** auto-reply email sending
- **Maintained** all existing functionality

### 3. User Management (`admin/users.php`)
- **Updated** verification email sending to use unified system
- **Removed** EmailSender class dependency
- **Maintained** all user creation and email functionality

### 4. SimpleMailer Class (`admin/lib/SimpleMailer.php`)
- **Enhanced** to support manual settings configuration
- **Added** `setSettings()` method for external configuration
- **Maintained** SMTP functionality for when SMTP is enabled

### 5. Database Configuration
- **Confirmed** correct setting names in `database.sql`:
  - `use_smtp` (not `smtp_enabled`)
  - `smtp_security` (not `smtp_encryption`)
  - All other SMTP settings properly configured

## Email Settings Used

### Required Settings
- `from_email` - Default sender email address
- `from_name` - Default sender name
- `use_smtp` - Enable/disable SMTP (0 = mail(), 1 = SMTP)

### SMTP Settings (when `use_smtp` = 1)
- `smtp_host` - SMTP server hostname
- `smtp_port` - SMTP server port (default: 587)
- `smtp_username` - SMTP authentication username
- `smtp_password` - SMTP authentication password
- `smtp_security` - Encryption method (tls/ssl/none)

## How It Works

### Email Routing Logic
```php
function send_email($to, $subject, $content, $attachments = []) {
    $use_smtp = get_setting('use_smtp', '0');
    
    if ($use_smtp === '1') {
        return send_email_smtp($to, $subject, $content, $attachments);
    } else {
        return send_email_mail($to, $subject, $content);
    }
}
```

### PHP Mail() Implementation
- Uses same headers as installation process
- Automatically detects HTML vs plain text content
- Includes proper MIME headers and encoding
- Uses configured `from_email` and `from_name`

### SMTP Implementation
- Uses SimpleMailer class for SMTP delivery
- Validates SMTP settings before attempting
- Falls back to PHP mail() if SMTP fails
- Supports TLS/SSL encryption

## Benefits

### 1. Consistency
- Same email approach across entire system
- Matches installation process behavior
- Unified configuration and settings

### 2. Reliability
- PHP mail() works out-of-the-box on most servers
- SMTP is optional enhancement, not requirement
- Automatic fallback prevents email failures

### 3. Simplicity
- Single `send_email()` function for all email needs
- No complex PHPMailer dependencies
- Easy to configure and troubleshoot

### 4. Flexibility
- Can switch between mail() and SMTP easily
- Supports both HTML and plain text emails
- Template system still available

## Testing

### Test File
- Created `admin/test-unified-email.php` for testing
- Tests basic email, HTML email, and template email
- Shows current configuration and results
- Accessible to admin users only

### Test Process
1. Go to `admin/test-unified-email.php`
2. Enter test email address
3. Run tests to verify functionality
4. Check email inbox for received messages

## Configuration

### Via Admin Panel
1. Go to **Settings → Email**
2. Configure basic settings (from_email, from_name)
3. Enable SMTP if desired
4. Configure SMTP settings if enabled
5. Test using the test page

### Via Database
Settings are stored in `system_settings` table with category `email`:
- `email.use_smtp`
- `email.from_email`
- `email.from_name`
- `email.smtp_host`
- `email.smtp_port`
- `email.smtp_username`
- `email.smtp_password`
- `email.smtp_security`

## Removed Components

### Classes No Longer Used
- Complex PHPMailer implementations in email-functions.php
- EmailSender class dependencies
- Mailer class dependencies for contact forms

### Files Cleaned Up
- `admin/includes/email-functions.php` - Simplified and unified
- `process-contact.php` - Updated to use unified system
- `admin/users.php` - Updated email sending

## Backward Compatibility

### Settings Migration
- Existing `use_smtp` settings are preserved
- SMTP configuration remains the same
- No database changes required

### API Compatibility
- All existing email functions still work
- Template system unchanged
- Contact form functionality preserved

## Troubleshooting

### Common Issues
1. **Emails not sending**: Check PHP mail() configuration or enable SMTP
2. **SMTP failures**: Verify SMTP settings and credentials
3. **HTML not rendering**: Ensure proper HTML structure in email content

### Debug Steps
1. Use `admin/test-unified-email.php` to test functionality
2. Check server error logs for mail() issues
3. Verify SMTP settings if using SMTP mode
4. Test with simple plain text emails first

## Future Enhancements

### Possible Improvements
- Email queue system for bulk sending
- Email templates with visual editor
- Advanced SMTP authentication methods
- Email delivery tracking and logging

### Maintenance
- Regular testing of email functionality
- Monitor server mail() configuration
- Keep SMTP credentials secure and updated
- Review email logs for delivery issues
