<?php
/**
 * Database Repair Script: Fix News Table Schema
 * 
 * This script fixes issues with the news table including:
 * - Missing slug column
 * - Missing author_id column
 * - Foreign key constraints
 * - Generates slugs for existing posts
 */

// Include configuration
require_once 'config.php';

// Set content type for proper output
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Repair - News Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        .progress { background: #e9ecef; border-radius: 4px; margin: 10px 0; }
        .progress-bar { background: #28a745; height: 20px; border-radius: 4px; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Database Repair: News Schema</h1>
        <p>This script will fix the news table schema and generate missing slugs.</p>";

$errors = [];
$success_messages = [];
$warnings = [];

try {
    // Check if we have a database connection
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    echo "<h2>📋 Checking Current Database Structure</h2>";

    // Check if news table exists
    $news_table_check = $conn->query("SHOW TABLES LIKE 'news'");
    if ($news_table_check->num_rows == 0) {
        $warnings[] = "News table does not exist. This script is not needed.";
    } else {
        echo "<div class='info'>✓ News table exists</div>";

        // Check if slug column exists
        $slug_check = $conn->query("SHOW COLUMNS FROM news LIKE 'slug'");
        
        if ($slug_check->num_rows == 0) {
            echo "<div class='warning'>⚠ slug column is missing from news table</div>";
            
            echo "<h2>🔨 Adding slug Column</h2>";
            
            // Add slug column
            $add_slug_sql = "ALTER TABLE news ADD COLUMN `slug` varchar(255) DEFAULT NULL AFTER `content`";
            
            if ($conn->query($add_slug_sql)) {
                $success_messages[] = "Successfully added slug column to news table";
                echo "<div class='success'>✓ Added slug column</div>";
                
                // Add unique index for slug
                $add_slug_index_sql = "ALTER TABLE news ADD UNIQUE KEY `slug` (`slug`)";
                
                if ($conn->query($add_slug_index_sql)) {
                    $success_messages[] = "Successfully added unique index for slug column";
                    echo "<div class='success'>✓ Added unique index for slug</div>";
                } else {
                    $warnings[] = "Could not add unique index for slug (may already exist): " . $conn->error;
                    echo "<div class='warning'>⚠ Could not add unique index (may already exist)</div>";
                }
                
            } else {
                throw new Exception("Failed to add slug column: " . $conn->error);
            }
        } else {
            echo "<div class='success'>✓ slug column already exists</div>";
            $success_messages[] = "slug column already exists in news table";
        }

        // Generate slugs for existing news posts that don't have them
        echo "<h2>🔄 Checking and Generating Slugs</h2>";
        
        $existing_news = $conn->query("SELECT id, title, slug FROM news WHERE slug IS NULL OR slug = ''");
        if ($existing_news && $existing_news->num_rows > 0) {
            $total_posts = $existing_news->num_rows;
            $updated_count = 0;
            echo "<div class='info'>Found $total_posts posts without slugs. Generating...</div>";
            
            while ($row = $existing_news->fetch_assoc()) {
                // Generate slug from title
                $slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9-]+/', '-', $row['title'])));
                $slug = trim($slug, '-'); // Remove leading/trailing hyphens
                
                // Ensure slug is not empty
                if (empty($slug)) {
                    $slug = 'post-' . $row['id'];
                }
                
                // Ensure slug is unique
                $original_slug = $slug;
                $counter = 1;
                while (true) {
                    $check_slug = $conn->prepare("SELECT id FROM news WHERE slug = ? AND id != ?");
                    $check_slug->bind_param("si", $slug, $row['id']);
                    $check_slug->execute();
                    $result = $check_slug->get_result();
                    
                    if ($result->num_rows == 0) {
                        break; // Slug is unique
                    }
                    
                    $slug = $original_slug . '-' . $counter;
                    $counter++;
                }
                
                // Update the news post with the generated slug
                $update_slug = $conn->prepare("UPDATE news SET slug = ? WHERE id = ?");
                $update_slug->bind_param("si", $slug, $row['id']);
                
                if ($update_slug->execute()) {
                    $updated_count++;
                    echo "<div class='info'>✓ Generated slug for: " . htmlspecialchars($row['title']) . " → " . htmlspecialchars($slug) . "</div>";
                } else {
                    echo "<div class='warning'>⚠ Failed to generate slug for: " . htmlspecialchars($row['title']) . "</div>";
                }
            }
            
            if ($updated_count > 0) {
                $success_messages[] = "Generated slugs for $updated_count existing news posts";
                echo "<div class='success'>✅ Generated slugs for $updated_count out of $total_posts posts</div>";
            }
        } else {
            echo "<div class='info'>✓ All existing posts already have slugs</div>";
        }

        // Check if author_id column exists
        $author_id_check = $conn->query("SHOW COLUMNS FROM news LIKE 'author_id'");
        
        if ($author_id_check->num_rows == 0) {
            echo "<div class='warning'>⚠ author_id column is missing from news table</div>";
            
            echo "<h2>🔨 Adding author_id Column</h2>";
            
            // Add author_id column
            $add_column_sql = "ALTER TABLE news ADD COLUMN `author_id` int(11) NOT NULL DEFAULT 1 AFTER `category_id`";
            
            if ($conn->query($add_column_sql)) {
                $success_messages[] = "Successfully added author_id column to news table";
                echo "<div class='success'>✓ Added author_id column</div>";
            } else {
                throw new Exception("Failed to add author_id column: " . $conn->error);
            }
            
            // Add index for author_id
            $add_index_sql = "ALTER TABLE news ADD INDEX `idx_author_id` (`author_id`)";
            
            if ($conn->query($add_index_sql)) {
                $success_messages[] = "Successfully added index for author_id column";
                echo "<div class='success'>✓ Added index for author_id</div>";
            } else {
                $warnings[] = "Could not add index for author_id (may already exist): " . $conn->error;
                echo "<div class='warning'>⚠ Could not add index (may already exist)</div>";
            }
            
            // Check if users table exists before adding foreign key
            $users_table_check = $conn->query("SHOW TABLES LIKE 'users'");
            if ($users_table_check->num_rows > 0) {
                // Add foreign key constraint
                $add_fk_sql = "ALTER TABLE news ADD CONSTRAINT `fk_news_author` FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE CASCADE";
                
                if ($conn->query($add_fk_sql)) {
                    $success_messages[] = "Successfully added foreign key constraint for author_id";
                    echo "<div class='success'>✓ Added foreign key constraint</div>";
                } else {
                    $warnings[] = "Could not add foreign key constraint (may already exist): " . $conn->error;
                    echo "<div class='warning'>⚠ Could not add foreign key constraint (may already exist)</div>";
                }
            } else {
                $warnings[] = "Users table does not exist. Foreign key constraint not added.";
                echo "<div class='warning'>⚠ Users table not found. Foreign key constraint not added.</div>";
            }
            
        } else {
            echo "<div class='success'>✓ author_id column already exists</div>";
            $success_messages[] = "author_id column already exists in news table";
        }
    }

    echo "<h2>🔍 Final Verification</h2>";
    
    // Verify the structure
    $structure_check = $conn->query("DESCRIBE news");
    if ($structure_check) {
        echo "<div class='info'>Current news table structure:</div>";
        echo "<pre>";
        while ($row = $structure_check->fetch_assoc()) {
            echo sprintf("%-15s %-20s %-10s %-10s %-15s %s\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'], 
                $row['Key'], 
                $row['Default'], 
                $row['Extra']
            );
        }
        echo "</pre>";
    }

} catch (Exception $e) {
    $errors[] = $e->getMessage();
    echo "<div class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Display summary
echo "<h2>📊 Summary</h2>";

if (!empty($success_messages)) {
    echo "<div class='success'><strong>✅ Success:</strong><ul>";
    foreach ($success_messages as $message) {
        echo "<li>" . htmlspecialchars($message) . "</li>";
    }
    echo "</ul></div>";
}

if (!empty($warnings)) {
    echo "<div class='warning'><strong>⚠ Warnings:</strong><ul>";
    foreach ($warnings as $warning) {
        echo "<li>" . htmlspecialchars($warning) . "</li>";
    }
    echo "</ul></div>";
}

if (!empty($errors)) {
    echo "<div class='error'><strong>❌ Errors:</strong><ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul></div>";
} else {
    echo "<div class='success'><strong>🎉 Repair completed successfully!</strong><br>
    You can now create and edit news articles with proper slug and author functionality.</div>";
}

echo "<p><a href='../dashboard.php'>← Back to Dashboard</a> | <a href='../create_news.php'>Create News</a> | <a href='../all_news.php'>View All News</a></p>";

echo "</div></body></html>";

// Close database connection
if ($conn) {
    $conn->close();
}
?>
