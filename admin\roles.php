<?php
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage roles
if (!$permissions->hasPermission('manage_roles')) {
    $_SESSION['error_message'] = "You do not have permission to manage roles.";
    redirect('dashboard.php');
}

// Set page title
if (isset($_GET['edit'])) {
    if ($_GET['edit'] === 'new') {
        $page_title = "Add New Role";
    } else {
        $page_title = "Edit Role";
    }
} else {
    $page_title = "Role Management";
}

// Add page-specific CSS and JS
if (isset($_GET['edit'])) {
    // Edit or Create mode
    $extra_css = '<link rel="stylesheet" href="assets/css/pages/edit-role.css?v=' . time() . '">';
    $extra_js = '<script src="js/edit-role.js?v=' . time() . '"></script>';
} else {
    // List mode
    $extra_css = '<link rel="stylesheet" href="assets/css/pages/roles-list.css?v=' . time() . '">';
}

// Helper function to sanitize strings for use in HTML attributes
function sanitize_for_attribute($string) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '-', $string);
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add/Edit Role
    if (isset($_POST['save_role'])) {
        $role_id = isset($_POST['role_id']) ? (int)$_POST['role_id'] : 0;
        $role_name = trim($_POST['role_name']);
        $role_description = trim($_POST['role_description']);
        $selected_permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];

        // Validate input
        if (empty($role_name)) {
            $error_message = "Role name is required.";
        } else {
            // Check if role name already exists (for new roles)
            if ($role_id === 0) {
                $stmt = $conn->prepare("SELECT id FROM roles WHERE name = ?");
                $stmt->bind_param("s", $role_name);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $error_message = "A role with this name already exists.";
                }
            }

            if (!isset($error_message)) {
                // Begin transaction
                $conn->begin_transaction();

                try {
                    if ($role_id === 0) {
                        // Insert new role
                        $stmt = $conn->prepare("INSERT INTO roles (name, description) VALUES (?, ?)");
                        $stmt->bind_param("ss", $role_name, $role_description);
                        $stmt->execute();
                        $role_id = $conn->insert_id;
                    } else {
                        // Update existing role
                        $stmt = $conn->prepare("UPDATE roles SET name = ?, description = ? WHERE id = ?");
                        $stmt->bind_param("ssi", $role_name, $role_description, $role_id);
                        $stmt->execute();

                        // Delete existing permissions
                        $stmt = $conn->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                        $stmt->bind_param("i", $role_id);
                        $stmt->execute();
                    }

                    // Add selected permissions
                    if (!empty($selected_permissions)) {
                        $stmt = $conn->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");

                        foreach ($selected_permissions as $permission_id) {
                            $stmt->bind_param("ii", $role_id, $permission_id);
                            $stmt->execute();
                        }
                    }

                    // Commit transaction
                    $conn->commit();

                    $success_message = "Role saved successfully.";
                } catch (Exception $e) {
                    // Rollback transaction on error
                    $conn->rollback();
                    $error_message = "Error saving role: " . $e->getMessage();
                }
            }
        }
    }

    // Delete Role
    if (isset($_POST['delete_role'])) {
        $role_id = (int)$_POST['role_id'];

        // Prevent deleting built-in roles
        $stmt = $conn->prepare("SELECT name FROM roles WHERE id = ?");
        $stmt->bind_param("i", $role_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $role = $result->fetch_assoc();

        if ($role && in_array($role['name'], ['Administrator', 'Editor', 'Contributor', 'Viewer'])) {
            $error_message = "Cannot delete built-in roles.";
        } else {
            // Begin transaction
            $conn->begin_transaction();

            try {
                // Delete role permissions
                $stmt = $conn->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                $stmt->bind_param("i", $role_id);
                $stmt->execute();

                // Delete user roles
                $stmt = $conn->prepare("DELETE FROM user_roles WHERE role_id = ?");
                $stmt->bind_param("i", $role_id);
                $stmt->execute();

                // Delete role
                $stmt = $conn->prepare("DELETE FROM roles WHERE id = ?");
                $stmt->bind_param("i", $role_id);
                $stmt->execute();

                // Commit transaction
                $conn->commit();

                $success_message = "Role deleted successfully.";
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error_message = "Error deleting role: " . $e->getMessage();
            }
        }
    }
}

// Get all roles
$roles_result = $conn->query("SELECT * FROM roles ORDER BY name");
$roles = [];
while ($row = $roles_result->fetch_assoc()) {
    $roles[] = $row;
}

// Get all permissions
$permissions_result = $conn->query("SELECT * FROM permissions ORDER BY category, name");
$all_permissions = [];
$permission_categories = [];

while ($row = $permissions_result->fetch_assoc()) {
    $all_permissions[] = $row;

    if (!isset($permission_categories[$row['category']])) {
        $permission_categories[$row['category']] = [];
    }

    $permission_categories[$row['category']][] = $row;
}

// Get role permissions for editing or creating
$edit_role = null;
$role_permissions = [];
$is_new_role = false;

if (isset($_GET['edit'])) {
    if ($_GET['edit'] === 'new') {
        // Creating a new role
        $is_new_role = true;
        $edit_role = [
            'id' => 0,
            'name' => '',
            'description' => '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => null
        ];
    } else if (is_numeric($_GET['edit'])) {
        // Editing an existing role
        $role_id = (int)$_GET['edit'];

        // Get role details
        $stmt = $conn->prepare("SELECT * FROM roles WHERE id = ?");
        $stmt->bind_param("i", $role_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $edit_role = $result->fetch_assoc();

            // Get role permissions
            $stmt = $conn->prepare("SELECT permission_id FROM role_permissions WHERE role_id = ?");
            $stmt->bind_param("i", $role_id);
            $stmt->execute();
            $result = $stmt->get_result();

            while ($row = $result->fetch_assoc()) {
                $role_permissions[] = $row['permission_id'];
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <?php
    // Set up variables for enhanced header
    $page_icon = 'fas fa-user-tag';
    $page_subtitle = 'Manage user roles and permissions. Total Roles: ' . count($roles) . ', Total Permissions: ' . count($all_permissions);

    $primary_action = ['url' => 'roles.php?edit=new', 'icon' => 'fas fa-plus-circle', 'text' => 'Add New Role'];

    // Include the content header
    include 'includes/content-header.php';
    ?>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!$edit_role): ?>
    <div class="admin-table-responsive">
        <table class="admin-table wp-style">
            <thead>
                <tr>
                    <th class="check-column">
                        <input type="checkbox" id="select-all">
                    </th>
                    <th class="title-column">Role Name</th>
                    <th>Description</th>
                    <th>Users</th>
                    <th class="actions-column">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($roles as $role): ?>
                <tr>
                    <td class="check-column">
                        <input type="checkbox" name="role_ids[]" value="<?php echo $role['id']; ?>">
                    </td>
                    <td class="title-column">
                        <div class="role-title-area">
                            <strong><?php echo htmlspecialchars($role['name']); ?></strong>
                            <div class="row-actions">
                                <span class="edit"><a href="roles.php?edit=<?php echo $role['id']; ?>">Edit</a> | </span>
                                <?php if (!in_array($role['name'], ['Administrator', 'Editor', 'Contributor', 'Viewer'])): ?>
                                <span class="trash"><a href="#" onclick="deleteRole(<?php echo $role['id']; ?>); return false;">Delete</a></span>
                                <?php else: ?>
                                <span class="view">Built-in role (cannot delete)</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </td>
                    <td><?php echo htmlspecialchars($role['description']); ?></td>
                    <td>
                        <?php
                        // Count users with this role
                        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_roles WHERE role_id = ?");
                        $stmt->bind_param("i", $role['id']);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        $count = $result->fetch_assoc()['count'];
                        echo '<span class="role-user-count">' . $count . ' ' . ($count == 1 ? 'user' : 'users') . '</span>';
                        ?>
                    </td>
                    <td class="actions-column">
                        <div class="role-actions">
                            <a href="roles.php?edit=<?php echo $role['id']; ?>" class="admin-btn" title="Edit"><i class="fas fa-edit"></i></a>
                            <?php if (!in_array($role['name'], ['Administrator', 'Editor', 'Contributor', 'Viewer'])): ?>
                            <a href="#" onclick="deleteRole(<?php echo $role['id']; ?>); return false;" class="admin-btn danger" title="Delete"><i class="fas fa-trash"></i></a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>

    <?php if ($edit_role): ?>
    <form method="post" action="roles.php" id="edit-role-form">
        <input type="hidden" name="role_id" value="<?php echo $edit_role['id']; ?>">

        <div class="role-edit-container">
            <div class="role-edit-main">
                <!-- Role Details Card -->
                <div class="role-details-card">
                    <div class="role-details-header">
                        <i class="fas fa-user-tag"></i>
                        <h3><?php echo $is_new_role ? 'New Role' : 'Role Details'; ?></h3>
                    </div>
                    <div class="role-details-body">
                        <div class="form-group">
                            <label for="role_name">Role Name</label>
                            <input type="text" id="role_name" name="role_name" class="form-control"
                                value="<?php echo htmlspecialchars($edit_role['name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="role_description">Description</label>
                            <textarea id="role_description" name="role_description" class="form-control" rows="3"><?php echo htmlspecialchars($edit_role['description']); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Permissions Card -->
                <div class="role-details-card">
                    <div class="role-details-header">
                        <i class="fas fa-key"></i>
                        <h3>Permissions</h3>
                    </div>
                    <div class="role-details-body">
                        <p class="admin-content-subtitle">Select the permissions for this role. Users with this role will be able to perform these actions.</p>

                        <div class="permission-categories">
                            <?php foreach ($permission_categories as $category => $category_permissions): ?>
                            <div class="permission-category" data-category="<?php echo sanitize_for_attribute($category); ?>">
                                <div class="permission-category-header">
                                    <h4 class="permission-category-title">
                                        <i class="fas fa-folder"></i>
                                        <?php echo htmlspecialchars($category); ?>
                                    </h4>
                                    <div class="permission-category-actions">
                                        <a href="#" class="select-all-permissions" data-category="<?php echo sanitize_for_attribute($category); ?>"
                                           onclick="toggleCategoryPermissions('<?php echo sanitize_for_attribute($category); ?>'); return false;">
                                            Toggle All
                                        </a>
                                        <i class="fas fa-chevron-down permission-category-toggle"></i>
                                    </div>
                                </div>
                                <div class="permission-category-content">
                                    <div class="permission-items">
                                        <?php foreach ($category_permissions as $permission): ?>
                                        <div class="permission-item">
                                            <div class="permission-item-header">
                                                <label class="permission-checkbox">
                                                    <input type="checkbox" name="permissions[]" value="<?php echo $permission['id']; ?>"
                                                        <?php echo in_array($permission['id'], $role_permissions) ? 'checked' : ''; ?>>
                                                    <span class="checkmark"></span>
                                                    <?php echo htmlspecialchars($permission['name']); ?>
                                                </label>
                                            </div>
                                            <div class="permission-description">
                                                <?php echo htmlspecialchars($permission['description']); ?>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="role-edit-sidebar">
                <!-- Actions Card -->
                <div class="role-actions-card">
                    <div class="role-actions-header">
                        <i class="fas fa-save"></i>
                        <h3><?php echo $is_new_role ? 'Create Role' : 'Save Changes'; ?></h3>
                    </div>
                    <div class="role-actions-body">
                        <div class="role-action-buttons">
                            <button type="submit" name="save_role" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $is_new_role ? 'Create Role' : 'Update Role'; ?>
                            </button>
                            <a href="roles.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (!$is_new_role): ?>
                <!-- Info Card -->
                <div class="role-actions-card">
                    <div class="role-actions-header">
                        <i class="fas fa-info-circle"></i>
                        <h3>Role Information</h3>
                    </div>
                    <div class="role-actions-body">
                        <div class="role-info">
                            <p><strong>Created:</strong>
                                <?php
                                    echo isset($edit_role['created_at']) ?
                                        date('M j, Y', strtotime($edit_role['created_at'])) :
                                        'Unknown';
                                ?>
                            </p>
                            <p><strong>Last Updated:</strong>
                                <?php
                                    echo isset($edit_role['updated_at']) ?
                                        date('M j, Y', strtotime($edit_role['updated_at'])) :
                                        'Never';
                                ?>
                            </p>
                            <p><strong>Users with this role:</strong>
                                <?php
                                // Count users with this role
                                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_roles WHERE role_id = ?");
                                $stmt->bind_param("i", $edit_role['id']);
                                $stmt->execute();
                                $result = $stmt->get_result();
                                $count = $result->fetch_assoc()['count'];
                                echo $count . ' ' . ($count == 1 ? 'user' : 'users');
                                ?>
                            </p>
                        </div>
                    </div>
                </div>

                <?php if (!in_array($edit_role['name'], ['Administrator', 'Editor', 'Contributor', 'Viewer'])): ?>
                <!-- Delete Card -->
                <div class="role-actions-card">
                    <div class="role-actions-header">
                        <i class="fas fa-trash"></i>
                        <h3>Delete Role</h3>
                    </div>
                    <div class="role-actions-body">
                        <p class="text-danger">Warning: This action cannot be undone.</p>
                        <div class="role-action-buttons">
                            <button type="button" class="btn btn-danger"
                                onclick="if(confirmDeleteRole(<?php echo $edit_role['id']; ?>)) {
                                    document.getElementById('delete-role-form').submit();
                                }">
                                <i class="fas fa-trash"></i> Delete Role
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <!-- Help Card for New Role -->
                <div class="role-actions-card">
                    <div class="role-actions-header">
                        <i class="fas fa-info-circle"></i>
                        <h3>Creating a New Role</h3>
                    </div>
                    <div class="role-actions-body">
                        <div class="role-info">
                            <p>Create a new role to define a set of permissions that can be assigned to users.</p>
                            <p><strong>Tips:</strong></p>
                            <ul>
                                <li>Choose a descriptive name for the role</li>
                                <li>Select only the permissions needed for this role</li>
                                <li>Group similar permissions together</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </form>

    <!-- Hidden form for delete action -->
    <form id="delete-role-form" method="post" action="roles.php" style="display: none;">
        <input type="hidden" name="role_id" value="<?php echo $edit_role['id']; ?>">
        <input type="hidden" name="delete_role" value="1">
    </form>
    <?php endif; ?>


</div>

<script>
// Function to delete a role from the list view
function deleteRole(roleId) {
    if (confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'post';
        form.action = 'roles.php';

        const roleIdInput = document.createElement('input');
        roleIdInput.type = 'hidden';
        roleIdInput.name = 'role_id';
        roleIdInput.value = roleId;

        const deleteButton = document.createElement('input');
        deleteButton.type = 'hidden';
        deleteButton.name = 'delete_role';
        deleteButton.value = '1';

        form.appendChild(roleIdInput);
        form.appendChild(deleteButton);
        document.body.appendChild(form);
        form.submit();
    }
}

// Select all checkbox functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('select-all');
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="role_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
