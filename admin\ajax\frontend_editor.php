<?php
/**
 * Frontend Editor AJAX Handler
 *
 * Handles AJAX requests for the frontend editor
 */

// Start session
session_start();

// Set content type to JSON
header('Content-Type: application/json');

// Include required files
require_once '../config.php';
require_once '../lib/Permissions.php';
require_once '../lib/FileVersions.php';
require_once '../lib/CollaborativeEditing.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to use this feature.'
    ]);
    exit;
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view files
if (!$permissions->hasPermission('view_files')) {
    echo json_encode([
        'success' => false,
        'message' => 'You do not have permission to access the Frontend Editor.'
    ]);
    exit;
}

// Initialize file versions and collaborative editing
$file_versions = new FileVersions($conn, $_SESSION['user_id']);
$collaborative = new CollaborativeEditing($conn, $_SESSION['user_id']);

// Get action
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Handle different actions
switch ($action) {
    case 'save_file':
        handleSaveFile();
        break;
    case 'acquire_lock':
        handleAcquireLock();
        break;
    case 'extend_lock':
        handleExtendLock();
        break;
    case 'release_lock':
        handleReleaseLock();
        break;
    case 'force_release_lock':
        handleForceReleaseLock();
        break;
    case 'check_lock':
        handleCheckLock();
        break;
    case 'get_version':
        handleGetVersion();
        break;
    case 'get_all_versions':
        handleGetAllVersions();
        break;
    case 'restore_version':
        handleRestoreVersion();
        break;
    case 'get_site_root':
        handleGetSiteRoot();
        break;
    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action.'
        ]);
        break;
}

/**
 * Handle save file action
 */
function handleSaveFile() {
    global $conn, $permissions, $file_versions, $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Check if user has write permission for this file
    if (!$permissions->canWriteFile($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'You do not have permission to edit this file.'
        ]);
        return;
    }

    // Get file content
    $file_content = isset($_POST['file_content']) ? $_POST['file_content'] : '';
    $comment = isset($_POST['version_comment']) ? $_POST['version_comment'] : '';

    // Check if file is locked by another user
    $lock = $collaborative->getLock($file_path);
    if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
        echo json_encode([
            'success' => false,
            'message' => "This file is currently being edited by {$lock['username']}. Please try again later."
        ]);
        return;
    }

    // Write content to file
    if (file_put_contents($file_path, $file_content) !== false) {
        // Save version history
        $file_versions->saveVersion($file_path, $file_content, $comment);

        // Extend lock
        $collaborative->extendLock($file_path);

        echo json_encode([
            'success' => true,
            'message' => 'File saved successfully.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to save file. Check file permissions.'
        ]);
    }
}

/**
 * Handle acquire lock action
 */
function handleAcquireLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Acquire lock
    $result = $collaborative->acquireLock($file_path);

    echo json_encode($result);
}

/**
 * Handle extend lock action
 */
function handleExtendLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Extend lock
    $result = $collaborative->extendLock($file_path);

    echo json_encode($result);
}

/**
 * Handle release lock action
 */
function handleReleaseLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Release lock
    $result = $collaborative->releaseLock($file_path);

    echo json_encode($result);
}

/**
 * Handle force release lock action
 */
function handleForceReleaseLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Force release lock
    $result = $collaborative->forceReleaseLock($file_path);

    echo json_encode($result);
}

/**
 * Handle check lock action
 */
function handleCheckLock() {
    global $collaborative;

    // Get file path
    $file_path = isset($_POST['file_path']) ? $_POST['file_path'] : '';

    // Validate file path
    if (empty($file_path)) {
        echo json_encode([
            'success' => false,
            'message' => 'File path is required.'
        ]);
        return;
    }

    // Check lock
    $lock = $collaborative->getLock($file_path);

    if ($lock) {
        echo json_encode([
            'success' => true,
            'locked' => true,
            'locked_by' => $lock['username'],
            'locked_by_current_user' => $lock['user_id'] == $_SESSION['user_id'],
            'expires_at' => $lock['expires_at']
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'locked' => false
        ]);
    }
}

/**
 * Handle get site root action
 */
function handleGetSiteRoot() {
    // Get the site root URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];

    // Determine the site root
    $siteRoot = $protocol . $host;

    // Check if the site is in a subdirectory
    $scriptPath = dirname(dirname($_SERVER['SCRIPT_NAME'])); // Go up two levels from ajax/frontend_editor.php
    if ($scriptPath !== '/' && $scriptPath !== '\\') {
        // Remove '/admin' from the path to get the site root
        $siteRoot .= str_replace('/admin', '', $scriptPath);
    }

    echo json_encode([
        'success' => true,
        'site_root' => $siteRoot
    ]);
}
