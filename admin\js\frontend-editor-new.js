/**
 * Frontend Editor JavaScript
 *
 * Enhances the frontend editor functionality with a modern interface
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize file browser
    initFileBrowser();

    // Initialize code editor
    initCodeEditor();

    // Initialize file selector
    initFileSelector();

    // Initialize form submission
    initFormSubmission();

    // Initialize image replacement
    initImageReplacement();

    // Initialize file versions
    initFileVersions();

    // Initialize collaborative editing
    initCollaborativeEditing();
});

/**
 * Initialize file browser functionality
 */
function initFileBrowser() {
    // Toggle folder expansion
    const folderItems = document.querySelectorAll('.file-list-folder');
    
    folderItems.forEach(folder => {
        folder.addEventListener('click', function() {
            const folderId = this.getAttribute('data-folder');
            const folderContent = document.querySelector(`.folder-content[data-folder="${folderId}"]`);
            
            if (folderContent) {
                // Toggle visibility
                if (folderContent.style.display === 'none' || !folderContent.style.display) {
                    folderContent.style.display = 'block';
                    this.querySelector('i').classList.replace('fa-folder', 'fa-folder-open');
                } else {
                    folderContent.style.display = 'none';
                    this.querySelector('i').classList.replace('fa-folder-open', 'fa-folder');
                }
            }
        });
    });

    // Highlight active file
    const currentFile = document.querySelector('input[name="file_path"]');
    if (currentFile) {
        const filePath = currentFile.value;
        const fileLinks = document.querySelectorAll('.file-list-link');
        
        fileLinks.forEach(link => {
            if (link.getAttribute('href').includes(`file=${encodeURIComponent(filePath)}`)) {
                link.classList.add('active');
                
                // Expand parent folder if needed
                const parentFolder = link.closest('.folder-content');
                if (parentFolder) {
                    parentFolder.style.display = 'block';
                    const folderId = parentFolder.getAttribute('data-folder');
                    const folderItem = document.querySelector(`.file-list-folder[data-folder="${folderId}"]`);
                    if (folderItem) {
                        folderItem.querySelector('i').classList.replace('fa-folder', 'fa-folder-open');
                    }
                }
            }
        });
    }
}

/**
 * Initialize code editor functionality
 */
function initCodeEditor() {
    const editorTextarea = document.getElementById('editor-textarea');
    
    if (editorTextarea) {
        // Set up auto-resize for textarea
        editorTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        
        // Trigger initial resize
        editorTextarea.dispatchEvent(new Event('input'));
        
        // Mark as modified when content changes
        editorTextarea.addEventListener('input', function() {
            this.dataset.modified = 'true';
        });
        
        // Tab key handling for indentation
        editorTextarea.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                e.preventDefault();
                
                const start = this.selectionStart;
                const end = this.selectionEnd;
                
                // Set textarea value to: text before caret + tab + text after caret
                this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                
                // Put caret at right position again
                this.selectionStart = this.selectionEnd = start + 4;
            }
        });
    }
}

/**
 * Initialize file selector dropdown
 */
function initFileSelector() {
    const fileSelector = document.getElementById('fileSelector');
    const fileSelectorTrigger = document.querySelector('.file-selector-trigger');
    const compactFileExplorer = document.getElementById('compactFileExplorer');
    
    if (fileSelectorTrigger && compactFileExplorer) {
        fileSelectorTrigger.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Toggle file explorer visibility
            if (compactFileExplorer.style.display === 'block') {
                compactFileExplorer.style.display = 'none';
                fileSelectorTrigger.classList.remove('active');
            } else {
                compactFileExplorer.style.display = 'block';
                fileSelectorTrigger.classList.add('active');
                
                // Focus the dropdown
                if (fileSelector) {
                    fileSelector.focus();
                }
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!compactFileExplorer.contains(e.target) &&
                !fileSelectorTrigger.contains(e.target) &&
                compactFileExplorer.style.display === 'block') {
                compactFileExplorer.style.display = 'none';
                fileSelectorTrigger.classList.remove('active');
            }
        });
        
        // Close dropdown when ESC key is pressed
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && compactFileExplorer.style.display === 'block') {
                compactFileExplorer.style.display = 'none';
                fileSelectorTrigger.classList.remove('active');
            }
        });
    }
    
    // Set the current file as selected in the dropdown
    if (fileSelector) {
        const currentFile = document.querySelector('input[name="file_path"]');
        if (currentFile) {
            const filePath = currentFile.value;
            const options = fileSelector.options;
            
            for (let i = 0; i < options.length; i++) {
                if (options[i].value.includes('file=' + encodeURIComponent(filePath))) {
                    options[i].selected = true;
                    break;
                }
            }
        }
    }
}

/**
 * Initialize form submission
 */
function initFormSubmission() {
    const editForm = document.getElementById('fileEditForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    
    if (editForm && loadingOverlay) {
        editForm.addEventListener('submit', function() {
            // Show loading overlay
            loadingOverlay.style.display = 'flex';
        });
    }
}

/**
 * Initialize image replacement
 */
function initImageReplacement() {
    const imageInput = document.getElementById('replacement_image');
    const imagePreview = document.getElementById('image_preview');
    const currentImage = document.getElementById('current_image');
    
    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                    
                    if (currentImage) {
                        currentImage.style.opacity = '0.5';
                    }
                };
                
                reader.readAsDataURL(this.files[0]);
                
                // Mark as modified
                this.dataset.modified = 'true';
            }
        });
    }
}

/**
 * Initialize file versions functionality
 */
function initFileVersions() {
    const versionLinks = document.querySelectorAll('.version-link');
    
    versionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Check if there are unsaved changes
            const editorTextarea = document.getElementById('editor-textarea');
            const imageInput = document.getElementById('replacement_image');
            
            let hasUnsavedChanges = false;
            
            if (editorTextarea && editorTextarea.dataset.modified === 'true') {
                hasUnsavedChanges = true;
            }
            
            if (imageInput && imageInput.files.length > 0) {
                hasUnsavedChanges = true;
            }
            
            if (hasUnsavedChanges) {
                if (!confirm('You have unsaved changes. Are you sure you want to load a previous version?')) {
                    return;
                }
            }
            
            // Load the version
            window.location.href = this.href;
        });
    });
}

/**
 * Initialize collaborative editing functionality
 */
function initCollaborativeEditing() {
    const unlockBtn = document.getElementById('unlockFileBtn');
    const forceUnlockBtn = document.getElementById('forceUnlockBtn');
    
    if (unlockBtn) {
        unlockBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to unlock this file? Any unsaved changes will be lost.')) {
                // Create a form to submit the unlock request
                const form = document.createElement('form');
                form.method = 'post';
                form.action = 'unlock_file.php';
                
                const filePathInput = document.createElement('input');
                filePathInput.type = 'hidden';
                filePathInput.name = 'file_path';
                filePathInput.value = document.querySelector('input[name="file_path"]').value;
                
                form.appendChild(filePathInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
    
    if (forceUnlockBtn) {
        forceUnlockBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to force unlock this file? This will remove the lock from another user.')) {
                // Create a form to submit the force unlock request
                const form = document.createElement('form');
                form.method = 'post';
                form.action = 'unlock_file.php';
                
                const filePathInput = document.createElement('input');
                filePathInput.type = 'hidden';
                filePathInput.name = 'file_path';
                filePathInput.value = document.querySelector('input[name="file_path"]').value;
                
                const forceInput = document.createElement('input');
                forceInput.type = 'hidden';
                forceInput.name = 'force';
                forceInput.value = '1';
                
                form.appendChild(filePathInput);
                form.appendChild(forceInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
}
