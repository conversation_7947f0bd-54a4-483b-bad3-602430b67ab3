<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Both Contact Forms</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .form-section {
            background: #f9f9f9;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #e0b929;
        }
        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 3px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .footer-form {
            background: #333;
            color: white;
            padding: 20px;
            border-radius: 5px;
        }
        .footer-form input, .footer-form textarea {
            background: white;
            color: #333;
        }
        .form-row {
            display: flex;
            gap: 10px;
        }
        .form-row input {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Both Contact Forms</h1>
    <p>This page tests both the main contact form and footer contact form to ensure they're both working with the clean processor.</p>

    <!-- Main Contact Form Test -->
    <div class="form-section">
        <h2>📝 Main Contact Form Test</h2>
        <p>This simulates the main contact form from contact-us.html</p>
        
        <div id="main-success-message" class="message success"></div>
        <div id="main-error-message" class="message error"></div>
        <div id="main-info-message" class="message info"></div>

        <form id="main-contact-form" action="process-contact-clean.php" method="post">
            <div class="form-group">
                <label for="main-name">Name *</label>
                <input type="text" id="main-name" name="name" value="Main Form User" required>
            </div>
            <div class="form-group">
                <label for="main-email">Email *</label>
                <input type="email" id="main-email" name="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="main-phone">Phone</label>
                <input type="tel" id="main-phone" name="phone" value="************">
            </div>
            <div class="form-group">
                <label for="main-message">Message *</label>
                <textarea id="main-message" name="message" required>This is a test message from the main contact form.</textarea>
            </div>
            <input type="hidden" name="source" value="Main Contact Form Test">
            <button type="submit">🚀 Send Main Form</button>
        </form>
    </div>

    <!-- Footer Contact Form Test -->
    <div class="form-section footer-form">
        <h2>📧 Footer Contact Form Test</h2>
        <p>This simulates the footer contact form from all pages</p>
        
        <div id="footer-success-message" class="message success"></div>
        <div id="footer-error-message" class="message error"></div>
        <div id="footer-info-message" class="message info"></div>

        <form id="footer-contact-form" action="process-contact-clean.php" method="post">
            <div class="form-row">
                <input type="text" name="name" placeholder="Name" value="Footer Form User" required>
                <input type="email" name="email" placeholder="Email Address" value="<EMAIL>" required>
            </div>
            <textarea name="message" placeholder="Message" required>This is a test message from the footer contact form.</textarea>
            <input type="hidden" name="source" value="Footer Form Test">
            <button type="submit" class="submit-btn">🚀 Send Footer Form</button>
        </form>
    </div>

    <h2>📊 Test Results</h2>
    <div id="test-results">
        <p>Submit both forms to see the results...</p>
    </div>

    <script>
        // Function to show message
        function showMessage(formType, type, text) {
            // Hide all messages for this form
            document.getElementById(formType + '-success-message').style.display = 'none';
            document.getElementById(formType + '-error-message').style.display = 'none';
            document.getElementById(formType + '-info-message').style.display = 'none';
            
            // Show the appropriate message
            const messageElement = document.getElementById(formType + '-' + type + '-message');
            if (messageElement) {
                messageElement.textContent = text;
                messageElement.style.display = 'block';
            }
        }

        // Handle main contact form
        document.getElementById('main-contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            console.log('Main form submission started');
            showMessage('main', 'info', 'Sending your message...');
            
            const formData = new FormData(this);
            
            fetch('process-contact-clean.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Main form response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Main form response:', data);
                if (data.success) {
                    showMessage('main', 'success', data.message);
                    this.reset();
                    updateTestResults('main', 'success', data.message);
                } else {
                    showMessage('main', 'error', data.message);
                    updateTestResults('main', 'error', data.message);
                }
            })
            .catch(error => {
                console.error('Main form error:', error);
                showMessage('main', 'error', 'Error: ' + error.message);
                updateTestResults('main', 'error', error.message);
            });
        });

        // Handle footer contact form
        document.getElementById('footer-contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            console.log('Footer form submission started');
            showMessage('footer', 'info', 'Sending your message...');
            
            const formData = new FormData(this);
            
            fetch('process-contact-clean.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Footer form response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Footer form response:', data);
                if (data.success) {
                    showMessage('footer', 'success', data.message);
                    this.reset();
                    updateTestResults('footer', 'success', data.message);
                } else {
                    showMessage('footer', 'error', data.message);
                    updateTestResults('footer', 'error', data.message);
                }
            })
            .catch(error => {
                console.error('Footer form error:', error);
                showMessage('footer', 'error', 'Error: ' + error.message);
                updateTestResults('footer', 'error', error.message);
            });
        });

        // Update test results
        function updateTestResults(formType, status, message) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = status === 'success' ? '✅' : '❌';
            const formName = formType === 'main' ? 'Main Contact Form' : 'Footer Contact Form';
            
            resultsDiv.innerHTML += `<p>${statusIcon} <strong>${formName}</strong> (${timestamp}): ${message}</p>`;
        }
    </script>
</body>
</html>
