<?php
/**
 * EmailSender Class
 *
 * This class handles sending emails using either PHP mail() or SMTP.
 */

// Include the EmailTemplateProcessor class
require_once __DIR__ . '/EmailTemplateProcessor.php';

class EmailSender {
    private $conn;
    private $settings;
    private $templateProcessor;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
        $this->templateProcessor = new EmailTemplateProcessor($conn);
    }

    /**
     * Load email settings from database
     */
    private function loadSettings() {
        // Get settings from system_settings table
        $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'email'";
        $result = $this->conn->query($sql);

        $this->settings = [
            'from_email' => '<EMAIL>',
            'from_name' => 'Manage Inc.',
            'reply_to' => '<EMAIL>',
            'use_smtp' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_security' => 'tls'
        ];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $this->settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        // Convert use_smtp to boolean
        $this->settings['use_smtp'] = ($this->settings['use_smtp'] == '1');
    }

    /**
     * Send an email
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param array $variables Optional variables to replace in the template
     * @param array $attachments Optional array of attachments
     * @return bool True if email was sent successfully, false otherwise
     */
    public function sendEmail($to, $subject, $message, $variables = [], $attachments = []) {
        // Log the attempt
        error_log("Attempting to send email to: $to, Subject: $subject");

        // Process the message with the template processor to add logo and consistent styling
        $processedMessage = $this->templateProcessor->processTemplate($message, $variables);

        // Try to use PHPMailer first (with SMTP or mail() transport)
        if ($this->tryLoadPHPMailer()) {
            error_log("Using PHPMailer for email delivery (SMTP: " . ($this->settings['use_smtp'] ? 'enabled' : 'disabled') . ")");
            return $this->sendWithPHPMailer($to, $subject, $processedMessage, $attachments);
        }

        // Fallback to PHP mail() function if PHPMailer is not available
        error_log("PHPMailer not available, using PHP mail() function for email delivery");
        return $this->sendWithPhpMail($to, $subject, $processedMessage);
    }

    /**
     * Try to load PHPMailer library using centralized loader
     */
    private function tryLoadPHPMailer() {
        // Include the centralized PHPMailer loader
        require_once __DIR__ . '/PHPMailerLoader.php';

        // Use the centralized loader
        return PHPMailerLoader::isAvailable();
    }

    /**
     * Send email using PHPMailer
     */
    private function sendWithPHPMailer($to, $subject, $message, $attachments = []) {
        try {
            // Include the centralized PHPMailer loader
            require_once __DIR__ . '/PHPMailerLoader.php';

            // Create a new PHPMailer instance using the centralized loader
            $mail = PHPMailerLoader::createInstance(true);

            if (!$mail) {
                throw new \Exception('Failed to create PHPMailer instance');
            }

            // Configure transport based on SMTP setting
            if ($this->settings['use_smtp']) {
                // Use SMTP transport
                error_log("Configuring PHPMailer with SMTP transport");
                $mail->isSMTP();
                $mail->Host = $this->settings['smtp_host'];
                $mail->Port = (int)$this->settings['smtp_port'];

                if (!empty($this->settings['smtp_username'])) {
                    $mail->SMTPAuth = true;
                    $mail->Username = $this->settings['smtp_username'];
                    $mail->Password = $this->settings['smtp_password'];
                }

                // Set encryption
                if ($this->settings['smtp_security'] === 'tls') {
                    $mail->SMTPSecure = 'tls';
                } elseif ($this->settings['smtp_security'] === 'ssl') {
                    $mail->SMTPSecure = 'ssl';
                } else {
                    $mail->SMTPSecure = '';
                    $mail->SMTPAutoTLS = false;
                }

                // Set timeout
                $mail->Timeout = 30;
                $mail->SMTPDebug = 0; // Disable debug output for production
            } else {
                // Use mail() transport (default PHP mail function)
                error_log("Configuring PHPMailer with mail() transport");
                $mail->isMail(); // Use PHP's mail() function
            }

            // Set sender
            $mail->setFrom($this->settings['from_email'], $this->settings['from_name']);

            // Set reply-to
            if (!empty($this->settings['reply_to'])) {
                $mail->addReplyTo($this->settings['reply_to']);
            }

            // Add recipient
            $mail->addAddress($to);

            // Set email content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $message));

            // Add attachments if any
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    if (is_array($attachment) && isset($attachment['path']) && isset($attachment['name'])) {
                        $mail->addAttachment($attachment['path'], $attachment['name']);
                    } elseif (is_string($attachment) && file_exists($attachment)) {
                        $mail->addAttachment($attachment);
                    }
                }
            }

            // Send the email
            $result = $mail->send();

            // Log the result
            if ($result) {
                error_log("Email sent successfully to $to");
                return [
                    'success' => true,
                    'message' => 'Email sent successfully!'
                ];
            } else {
                error_log("Failed to send email to $to: " . $mail->ErrorInfo);
                return [
                    'success' => false,
                    'message' => 'Failed to send email: ' . $mail->ErrorInfo
                ];
            }
        } catch (\Exception $e) {
            error_log("Exception while sending email: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception while sending email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send email using enhanced PHP mail() function
     */
    private function sendWithPhpMail($to, $subject, $message) {
        try {
            // Enhanced headers for better deliverability
            $headers = [];
            $headers[] = "MIME-Version: 1.0";
            $headers[] = "Content-Type: text/html; charset=UTF-8";
            $headers[] = "Content-Transfer-Encoding: 8bit";
            $headers[] = "From: {$this->settings['from_name']} <{$this->settings['from_email']}>";
            $headers[] = "Reply-To: {$this->settings['from_email']}";
            $headers[] = "Return-Path: {$this->settings['from_email']}";
            $headers[] = "X-Mailer: Manage Inc Email System v2.0";
            $headers[] = "X-Priority: 3";
            $headers[] = "Date: " . date('r');
            $headers[] = "Message-ID: <" . md5(uniqid(time())) . "@{$_SERVER['SERVER_NAME']}>";

            // Anti-spam headers
            $headers[] = "X-Spam-Status: No";
            $headers[] = "X-Originating-IP: " . ($_SERVER['SERVER_ADDR'] ?? 'unknown');

            // Clean subject line
            $subject = $this->cleanSubject($subject);

            // Join headers
            $headerString = implode("\r\n", $headers);

            // Additional mail parameters for better delivery
            $additional_parameters = "-f{$this->settings['from_email']}";

            // Send the email with enhanced parameters
            $result = @mail($to, $subject, $message, $headerString, $additional_parameters);

            if ($result) {
                error_log("Email sent successfully to $to using enhanced PHP mail()");
                return [
                    'success' => true,
                    'message' => 'Email sent successfully using enhanced PHP mail()!'
                ];
            } else {
                // Try alternative sending method
                return $this->tryAlternativeMailMethod($to, $subject, $message);
            }

        } catch (Exception $e) {
            error_log("Exception in sendWithPhpMail: " . $e->getMessage());
            return $this->tryAlternativeMailMethod($to, $subject, $message);
        }
    }

    /**
     * Try alternative mail sending methods
     */
    private function tryAlternativeMailMethod($to, $subject, $message) {
        // Try with minimal headers first
        try {
            $simple_headers = "From: {$this->settings['from_email']}\r\n";
            $simple_headers .= "Content-Type: text/html; charset=UTF-8\r\n";

            $result = @mail($to, $subject, $message, $simple_headers);

            if ($result) {
                error_log("Email sent successfully to $to using simple mail()");
                return [
                    'success' => true,
                    'message' => 'Email sent successfully using simple mail()!'
                ];
            }
        } catch (Exception $e) {
            error_log("Simple mail also failed: " . $e->getMessage());
        }

        // Try using sendmail directly if available
        if ($this->isSendmailAvailable()) {
            return $this->sendWithSendmail($to, $subject, $message);
        }

        // Final fallback - log the issue and provide helpful error message
        $error = error_get_last();
        $error_message = $error ? $error['message'] : 'Unknown mail error';

        error_log("All mail methods failed for $to: " . $error_message);

        return [
            'success' => false,
            'message' => $this->getHelpfulErrorMessage($error_message)
        ];
    }

    /**
     * Check if sendmail is available
     */
    private function isSendmailAvailable() {
        return function_exists('popen') && !in_array('popen', explode(',', ini_get('disable_functions')));
    }

    /**
     * Send email using sendmail directly
     */
    private function sendWithSendmail($to, $subject, $message) {
        try {
            $sendmail_path = ini_get('sendmail_path') ?: '/usr/sbin/sendmail -t -i';
            $handle = popen($sendmail_path, 'w');

            if (!$handle) {
                throw new Exception('Could not open sendmail');
            }

            $email_content = "To: $to\r\n";
            $email_content .= "From: {$this->settings['from_name']} <{$this->settings['from_email']}>\r\n";
            $email_content .= "Subject: $subject\r\n";
            $email_content .= "Content-Type: text/html; charset=UTF-8\r\n";
            $email_content .= "\r\n";
            $email_content .= $message;

            fwrite($handle, $email_content);
            $result = pclose($handle);

            if ($result === 0) {
                error_log("Email sent successfully to $to using sendmail");
                return [
                    'success' => true,
                    'message' => 'Email sent successfully using sendmail!'
                ];
            } else {
                throw new Exception("Sendmail returned error code: $result");
            }

        } catch (Exception $e) {
            error_log("Sendmail failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Sendmail failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Clean email subject to prevent header injection
     */
    private function cleanSubject($subject) {
        return str_replace(["\r", "\n", "\t"], '', trim($subject));
    }

    /**
     * Get helpful error message based on the error
     */
    private function getHelpfulErrorMessage($error_message) {
        if (strpos($error_message, "Failed to connect to mailserver") !== false) {
            return "Mail server connection failed. This usually means:\n" .
                   "1. No mail server is configured on this system\n" .
                   "2. The mail server is not running\n" .
                   "3. Firewall is blocking mail connections\n\n" .
                   "Solutions:\n" .
                   "- Enable SMTP in email settings and use an external mail service\n" .
                   "- Install and configure a local mail server\n" .
                   "- Use services like Gmail, SendGrid, or Mailgun for SMTP";
        }

        if (strpos($error_message, "relay") !== false) {
            return "Mail relay denied. Your server is configured to prevent sending external emails.\n\n" .
                   "Solutions:\n" .
                   "- Configure SMTP settings to use an external mail service\n" .
                   "- Contact your hosting provider to enable mail relay\n" .
                   "- Use a dedicated email service like SendGrid or Mailgun";
        }

        return "Email sending failed: $error_message\n\n" .
               "Common solutions:\n" .
               "1. Enable SMTP and configure external mail service\n" .
               "2. Check server mail configuration\n" .
               "3. Verify email settings in admin panel\n" .
               "4. Contact your hosting provider for mail server support";
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration($test_email = null) {
        $test_email = $test_email ?: $this->settings['from_email'];

        $subject = 'Email Configuration Test - ' . date('Y-m-d H:i:s');
        $message = "<h2>Email Configuration Test</h2>";
        $message .= "<p>This is a test email to verify your email configuration.</p>";
        $message .= "<p><strong>Server:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
        $message .= "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
        $message .= "<p><strong>Method:</strong> " . ($this->settings['use_smtp'] ? 'SMTP' : 'PHP mail()') . "</p>";
        $message .= "<p>If you received this email, your configuration is working correctly!</p>";

        return $this->sendEmail($test_email, $subject, $message);
    }
}
