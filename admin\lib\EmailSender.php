<?php
/**
 * EmailSender Class
 *
 * This class handles sending emails using either PHP mail() or SMTP.
 */

// Include the EmailTemplateProcessor class
require_once __DIR__ . '/EmailTemplateProcessor.php';

class EmailSender {
    private $conn;
    private $settings;
    private $templateProcessor;
    private $mailer;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
        $this->templateProcessor = new EmailTemplateProcessor($conn);

        // Use the new SimpleMailer instead of PHPMailer
        require_once __DIR__ . '/SimpleMailer.php';
        $this->mailer = new SimpleMailer($conn);
    }

    /**
     * Load email settings from database
     */
    private function loadSettings() {
        // Get settings from system_settings table
        $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'email'";
        $result = $this->conn->query($sql);

        $this->settings = [
            'from_email' => '<EMAIL>',
            'from_name' => 'Manage Inc.',
            'reply_to' => '<EMAIL>',
            'use_smtp' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_security' => 'tls'
        ];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $this->settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        // Convert use_smtp to boolean
        $this->settings['use_smtp'] = ($this->settings['use_smtp'] == '1');
    }

    /**
     * Send an email
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param array $variables Optional variables to replace in the template
     * @param array $attachments Optional array of attachments
     * @return bool True if email was sent successfully, false otherwise
     */
    public function sendEmail($to, $subject, $message, $variables = [], $attachments = []) {
        // Log the attempt
        error_log("Attempting to send email to: $to, Subject: $subject");

        // Process the message with the template processor to add logo and consistent styling
        $processedMessage = $this->templateProcessor->processTemplate($message, $variables);

        // Use SimpleMailer (no PHPMailer dependency)
        error_log("Using SimpleMailer for email delivery (SMTP: " . ($this->settings['use_smtp'] ? 'enabled' : 'disabled') . ")");
        return $this->mailer->send($to, $subject, $processedMessage, $attachments);
    }

    // All email sending is now handled by SimpleMailer class
    // Previous helper methods removed for simplicity

    /**
     * Test email configuration
     */
    public function testEmailConfiguration($test_email = null) {
        $test_email = $test_email ?: $this->settings['from_email'];
        return $this->mailer->testConfiguration($test_email);
    }
}
