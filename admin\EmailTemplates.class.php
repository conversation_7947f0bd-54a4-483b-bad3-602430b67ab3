<?php
/**
 * Email Templates Class
 *
 * Handles email templates management
 */
class EmailTemplates {
    private $conn;
    private $templates = [];

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeTemplates();
    }

    /**
     * Initialize templates
     */
    private function initializeTemplates() {
        // Check if email_templates table exists
        $check_table = "SHOW TABLES LIKE 'email_templates'";
        $table_result = $this->conn->query($check_table);

        if ($table_result->num_rows == 0) {
            // Table doesn't exist, create it
            $this->createTemplatesTable();
        }

        // Load templates
        $this->loadTemplates();
    }

    /**
     * Create email_templates table
     */
    private function createTemplatesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `email_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `template_key` varchar(50) NOT NULL,
            `subject` varchar(255) NOT NULL,
            `content` text NOT NULL,
            `description` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `template_key` (`template_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        $this->conn->query($sql);

        // Insert default templates
        $this->insertDefaultTemplates();
    }

    /**
     * Insert default email templates
     */
    private function insertDefaultTemplates() {
        $default_templates = [
            [
                'template_key' => 'contact_auto_reply',
                'subject' => 'Thank You for Contacting Us',
                'content' => "Dear {contact_name},\n\nThank you for contacting {company_name}. This is an automatic confirmation that we have received your message.\n\nOur team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.\n\nFor your reference, here is a copy of your message:\n\nSubject: {contact_subject}\nMessage:\n{contact_message}\n\nIf you have any urgent matters, please call us directly at our support number.\n\nBest regards,\nThe {company_name} Team\n{site_url}",
                'description' => 'Auto-reply sent to users who submit the contact form'
            ],
            [
                'template_key' => 'contact_reply',
                'subject' => 'Re: {contact_subject}',
                'content' => "Dear {contact_name},\n\nThank you for your message. I'm writing in response to your inquiry about \"{contact_subject}\".\n\n[Your response here]\n\nIf you have any further questions, please don't hesitate to contact us.\n\nBest regards,\n{admin_name}\nThe {company_name} Team\n{site_url}",
                'description' => 'Template for replying to contact form submissions'
            ],
            [
                'template_key' => 'password_reset',
                'subject' => 'Password Reset Request - {company_name}',
                'content' => "Dear {username},\n\nWe received a request to reset your password for your account ({user_email}) at {company_name}. Please click the link below to reset your password:\n\n{reset_link}\n\nThis link will expire in 24 hours.\n\nIf you did not request a password reset, please ignore this email or contact our support team if you have concerns about your account security.\n\nBest regards,\nThe {company_name} Team\n{site_url}",
                'description' => 'Email sent when a user requests a password reset'
            ],
            [
                'template_key' => 'account_verification',
                'subject' => 'Verify Your Account - {company_name}',
                'content' => "Dear {username},\n\nThank you for creating an account with {company_name}. To complete your registration, please verify your email address by clicking the link below:\n\n{verification_link}\n\nThis link will expire in 48 hours.\n\nIf you did not create an account with us, please ignore this email.\n\nBest regards,\nThe {company_name} Team\n{site_url}",
                'description' => 'Email sent to verify a new user account'
            ]
        ];

        foreach ($default_templates as $template) {
            $template_key = $this->conn->real_escape_string($template['template_key']);
            $subject = $this->conn->real_escape_string($template['subject']);
            $content = $this->conn->real_escape_string($template['content']);
            $description = $this->conn->real_escape_string($template['description']);

            $sql = "INSERT IGNORE INTO `email_templates`
                    (`template_key`, `subject`, `content`, `description`)
                    VALUES
                    ('$template_key', '$subject', '$content', '$description')";

            $this->conn->query($sql);
        }
    }

    /**
     * Load all templates
     */
    private function loadTemplates() {
        $sql = "SELECT * FROM email_templates";
        $result = $this->conn->query($sql);

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $this->templates[$row['template_key']] = $row;
            }
        }
    }

    /**
     * Get all templates
     *
     * @return array All templates
     */
    public function getAllTemplates() {
        return $this->templates;
    }

    /**
     * Get a specific template
     *
     * @param string $key Template key
     * @return array|null Template data or null if not found
     */
    public function getTemplate($key) {
        return isset($this->templates[$key]) ? $this->templates[$key] : null;
    }

    /**
     * Update a template
     *
     * @param string $key Template key
     * @param string $subject Template subject
     * @param string $content Template content
     * @param string $description Template description
     * @return bool Success status
     */
    public function updateTemplate($key, $subject, $content, $description = null) {
        // Log the template data for debugging
        error_log("EmailTemplates::updateTemplate - Key: " . $key);
        error_log("EmailTemplates::updateTemplate - Subject length: " . strlen($subject));
        error_log("EmailTemplates::updateTemplate - Content length: " . strlen($content));

        // Validate inputs
        if (empty($key)) {
            error_log("EmailTemplates::updateTemplate - Empty key");
            return false;
        }

        if (empty($subject)) {
            error_log("EmailTemplates::updateTemplate - Empty subject");
            return false;
        }

        if (empty($content)) {
            error_log("EmailTemplates::updateTemplate - Empty content");
            return false;
        }

        // Check if template exists
        if (!isset($this->templates[$key])) {
            error_log("EmailTemplates::updateTemplate - Template not found: " . $key);
            return false;
        }

        try {
            // Normalize line breaks to \n (Unix style)
            // First convert all \r\n to \n, then convert any remaining \r to \n
            $content = str_replace("\r\n", "\n", $content);
            $content = str_replace("\r", "\n", $content);

            // Log the normalized content for debugging
            error_log("EmailTemplates::updateTemplate - Normalized content length: " . strlen($content));

            // Escape strings to prevent SQL injection
            $key = $this->conn->real_escape_string($key);
            $subject = $this->conn->real_escape_string($subject);
            $content = $this->conn->real_escape_string($content);
            $description = $description ? $this->conn->real_escape_string($description) : null;

            // Build the SQL query
            $sql = "UPDATE email_templates SET
                    subject = '$subject',
                    content = '$content'";

            if ($description !== null) {
                $sql .= ", description = '$description'";
            }

            $sql .= " WHERE template_key = '$key'";

            // Log the SQL query for debugging
            error_log("EmailTemplates::updateTemplate - SQL: " . $sql);

            // Execute the query
            $result = $this->conn->query($sql);

            if ($result) {
                // Update local cache
                if (isset($this->templates[$key])) {
                    $this->templates[$key]['subject'] = $subject;
                    $this->templates[$key]['content'] = $content;
                    if ($description !== null) {
                        $this->templates[$key]['description'] = $description;
                    }
                }
                error_log("EmailTemplates::updateTemplate - Template updated successfully");
                return true;
            } else {
                error_log("EmailTemplates::updateTemplate - Database error: " . $this->conn->error);
                return false;
            }
        } catch (Exception $e) {
            error_log("EmailTemplates::updateTemplate - Exception: " . $e->getMessage());
            return false;
        }
    }
}
?>
