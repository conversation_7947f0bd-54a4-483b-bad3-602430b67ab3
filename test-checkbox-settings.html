<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Checkbox Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        
        .settings-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .settings-group {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: #f8f9fa;
        }
        
        .settings-group h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        
        /* Simple Checkbox Styling */
        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            margin: 0;
        }

        .checkbox-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin: 0;
            cursor: pointer;
            accent-color: #f1ca2f;
        }

        .checkbox-text {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            user-select: none;
        }

        .checkbox-wrapper.checked .checkbox-text {
            color: #333;
            font-weight: 600;
        }

        .checkbox-label:hover .checkbox-text {
            color: #333;
        }

        .checkbox-label input[type="checkbox"]:focus {
            outline: 2px solid #f1ca2f;
            outline-offset: 2px;
        }
        
        .setting-description {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            margin-left: 26px;
        }
        
        .test-results {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .test-results h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }
        
        .status {
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <h1>🔧 Simple Checkbox Settings Test</h1>
        <p>This page tests the new simple checkbox components that replace toggle switches.</p>
        
        <form id="test-settings-form">
            <div class="settings-group">
                <h3>📧 Email Settings</h3>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="use_smtp" value="1" id="use_smtp">
                        <span class="checkbox-text">Option disabled</span>
                    </label>
                </div>
                <div class="setting-description">Enable SMTP for email sending</div>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="email_notifications" value="1" id="email_notifications" checked>
                        <span class="checkbox-text">Option enabled</span>
                    </label>
                </div>
                <div class="setting-description">Send email notifications to admin</div>
            </div>
            
            <div class="settings-group">
                <h3>🔒 Security Settings</h3>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="enable_2fa" value="1" id="enable_2fa">
                        <span class="checkbox-text">Option disabled</span>
                    </label>
                </div>
                <div class="setting-description">Enable two-factor authentication</div>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="force_https" value="1" id="force_https" checked>
                        <span class="checkbox-text">Option enabled</span>
                    </label>
                </div>
                <div class="setting-description">Force HTTPS connections</div>
            </div>
            
            <div class="settings-group">
                <h3>🎨 Appearance Settings</h3>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="dark_mode" value="1" id="dark_mode">
                        <span class="checkbox-text">Option disabled</span>
                    </label>
                </div>
                <div class="setting-description">Enable dark mode interface</div>
                
                <div class="checkbox-wrapper">
                    <label class="checkbox-label">
                        <input type="checkbox" name="compact_layout" value="1" id="compact_layout">
                        <span class="checkbox-text">Option disabled</span>
                    </label>
                </div>
                <div class="setting-description">Use compact layout for admin panel</div>
            </div>
        </form>
        
        <div class="test-results">
            <h4>📊 Checkbox Status</h4>
            <div id="status-display">Click checkboxes to see status updates...</div>
        </div>
    </div>

    <script>
        // Simple checkbox styling and functionality
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.checkbox-wrapper input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const wrapper = this.closest('.checkbox-wrapper');
                    const textSpan = wrapper.querySelector('.checkbox-text');
                    
                    if (this.checked) {
                        wrapper.classList.add('checked');
                        textSpan.textContent = 'Option enabled';
                    } else {
                        wrapper.classList.remove('checked');
                        textSpan.textContent = 'Option disabled';
                    }
                    
                    updateStatusDisplay();
                });

                // Initialize state
                const wrapper = checkbox.closest('.checkbox-wrapper');
                const textSpan = wrapper.querySelector('.checkbox-text');
                
                if (checkbox.checked) {
                    wrapper.classList.add('checked');
                    textSpan.textContent = 'Option enabled';
                } else {
                    textSpan.textContent = 'Option disabled';
                }
            });
            
            // Initial status display
            updateStatusDisplay();
        });
        
        function updateStatusDisplay() {
            const statusDiv = document.getElementById('status-display');
            const checkboxes = document.querySelectorAll('.checkbox-wrapper input[type="checkbox"]');
            
            let statusHTML = '';
            checkboxes.forEach(checkbox => {
                const status = checkbox.checked ? '✅ Enabled' : '❌ Disabled';
                const name = checkbox.name.replace('_', ' ').toUpperCase();
                statusHTML += `<div class="status">${name}: ${status}</div>`;
            });
            
            statusDiv.innerHTML = statusHTML;
        }
    </script>
</body>
</html>
