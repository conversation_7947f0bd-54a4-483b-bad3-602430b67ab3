/**
 * Simple Editor
 * 
 * A lightweight WYSIWYG editor for the frontend editor
 */

class SimpleEditor {
    constructor(selector, options = {}) {
        this.selector = selector;
        this.options = Object.assign({
            height: 400,
            toolbar: true,
            onChange: null
        }, options);
        
        this.init();
    }
    
    init() {
        // Get the textarea
        this.textarea = document.querySelector(this.selector);
        if (!this.textarea) return;
        
        // Create editor container
        this.container = document.createElement('div');
        this.container.className = 'simple-editor-container';
        this.textarea.parentNode.insertBefore(this.container, this.textarea);
        
        // Create toolbar if enabled
        if (this.options.toolbar) {
            this.createToolbar();
        }
        
        // Create editor content area
        this.createEditorContent();
        
        // Set initial content
        this.setContent(this.textarea.value);
        
        // Set up event listeners
        this.setupEventListeners();
    }
    
    createToolbar() {
        this.toolbar = document.createElement('div');
        this.toolbar.className = 'simple-editor-toolbar';
        this.container.appendChild(this.toolbar);
        
        // Add toolbar buttons
        const buttonGroups = [
            [
                { command: 'bold', icon: 'fas fa-bold', title: 'Bold' },
                { command: 'italic', icon: 'fas fa-italic', title: 'Italic' },
                { command: 'underline', icon: 'fas fa-underline', title: 'Underline' },
                { command: 'strikethrough', icon: 'fas fa-strikethrough', title: 'Strikethrough' }
            ],
            [
                { command: 'formatBlock', value: 'h1', text: 'H1', title: 'Heading 1' },
                { command: 'formatBlock', value: 'h2', text: 'H2', title: 'Heading 2' },
                { command: 'formatBlock', value: 'h3', text: 'H3', title: 'Heading 3' },
                { command: 'formatBlock', value: 'p', text: 'P', title: 'Paragraph' }
            ],
            [
                { command: 'justifyLeft', icon: 'fas fa-align-left', title: 'Align Left' },
                { command: 'justifyCenter', icon: 'fas fa-align-center', title: 'Align Center' },
                { command: 'justifyRight', icon: 'fas fa-align-right', title: 'Align Right' },
                { command: 'justifyFull', icon: 'fas fa-align-justify', title: 'Justify' }
            ],
            [
                { command: 'insertUnorderedList', icon: 'fas fa-list-ul', title: 'Bullet List' },
                { command: 'insertOrderedList', icon: 'fas fa-list-ol', title: 'Numbered List' },
                { command: 'indent', icon: 'fas fa-indent', title: 'Indent' },
                { command: 'outdent', icon: 'fas fa-outdent', title: 'Outdent' }
            ],
            [
                { command: 'createLink', icon: 'fas fa-link', title: 'Insert Link' },
                { command: 'insertImage', icon: 'fas fa-image', title: 'Insert Image' },
                { command: 'insertTable', icon: 'fas fa-table', title: 'Insert Table' },
                { command: 'removeFormat', icon: 'fas fa-eraser', title: 'Clear Formatting' }
            ]
        ];
        
        buttonGroups.forEach(group => {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'simple-editor-toolbar-group';
            
            group.forEach(button => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'simple-editor-toolbar-button';
                btn.title = button.title;
                btn.dataset.command = button.command;
                
                if (button.value) {
                    btn.dataset.value = button.value;
                }
                
                if (button.icon) {
                    const icon = document.createElement('i');
                    icon.className = button.icon;
                    btn.appendChild(icon);
                } else if (button.text) {
                    btn.textContent = button.text;
                }
                
                btn.addEventListener('click', () => this.execCommand(button.command, button.value));
                
                groupDiv.appendChild(btn);
            });
            
            this.toolbar.appendChild(groupDiv);
        });
    }
    
    createEditorContent() {
        this.editorContent = document.createElement('div');
        this.editorContent.className = 'simple-editor-content';
        this.editorContent.contentEditable = true;
        this.editorContent.style.height = this.options.height + 'px';
        this.container.appendChild(this.editorContent);
    }
    
    setupEventListeners() {
        // Update textarea on input
        this.editorContent.addEventListener('input', () => {
            this.updateTextarea();
            
            // Call onChange callback if provided
            if (typeof this.options.onChange === 'function') {
                this.options.onChange(this.getContent());
            }
        });
        
        // Handle keyboard shortcuts
        this.editorContent.addEventListener('keydown', (e) => {
            // Ctrl+B: Bold
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                this.execCommand('bold');
            }
            
            // Ctrl+I: Italic
            if (e.ctrlKey && e.key === 'i') {
                e.preventDefault();
                this.execCommand('italic');
            }
            
            // Ctrl+U: Underline
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                this.execCommand('underline');
            }
        });
    }
    
    execCommand(command, value = null) {
        // Focus the editor
        this.editorContent.focus();
        
        // Handle special commands
        if (command === 'createLink') {
            const url = prompt('Enter the link URL:', 'https://');
            if (url) {
                document.execCommand(command, false, url);
            }
        } else if (command === 'insertImage') {
            const url = prompt('Enter the image URL:', 'images/');
            if (url) {
                document.execCommand(command, false, url);
            }
        } else if (command === 'insertTable') {
            this.insertTable();
        } else if (command === 'formatBlock') {
            document.execCommand(command, false, value);
        } else {
            // Execute standard command
            document.execCommand(command, false, null);
        }
        
        // Update textarea
        this.updateTextarea();
    }
    
    insertTable() {
        const rows = prompt('Enter number of rows:', '3');
        const cols = prompt('Enter number of columns:', '3');
        
        if (rows && cols) {
            let tableHTML = '<table border="1" style="width:100%">';
            
            // Add header row
            tableHTML += '<tr>';
            for (let i = 0; i < cols; i++) {
                tableHTML += '<th>Header ' + (i + 1) + '</th>';
            }
            tableHTML += '</tr>';
            
            // Add data rows
            for (let i = 0; i < rows - 1; i++) {
                tableHTML += '<tr>';
                for (let j = 0; j < cols; j++) {
                    tableHTML += '<td>Cell ' + (i + 1) + ',' + (j + 1) + '</td>';
                }
                tableHTML += '</tr>';
            }
            
            tableHTML += '</table>';
            
            document.execCommand('insertHTML', false, tableHTML);
        }
    }
    
    updateTextarea() {
        this.textarea.value = this.getContent();
    }
    
    getContent() {
        return this.editorContent.innerHTML;
    }
    
    setContent(content) {
        this.editorContent.innerHTML = content;
        this.updateTextarea();
    }
    
    getValue() {
        return this.textarea.value;
    }
    
    setValue(value) {
        this.textarea.value = value;
        this.setContent(value);
    }
}
