<?php
/**
 * Contact Form Handler
 *
 * This script processes contact form submissions from both the contact-us.html page
 * and the footer contact form on all pages.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to browser for AJAX
ini_set('log_errors', 1);

// Set up error handler for fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("FATAL ERROR in process-contact.php: " . print_r($error, true));

        // If this is an AJAX request, return JSON error
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if (!headers_sent()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Server error occurred. Please try again later.']);
            }
        }
    }
});

// Log the start of processing
error_log("=== CONTACT FORM PROCESSING STARTED ===");
error_log("Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN'));
error_log("Is AJAX: " . ((!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ? 'YES' : 'NO'));

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
if (!file_exists('admin/config.php')) {
    error_log("CRITICAL: admin/config.php file not found");
    $response = ['success' => false, 'message' => 'Configuration file not found'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}

try {
    require_once 'admin/config.php';
    error_log("Config loaded successfully");

    // Check if database connection exists
    if (!isset($conn) || !$conn) {
        error_log("CRITICAL: Database connection not established");
        $response = ['success' => false, 'message' => 'Database connection error'];
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
} catch (Exception $e) {
    error_log("Failed to load config: " . $e->getMessage());
    $response = ['success' => false, 'message' => 'Configuration error'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
} catch (Error $e) {
    error_log("Fatal error loading config: " . $e->getMessage());
    $response = ['success' => false, 'message' => 'Configuration error'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}

// Include unified email functions
if (!file_exists('admin/includes/email-functions.php')) {
    error_log("CRITICAL: admin/includes/email-functions.php file not found");
    $response = ['success' => false, 'message' => 'Email system file not found'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}

try {
    require_once 'admin/includes/email-functions.php';
    error_log("Email functions loaded successfully");
} catch (Exception $e) {
    error_log("Failed to load email functions: " . $e->getMessage());
    $response = ['success' => false, 'message' => 'Email system error'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
} catch (Error $e) {
    error_log("Fatal error loading email functions: " . $e->getMessage());
    $response = ['success' => false, 'message' => 'Email system error'];
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}

// Include Notifications class
try {
    require_once 'admin/lib/Notifications.php';
    error_log("Notifications class loaded successfully");
} catch (Exception $e) {
    error_log("Failed to load Notifications class: " . $e->getMessage());
    // Don't exit here as notifications are not critical
}

// Sanitize function
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while processing your request.'
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Log received POST data
    error_log("POST data received: " . print_r($_POST, true));

    // Get form data and sanitize
    $name = isset($_POST['name']) ? sanitize($_POST['name']) : '';
    $email = isset($_POST['email']) ? sanitize($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? sanitize($_POST['phone']) : '';
    $message = isset($_POST['message']) ? sanitize($_POST['message']) : '';
    $source = isset($_POST['source']) ? sanitize($_POST['source']) : 'Unknown';

    // Log sanitized data
    error_log("Sanitized data - Name: $name, Email: $email, Phone: $phone, Source: $source");

    // Validate required fields
    if (empty($name) || empty($email) || empty($message)) {
        $response['message'] = 'Please fill in all required fields.';
    }
    // Validate email format
    elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
    }
    else {
        // Get email settings for logging and recipient determination
        $settings = get_email_settings();

        // Log email configuration for debugging
        error_log("=== CONTACT FORM EMAIL CONFIGURATION ===");
        error_log("Form Source: " . $source);
        error_log("Email System: Unified Email System");
        error_log("Use SMTP: " . ($settings['use_smtp'] === '1' ? 'Yes' : 'No (using PHP mail())'));
        error_log("From Email: " . $settings['from_email']);
        error_log("From Name: " . $settings['from_name']);

        if ($settings['use_smtp'] === '1') {
            error_log("SMTP Host: " . $settings['smtp_host']);
            error_log("SMTP Port: " . $settings['smtp_port']);
            error_log("SMTP Username: " . $settings['smtp_username']);
            error_log("SMTP Security: " . $settings['smtp_security']);
        }
            // Prepare email content
            $subject = "Contact Form Submission from " . $name;

        // Create HTML message
        $htmlMessage = "
        <html>
        <head>
            <title>Contact Form Submission</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h2 { color: #3c3c45; border-bottom: 2px solid #f1ca2f; padding-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th { text-align: left; padding: 8px; background-color: #f2f2f2; }
                td { padding: 8px; border-top: 1px solid #ddd; }
                .footer { margin-top: 20px; font-size: 12px; color: #777; border-top: 1px solid #ddd; padding-top: 10px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h2>New Contact Form Submission</h2>
                <p>You have received a new message from your website contact form.</p>

                <table>
                    <tr>
                        <th>Name:</th>
                        <td>" . $name . "</td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td>" . $email . "</td>
                    </tr>";

        // Add phone if provided
        if (!empty($phone)) {
            $htmlMessage .= "
                    <tr>
                        <th>Phone:</th>
                        <td>" . $phone . "</td>
                    </tr>";
        }

        $htmlMessage .= "
                    <tr>
                        <th>Message:</th>
                        <td>" . nl2br($message) . "</td>
                    </tr>
                    <tr>
                        <th>Source:</th>
                        <td>" . $source . "</td>
                    </tr>
                    <tr>
                        <th>Date:</th>
                        <td>" . date('Y-m-d H:i:s') . "</td>
                    </tr>
                </table>

                <div class='footer'>
                    <p>This email was sent from the contact form on your website.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        // Determine recipient
        $recipient = get_setting('admin_email', '');
        if (empty($recipient)) {
            $recipient = $settings['from_email'];
            error_log("Admin email not set, using from_email as recipient: " . $recipient);
        }

        if (empty($recipient)) {
            $recipient = '<EMAIL>'; // Final fallback
            error_log("No admin_email or from_email found, using default recipient: " . $recipient);
        }

        // Send email using unified email system
        error_log("Sending contact form email using unified email system to: " . $recipient);

        try {
            $email_result = send_email($recipient, $subject, $htmlMessage);
            $result = [
                'success' => $email_result,
                'message' => $email_result ? 'Email sent successfully' : 'Failed to send email'
            ];

            error_log("Email sending result: " . ($email_result ? 'SUCCESS' : 'FAILED'));
        } catch (Exception $e) {
            error_log("Exception during email sending: " . $e->getMessage());
            $result = [
                'success' => false,
                'message' => 'Email system error: ' . $e->getMessage()
            ];
        }

        // Log the email attempt
        error_log("Footer contact form submission: " . ($result['success'] ? 'Success' : 'Failed: ' . $result['message']));

        if ($result['success']) {
            $response = [
                'success' => true,
                'message' => 'Thank you for your message. We\'ll get back to you soon!'
            ];

            // Store contact submission in database if table exists
            error_log("Attempting to store contact submission in database");
            $submission_id = storeContactSubmission($conn, $name, $email, $phone, $message, $source);
            error_log("Contact submission stored with ID: " . ($submission_id ? $submission_id : 'FAILED'));

            // Create notification for admin users
            if ($submission_id) {
                try {
                    $notifications = new Notifications($conn);

                    // Create notification title and message
                    $notification_title = "New Contact Form Submission";
                    $notification_message = "New message from $name ($email)";

                    // Get the absolute URL for the admin panel
                    $admin_url = '';
                    if (isset($_SERVER['HTTP_HOST'])) {
                        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                        $script_dir = dirname($_SERVER['SCRIPT_NAME']);
                        $admin_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . $script_dir . '/admin/';
                        error_log("Admin URL for notification: " . $admin_url);
                    }

                    // Add notification for all admin users (user_id = 0)
                    $notification_result = $notifications->addNotification(
                        0,
                        $notification_title,
                        $notification_message,
                        'info',
                        $admin_url . 'inbox.php?id=' . $submission_id
                    );

                    error_log("Notification creation result: " . ($notification_result ? 'SUCCESS' : 'FAILED'));
                } catch (Exception $e) {
                    error_log("Exception during notification creation: " . $e->getMessage());
                }
            }

            // Send auto-reply email to the submitter
            try {
                error_log("Attempting to send auto-reply email to: " . $email);
                $auto_reply_result = sendAutoReplyEmail(null, $name, $email, $message, $settings);
                error_log("Auto-reply email result: " . ($auto_reply_result ? 'SUCCESS' : 'FAILED'));
            } catch (Exception $e) {
                error_log("Exception during auto-reply email: " . $e->getMessage());
            }
        } else {
            // Store the submission in the database even if email fails
            $submission_id = storeContactSubmission($conn, $name, $email, $phone, $message, $source);

            // Log the error
            error_log("Contact form submission email failed: " . $result['message']);

            // Create notification for admin users with warning about email failure
            if ($submission_id) {
                $notifications = new Notifications($conn);

                // Create notification title and message
                $notification_title = "New Contact Form Submission (Email Failed)";
                $notification_message = "New message from $name ($email). Note: The email notification failed to send.";

                // Get the absolute URL for the admin panel if not already set
                if (empty($admin_url) && isset($_SERVER['HTTP_HOST'])) {
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
                    $admin_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . $script_dir . '/admin/';
                    error_log("Admin URL for notification (warning): " . $admin_url);
                }

                // Add notification for all admin users (user_id = 0) with warning type
                $notifications->addNotification(
                    0,
                    $notification_title,
                    $notification_message,
                    'warning',
                    $admin_url . 'inbox.php?id=' . $submission_id
                );
            }

            // Still try to send auto-reply email to the submitter even if admin notification failed
            sendAutoReplyEmail(null, $name, $email, $message, $settings);

            // Provide a user-friendly message
            $response['message'] = 'Your message has been received, but there was an issue with our email system. Our team has been notified and will contact you soon.';
        }
    }
}

/**
 * Function to send an auto-reply email to the contact form submitter
 *
 * @param EmailSender|Mailer $emailSystem The email system instance (EmailSender or Mailer)
 * @param string $name Recipient's name
 * @param string $email Recipient's email
 * @param string $message The original message from the submitter
 * @param array $settings Email settings
 * @return bool True if email was sent successfully, false otherwise
 */
function sendAutoReplyEmail($emailSystem, $name, $email, $message, $settings) {
    // Get company name from settings or use default
    $company_name = isset($settings['from_name']) ? $settings['from_name'] : 'Manage Inc.';

    // Auto-reply template subject
    $subject = "Thank You for Contacting Us";

    // Auto-reply template content
    $template = "Dear {contact_name},

Thank you for contacting {company_name}. This is an automatic confirmation that we have received your message.

Our team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.

For your reference, here is a copy of your message:

Message:
{contact_message}

If you have any urgent matters, please call us directly at our support number.

Best regards,
The {company_name} Team";

    // Replace template variables with actual values
    $content = str_replace(
        ['{contact_name}', '{company_name}', '{contact_message}'],
        [$name, $company_name, $message],
        $template
    );

    // Create HTML message
    $htmlMessage = "
    <html>
    <head>
        <title>Thank You for Contacting Us</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h2 { color: #3c3c45; border-bottom: 2px solid #f1ca2f; padding-bottom: 10px; }
            .message { background-color: #f9f9f9; padding: 15px; border-left: 4px solid #f1ca2f; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 12px; color: #777; border-top: 1px solid #ddd; padding-top: 10px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <h2>Thank You for Contacting Us</h2>
            <p>Dear " . htmlspecialchars($name) . ",</p>
            <p>Thank you for contacting " . htmlspecialchars($company_name) . ". This is an automatic confirmation that we have received your message.</p>
            <p>Our team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.</p>
            <p>For your reference, here is a copy of your message:</p>
            <div class='message'>
                " . nl2br(htmlspecialchars($message)) . "
            </div>
            <p>If you have any urgent matters, please call us directly at our support number.</p>
            <p>Best regards,<br>The " . htmlspecialchars($company_name) . " Team</p>
            <div class='footer'>
                <p>This is an automated response. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    ";

    // Log the email method being used
    if (!empty($settings['use_smtp'])) {
        error_log("Auto-reply email: SMTP " . ($settings['use_smtp'] ? 'enabled' : 'disabled'));
    }

    // Send the auto-reply email using unified email system
    $result = send_email($email, $subject, $htmlMessage);

    // Log the result
    if ($result) {
        error_log("Auto-reply email sent successfully to $email");
    } else {
        error_log("Failed to send auto-reply email to $email");
    }

    return $result;
}

// Function to store contact submission in database
function storeContactSubmission($conn, $name, $email, $phone, $message, $source) {
    // Check if contact_submissions table exists
    $check_table = "SHOW TABLES LIKE 'contact_submissions'";
    $table_result = $conn->query($check_table);

    if ($table_result->num_rows == 0) {
        // Create table if it doesn't exist
        $create_table = "CREATE TABLE `contact_submissions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `phone` varchar(50) DEFAULT NULL,
            `message` text NOT NULL,
            `source` varchar(50) NOT NULL DEFAULT 'Unknown',
            `is_read` tinyint(1) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        $conn->query($create_table);
    }

    // Insert submission using prepared statement
    $sql = "INSERT INTO `contact_submissions` (`name`, `email`, `phone`, `message`, `source`) VALUES (?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param("sssss", $name, $email, $phone, $message, $source);

    if ($stmt->execute()) {
        // Get the ID of the inserted submission
        $submission_id = $stmt->insert_id;
        $stmt->close();
        return $submission_id;
    }

    error_log("Failed to insert contact submission: " . $stmt->error);
    $stmt->close();
    return false;
}

// Return JSON response for AJAX requests
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    // Log the response for debugging
    error_log("AJAX Response: " . json_encode($response));

    echo json_encode($response);
    exit;
}

// For non-AJAX requests, redirect back with status in URL parameter
// Determine redirect URL
$redirect = 'index.html';
if (isset($_SERVER['HTTP_REFERER'])) {
    $redirect = $_SERVER['HTTP_REFERER'];
}

// Check if the source is from the main contact form or footer form
$form_param = 'form_status';
if (isset($_POST['source']) && $_POST['source'] === 'Footer Form') {
    $form_param = 'footer_form_status';
}

// Add status parameter to URL
$separator = (parse_url($redirect, PHP_URL_QUERY) == NULL) ? '?' : '&';
$status_param = $response['success'] ? 'success' : 'error';
$redirect .= $separator . $form_param . '=' . $status_param;

// Log the redirect URL
error_log("Redirecting to: " . $redirect);

// Redirect back to the form
header("Location: $redirect");
exit;
