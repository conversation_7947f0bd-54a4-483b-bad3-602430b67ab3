-- Database schema for Website Admin System
-- Created by Installation Wizard

-- Users table for admin authentication
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','editor','user') NOT NULL DEFAULT 'user',
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `verification_token` varchar(255) DEFAULT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_is_verified` (`is_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessions table for session management
CREATE TABLE IF NOT EXISTS `sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Settings table for system configuration
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL UNIQUE,
  `value` longtext,
  `type` enum('string','integer','boolean','json','text') NOT NULL DEFAULT 'string',
  `description` text,
  `group` varchar(50) DEFAULT 'general',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key` (`key`),
  KEY `idx_group` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- News/Posts table for content management
CREATE TABLE IF NOT EXISTS `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `content` longtext,
  `excerpt` text,
  `featured_image` varchar(255) DEFAULT NULL,
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `author_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `views` int(11) DEFAULT 0,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text,
  `published_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_author` (`author_id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_published_at` (`published_at`),
  FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Categories table for organizing content
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL UNIQUE,
  `description` text,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`parent_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key for news categories
ALTER TABLE `news` ADD FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL;

-- Contact submissions table
CREATE TABLE IF NOT EXISTS `contact_submissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` longtext NOT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `source` varchar(50) NOT NULL DEFAULT 'Unknown',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `status` enum('new','read','replied','archived') NOT NULL DEFAULT 'new',
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `replied_at` datetime DEFAULT NULL,
  `replied_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_email` (`email`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`replied_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Files/Media table for file management
CREATE TABLE IF NOT EXISTS `files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `file_type` enum('image','document','video','audio','other') NOT NULL DEFAULT 'other',
  `uploaded_by` int(11) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_filename` (`filename`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity log table for audit trail
CREATE TABLE IF NOT EXISTS `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO `settings` (`key`, `value`, `type`, `description`, `group`) VALUES
('site_name', 'Website Administration', 'string', 'The name of the website', 'general'),
('site_description', 'A professional website management system', 'string', 'Description of the website', 'general'),
('site_url', '', 'string', 'The base URL of the website', 'general'),
('admin_email', '', 'string', 'Administrator email address', 'general'),
('timezone', 'UTC', 'string', 'Default timezone for the application', 'general'),
('date_format', 'Y-m-d', 'string', 'Default date format', 'general'),
('time_format', 'H:i:s', 'string', 'Default time format', 'general'),
('items_per_page', '20', 'integer', 'Number of items to show per page', 'general'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 'general'),
('allow_registration', '0', 'boolean', 'Allow user registration', 'users'),
('email_verification', '1', 'boolean', 'Require email verification for new users', 'users'),
('max_login_attempts', '5', 'integer', 'Maximum login attempts before lockout', 'security'),
('lockout_duration', '15', 'integer', 'Lockout duration in minutes', 'security'),
('session_timeout', '3600', 'integer', 'Session timeout in seconds', 'security'),
('password_min_length', '8', 'integer', 'Minimum password length', 'security');

-- Notifications table for user notifications
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'info',
  `icon` varchar(50) DEFAULT 'fas fa-bell',
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- File locks table for collaborative editing
CREATE TABLE IF NOT EXISTS `file_locks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_path` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `locked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_file_path` (`file_path`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- File versions table for version control
CREATE TABLE IF NOT EXISTS `file_versions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_path` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `version` int(11) NOT NULL,
  `comment` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_file_path` (`file_path`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_version` (`version`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email templates table for email management
CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_key` varchar(100) NOT NULL UNIQUE,
  `template_name` varchar(255) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `content` text NOT NULL,
  `variables` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_template_key` (`template_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings table for advanced configuration
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` varchar(20) NOT NULL DEFAULT 'text',
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `options` text,
  `is_required` tinyint(1) NOT NULL DEFAULT 0,
  `order_index` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_setting` (`category`, `setting_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User settings table for user preferences
CREATE TABLE IF NOT EXISTS `user_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `theme` varchar(20) DEFAULT 'light',
  `sidebar_collapsed` tinyint(1) DEFAULT 0,
  `accent_color` varchar(20) DEFAULT '#f1ca2f',
  `items_per_page` int(11) DEFAULT 10,
  `date_format` varchar(20) DEFAULT 'Y-m-d',
  `time_format` varchar(20) DEFAULT 'H:i',
  `timezone` varchar(100) NOT NULL DEFAULT 'UTC',
  `language` varchar(10) NOT NULL DEFAULT 'en',
  `notifications_enabled` tinyint(1) DEFAULT 1,
  `email_notifications` tinyint(1) DEFAULT 1,
  `browser_notifications` tinyint(1) DEFAULT 1,
  `notification_sound` tinyint(1) DEFAULT 1,
  `refresh_interval` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Roles table for user role management
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL UNIQUE,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `is_system` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Permissions table for permission definitions
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL UNIQUE,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `category` varchar(50) DEFAULT 'general',
  `is_system` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User roles junction table
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assigned_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Role permissions junction table
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `granted_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- File permissions table for file-specific access control
CREATE TABLE IF NOT EXISTS `file_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_path` varchar(500) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `can_read` tinyint(1) NOT NULL DEFAULT 0,
  `can_write` tinyint(1) NOT NULL DEFAULT 0,
  `can_delete` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_file_path` (`file_path`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email settings table for SMTP configuration
CREATE TABLE IF NOT EXISTS `email_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smtp_host` varchar(255) DEFAULT NULL,
  `smtp_port` int(11) DEFAULT 587,
  `smtp_username` varchar(255) DEFAULT NULL,
  `smtp_password` varchar(255) DEFAULT NULL,
  `smtp_encryption` enum('none','ssl','tls') DEFAULT 'tls',
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `reply_to` varchar(255) DEFAULT NULL,
  `is_enabled` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User profiles table for extended user information
CREATE TABLE IF NOT EXISTS `user_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `bio` text,
  `website` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `social_links` json DEFAULT NULL,
  `preferences` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings (ignore if they already exist)
-- Note: site_name, site_description, from_email, from_name, and admin_logo will be set during installation
INSERT IGNORE INTO `settings` (`key`, `value`, `type`, `description`, `group`) VALUES
('site_url', '', 'string', 'The base URL of the website', 'general'),
('admin_email', '', 'string', 'Administrator email address', 'general'),
('timezone', 'UTC', 'string', 'Default timezone for the application', 'general'),
('date_format', 'Y-m-d', 'string', 'Default date format', 'general'),
('time_format', 'H:i:s', 'string', 'Default time format', 'general'),
('items_per_page', '20', 'integer', 'Number of items to show per page', 'general'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 'general'),
('allow_registration', '0', 'boolean', 'Allow user registration', 'users'),
('email_verification', '1', 'boolean', 'Require email verification for new users', 'users'),
('max_login_attempts', '5', 'integer', 'Maximum login attempts before lockout', 'security'),
('lockout_duration', '15', 'integer', 'Lockout duration in minutes', 'security'),
('session_timeout', '3600', 'integer', 'Session timeout in seconds', 'security'),
('password_min_length', '8', 'integer', 'Minimum password length', 'security'),
('smtp_enabled', '0', 'boolean', 'Enable SMTP email sending', 'email'),
('smtp_host', '', 'string', 'SMTP server host', 'email'),
('smtp_port', '587', 'integer', 'SMTP server port', 'email'),
('smtp_username', '', 'string', 'SMTP username', 'email'),
('smtp_password', '', 'string', 'SMTP password', 'email'),
('smtp_encryption', 'tls', 'string', 'SMTP encryption method', 'email');

-- Insert default categories (ignore if they already exist)
INSERT IGNORE INTO `categories` (`name`, `slug`, `description`, `status`) VALUES
('General', 'general', 'General category for uncategorized content', 'active'),
('News', 'news', 'Latest news and announcements', 'active'),
('Technology', 'technology', 'Technology news and updates', 'active'),
('Business', 'business', 'Business insights and market trends', 'active'),
('Cloud', 'cloud', 'Cloud computing and services', 'active'),
('Security', 'security', 'Cybersecurity news and best practices', 'active'),
('Infrastructure', 'infrastructure', 'Infrastructure and system updates', 'active'),
('Services', 'services', 'Service announcements and updates', 'active');

-- Insert default email templates (ignore if they already exist)
INSERT IGNORE INTO `email_templates` (`template_key`, `template_name`, `subject`, `content`, `variables`) VALUES
('contact_auto_reply', 'Contact Form Auto-Reply', 'Thank You for Contacting Us', 'Dear {contact_name},\n\nThank you for contacting {company_name}. This is an automatic confirmation that we have received your message.\n\nOur team will review your inquiry and get back to you as soon as possible. Please allow 1-2 business days for a response.\n\nFor your reference, here is a copy of your message:\n\nMessage:\n{contact_message}\n\nIf you have any urgent matters, please call us directly at our support number.\n\nBest regards,\nThe {company_name} Team', '{contact_name}, {company_name}, {contact_message}, {site_url}'),
('contact_notification', 'Contact Form Notification (Admin)', 'New Contact Form Submission from {contact_name}', 'You have received a new message from your website contact form.\n\nName: {contact_name}\nEmail: {contact_email}\nPhone: {contact_phone}\nMessage:\n{contact_message}\n\nSource: {contact_source}\nDate: {submission_date}\n\nPlease respond to this inquiry as soon as possible.', '{contact_name}, {contact_email}, {contact_phone}, {contact_message}, {contact_source}, {submission_date}'),
('password_reset', 'Password Reset', 'Password Reset Request - {site_name}', 'Hello {username},\n\nWe received a request to reset your password. Please click the link below to reset your password:\n\n{reset_url}\n\nThis link will expire in 1 hour.\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe {site_name} Team', '{username}, {reset_url}, {site_name}, {site_url}'),
('email_verification', 'Email Verification', 'Verify Your Account - {site_name}', 'Hello {username},\n\nThank you for creating an account with us. Please click the link below to verify your email address:\n\n{verification_url}\n\nThis link will expire in 24 hours.\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\nThe {site_name} Team', '{username}, {verification_url}, {site_name}, {site_url}');

-- Insert default roles
INSERT IGNORE INTO `roles` (`name`, `display_name`, `description`, `is_system`) VALUES
('Administrator', 'Administrator', 'Full system access with all permissions', 1),
('Editor', 'Editor', 'Can create, edit, and manage content', 1),
('Author', 'Author', 'Can create and edit own content', 1),
('Contributor', 'Contributor', 'Can create content but cannot publish', 1),
('Viewer', 'Viewer', 'Read-only access to content', 1);

-- Insert default permissions
INSERT IGNORE INTO `permissions` (`name`, `display_name`, `description`, `category`, `is_system`) VALUES
-- User Management
('manage_users', 'Manage Users', 'Create, edit, and delete user accounts', 'users', 1),
('view_users', 'View Users', 'View user accounts and profiles', 'users', 1),
('edit_users', 'Edit Users', 'Edit user accounts and profiles', 'users', 1),
('delete_users', 'Delete Users', 'Delete user accounts', 'users', 1),

-- Content Management
('manage_content', 'Manage Content', 'Full content management access', 'content', 1),
('view_content', 'View Content', 'View all content', 'content', 1),
('create_content', 'Create Content', 'Create new content', 'content', 1),
('edit_content', 'Edit Content', 'Edit existing content', 'content', 1),
('delete_content', 'Delete Content', 'Delete content', 'content', 1),
('publish_content', 'Publish Content', 'Publish and unpublish content', 'content', 1),

-- File Management
('manage_files', 'Manage Files', 'Full file management access', 'files', 1),
('view_files', 'View Files', 'View and browse files', 'files', 1),
('upload_files', 'Upload Files', 'Upload new files', 'files', 1),
('edit_files', 'Edit Files', 'Edit existing files', 'files', 1),
('delete_files', 'Delete Files', 'Delete files', 'files', 1),

-- System Settings
('manage_settings', 'Manage Settings', 'Access and modify system settings', 'settings', 1),
('view_settings', 'View Settings', 'View system settings', 'settings', 1),

-- Categories
('manage_categories', 'Manage Categories', 'Create, edit, and delete categories', 'categories', 1),
('view_categories', 'View Categories', 'View categories', 'categories', 1),

-- Dashboard and Reports
('view_dashboard', 'View Dashboard', 'Access to admin dashboard', 'dashboard', 1),
('view_reports', 'View Reports', 'Access to reports and analytics', 'reports', 1),

-- Contact Management
('manage_contacts', 'Manage Contacts', 'Manage contact submissions', 'contacts', 1),
('view_contacts', 'View Contacts', 'View contact submissions', 'contacts', 1),
('manage_inbox', 'Manage Inbox', 'Access and manage inbox/contact submissions', 'contacts', 1),

-- News Management
('manage_news', 'Manage News', 'Full news management access', 'news', 1),
('view_news', 'View News', 'View news articles', 'news', 1),
('create_news', 'Create News', 'Create new news articles', 'news', 1),
('edit_news', 'Edit News', 'Edit existing news articles', 'news', 1),
('delete_news', 'Delete News', 'Delete news articles', 'news', 1),

-- Email Settings
('manage_email_settings', 'Manage Email Settings', 'Configure email and SMTP settings', 'email', 1),
('view_email_settings', 'View Email Settings', 'View email configuration', 'email', 1),
('manage_email_templates', 'Manage Email Templates', 'Create, edit, and delete email templates', 'email', 1),
('view_email_templates', 'View Email Templates', 'View email templates', 'email', 1),
('edit_email_templates', 'Edit Email Templates', 'Edit existing email templates', 'email', 1),
('preview_email_templates', 'Preview Email Templates', 'Preview email templates', 'email', 1);

-- Assign permissions to Administrator role (all permissions)
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.name = 'Administrator';

-- Assign permissions to Editor role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.name = 'Editor' AND p.name IN (
    'view_dashboard', 'manage_content', 'view_content', 'create_content', 'edit_content', 'delete_content', 'publish_content',
    'manage_files', 'view_files', 'upload_files', 'edit_files', 'delete_files',
    'manage_categories', 'view_categories', 'view_contacts', 'manage_contacts', 'manage_inbox',
    'manage_news', 'view_news', 'create_news', 'edit_news', 'delete_news',
    'view_email_settings', 'manage_email_templates', 'view_email_templates', 'edit_email_templates', 'preview_email_templates', 'view_users'
);

-- Assign permissions to Author role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.name = 'Author' AND p.name IN (
    'view_dashboard', 'view_content', 'create_content', 'edit_content', 'publish_content',
    'view_files', 'upload_files', 'view_categories',
    'view_news', 'create_news', 'edit_news'
);

-- Assign permissions to Contributor role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.name = 'Contributor' AND p.name IN (
    'view_dashboard', 'view_content', 'create_content', 'edit_content',
    'view_files', 'upload_files', 'view_categories',
    'view_news', 'create_news'
);

-- Assign permissions to Viewer role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.name = 'Viewer' AND p.name IN (
    'view_dashboard', 'view_content', 'view_files', 'view_categories', 'view_contacts',
    'view_news', 'view_email_settings'
);

-- Insert default system settings
INSERT IGNORE INTO `system_settings` (`category`, `setting_key`, `setting_value`, `setting_type`, `display_name`, `description`, `options`, `is_required`, `order_index`) VALUES
-- General Settings
('general', 'site_name', 'Manage Inc', 'text', 'Site Name', 'The name of your website', NULL, 1, 1),
('general', 'site_description', 'Professional business management platform', 'textarea', 'Site Description', 'A brief description of your website', NULL, 0, 2),
('general', 'admin_email', '<EMAIL>', 'email', 'Admin Email', 'The main administrator email address', NULL, 1, 3),
('general', 'timezone', 'UTC', 'select', 'Timezone', 'Default timezone for the site', '{"UTC":"UTC","America/New_York":"Eastern Time","America/Chicago":"Central Time","America/Denver":"Mountain Time","America/Los_Angeles":"Pacific Time","Europe/London":"London","Europe/Paris":"Paris","Asia/Tokyo":"Tokyo","Australia/Sydney":"Sydney"}', 1, 4),
('general', 'date_format', 'Y-m-d', 'select', 'Date Format', 'Default date format', '{"Y-m-d":"YYYY-MM-DD","d-m-Y":"DD-MM-YYYY","m/d/Y":"MM/DD/YYYY","d/m/Y":"DD/MM/YYYY","F j, Y":"Month Day, Year"}', 1, 5),
('general', 'time_format', 'H:i', 'select', 'Time Format', 'Default time format', '{"H:i":"24 Hour (HH:MM)","g:i A":"12 Hour (H:MM AM/PM)"}', 1, 6),
('general', 'language', 'en', 'select', 'Default Language', 'Default language for the site', '{"en":"English","es":"Spanish","fr":"French","de":"German","it":"Italian"}', 1, 7),
('general', 'contact_email', '<EMAIL>', 'email', 'Contact Email', 'Email address for contact form submissions', NULL, 0, 21),
('general', 'contact_phone', '', 'text', 'Contact Phone', 'Phone number for contact information', NULL, 0, 22),
('general', 'contact_address', '', 'textarea', 'Contact Address', 'Physical address for contact information', NULL, 0, 23),

-- Email Settings
('email', 'from_email', '<EMAIL>', 'email', 'From Email', 'Default email address for outgoing emails', NULL, 1, 1),
('email', 'from_name', 'Manage Inc', 'text', 'From Name', 'Default name for outgoing emails', NULL, 1, 2),
('email', 'reply_to', '', 'email', 'Reply To Email', 'Email address for replies (optional)', NULL, 0, 3),
('email', 'use_smtp', '0', 'checkbox', 'Use SMTP', 'Enable SMTP for sending emails', NULL, 0, 4),
('email', 'smtp_host', '', 'text', 'SMTP Host', 'SMTP server hostname', NULL, 0, 11),
('email', 'smtp_port', '587', 'number', 'SMTP Port', 'SMTP server port (usually 587 for TLS, 465 for SSL)', NULL, 0, 12),
('email', 'smtp_security', 'tls', 'select', 'SMTP Security', 'SMTP encryption method', '{"none":"None","tls":"TLS","ssl":"SSL"}', 0, 13),
('email', 'smtp_username', '', 'text', 'SMTP Username', 'SMTP authentication username', NULL, 0, 14),
('email', 'smtp_password', '', 'password', 'SMTP Password', 'SMTP authentication password', NULL, 0, 15),

-- Appearance Settings
('appearance', 'primary_color', '#f1ca2f', 'color', 'Primary Color', 'Main color for the site theme', NULL, 0, 1),
('appearance', 'secondary_color', '#3c3c45', 'color', 'Secondary Color', 'Secondary color for the site theme', NULL, 0, 2),
('appearance', 'success_color', '#28a745', 'color', 'Success Color', 'Color for success messages and elements', NULL, 0, 3),
('appearance', 'warning_color', '#ffc107', 'color', 'Warning Color', 'Color for warning messages and elements', NULL, 0, 4),
('appearance', 'danger_color', '#dc3545', 'color', 'Danger Color', 'Color for error messages and elements', NULL, 0, 5),
('appearance', 'info_color', '#17a2b8', 'color', 'Info Color', 'Color for info messages and elements', NULL, 0, 6),
('appearance', 'admin_logo', 'images/logo.png', 'file', 'Admin Logo', 'Logo displayed in the admin panel', NULL, 0, 11),
('appearance', 'login_logo', 'images/logo.png', 'file', 'Login Logo', 'Logo displayed on login page', NULL, 0, 12),
('appearance', 'favicon', 'images/favicon.ico', 'file', 'Favicon', 'Website favicon', NULL, 0, 13),
('appearance', 'enable_dark_mode', '0', 'checkbox', 'Enable Dark Mode', 'Allow users to switch to dark mode', NULL, 0, 21),
('appearance', 'custom_css', '', 'textarea', 'Custom CSS', 'Additional CSS styles for customization', NULL, 0, 22),

-- Admin Appearance Settings (separate category for admin-specific styling)
('admin_appearance', 'admin_primary_color', '#f1ca2f', 'color', 'Admin Primary Color', 'Primary color for admin panel', NULL, 0, 1),
('admin_appearance', 'admin_secondary_color', '#3c3c45', 'color', 'Admin Secondary Color', 'Secondary color for admin panel', NULL, 0, 2),
('admin_appearance', 'admin_success_color', '#28a745', 'color', 'Admin Success Color', 'Success color for admin panel', NULL, 0, 3),
('admin_appearance', 'admin_warning_color', '#ffc107', 'color', 'Admin Warning Color', 'Warning color for admin panel', NULL, 0, 4),
('admin_appearance', 'admin_danger_color', '#dc3545', 'color', 'Admin Danger Color', 'Danger color for admin panel', NULL, 0, 5),
('admin_appearance', 'admin_logo_path', 'images/admin-logo.png', 'file', 'Admin Logo Path', 'Path to the admin panel logo', NULL, 0, 11),
('admin_appearance', 'admin_enable_dark_mode', '0', 'checkbox', 'Enable Admin Dark Mode', 'Allow dark mode in admin panel', NULL, 0, 21),

-- Font Settings
('fonts', 'google_fonts_api_key', '', 'text', 'Google Fonts API Key', 'API key for Google Fonts (optional)', NULL, 0, 1),
('fonts', 'enable_google_fonts', '0', 'checkbox', 'Enable Google Fonts', 'Enable Google Fonts integration', NULL, 0, 2),
('fonts', 'heading_font', 'Arial, sans-serif', 'text', 'Heading Font', 'Font family for headings', NULL, 0, 11),
('fonts', 'body_font', 'Arial, sans-serif', 'text', 'Body Font', 'Font family for body text', NULL, 0, 12),

-- News Settings
('news', 'news_per_page', '10', 'number', 'News Per Page', 'Number of news articles to display per page', NULL, 0, 1),
('news', 'enable_comments', '0', 'checkbox', 'Enable Comments', 'Allow comments on news articles', NULL, 0, 2),
('news', 'auto_excerpt', '1', 'checkbox', 'Auto Generate Excerpt', 'Automatically generate excerpts from content', NULL, 0, 3),
('news', 'excerpt_length', '150', 'number', 'Excerpt Length', 'Maximum length of auto-generated excerpts (characters)', NULL, 0, 4),
('news', 'enable_featured_image', '1', 'checkbox', 'Enable Featured Images', 'Allow featured images for news articles', NULL, 0, 5),
('news', 'image_max_width', '1200', 'number', 'Max Image Width', 'Maximum width for uploaded images (pixels)', NULL, 0, 6),
('news', 'image_max_height', '800', 'number', 'Max Image Height', 'Maximum height for uploaded images (pixels)', NULL, 0, 7),

-- Security Settings
('security', 'enable_csrf_protection', '1', 'checkbox', 'Enable CSRF Protection', 'Enable Cross-Site Request Forgery protection', NULL, 1, 1),
('security', 'session_timeout', '3600', 'number', 'Session Timeout', 'Session timeout in seconds (3600 = 1 hour)', NULL, 1, 2),
('security', 'max_login_attempts', '5', 'number', 'Max Login Attempts', 'Maximum failed login attempts before lockout', NULL, 1, 3),
('security', 'lockout_duration', '900', 'number', 'Lockout Duration', 'Account lockout duration in seconds (900 = 15 minutes)', NULL, 1, 4),
('security', 'password_min_length', '8', 'number', 'Minimum Password Length', 'Minimum required password length', NULL, 1, 5),
('security', 'require_strong_passwords', '1', 'checkbox', 'Require Strong Passwords', 'Require passwords to contain uppercase, lowercase, numbers, and symbols', NULL, 1, 6);

-- Sample notifications will be inserted after the icon column is added below

-- =============================================================================
-- DATABASE REPAIR AND CONSISTENCY FIXES
-- =============================================================================

-- Fix 1: Ensure notifications table has icon column
-- Check if icon column exists and add it if missing
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE()
                     AND TABLE_NAME = 'notifications'
                     AND COLUMN_NAME = 'icon');

SET @sql = IF(@column_exists = 0,
              'ALTER TABLE notifications ADD COLUMN `icon` varchar(50) DEFAULT ''fas fa-bell'' AFTER `type`',
              'SELECT ''Icon column already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Insert sample notifications for admin user (after icon column is ensured to exist)
-- Note: These will only be inserted if user ID 1 exists and notifications don't already exist
INSERT IGNORE INTO `notifications` (`user_id`, `title`, `message`, `type`, `icon`, `link`, `is_read`) VALUES
(1, 'Welcome to Admin Panel', 'Thank you for using our admin panel. Get started by exploring the dashboard.', 'info', 'fas fa-info-circle', 'dashboard.php', 0),
(1, 'System Setup Complete', 'Your website has been successfully installed and configured.', 'success', 'fas fa-check-circle', 'settings.php', 0);

-- Fix 2: Ensure news table has proper image column structure
-- Check if featured_image column exists, if not add it
SET @featured_image_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                             WHERE TABLE_SCHEMA = DATABASE()
                             AND TABLE_NAME = 'news'
                             AND COLUMN_NAME = 'featured_image');

SET @image_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'news'
                    AND COLUMN_NAME = 'image');

-- If neither column exists, add featured_image
SET @sql = IF(@featured_image_exists = 0 AND @image_exists = 0,
              'ALTER TABLE news ADD COLUMN `featured_image` varchar(255) DEFAULT NULL AFTER `excerpt`',
              'SELECT ''Image column already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If only image column exists, rename it to featured_image for consistency
SET @sql = IF(@featured_image_exists = 0 AND @image_exists = 1,
              'ALTER TABLE news CHANGE COLUMN `image` `featured_image` varchar(255) DEFAULT NULL',
              'SELECT ''No image column rename needed'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 3: Ensure all required system_settings exist
-- Insert missing email settings if they don't exist
INSERT IGNORE INTO `system_settings` (`category`, `setting_key`, `setting_value`, `setting_type`, `display_name`, `description`, `is_required`, `order_index`) VALUES
('email', 'use_smtp', '0', 'checkbox', 'Use SMTP', 'Enable SMTP for email delivery', 0, 1),
('email', 'smtp_host', '', 'text', 'SMTP Host', 'SMTP server hostname', 0, 2),
('email', 'smtp_port', '587', 'number', 'SMTP Port', 'SMTP server port', 0, 3),
('email', 'smtp_username', '', 'text', 'SMTP Username', 'SMTP authentication username', 0, 4),
('email', 'smtp_password', '', 'password', 'SMTP Password', 'SMTP authentication password', 0, 5),
('email', 'smtp_security', 'tls', 'select', 'SMTP Security', 'SMTP encryption method', 0, 6),
('email', 'from_email', '<EMAIL>', 'email', 'From Email', 'Default sender email address', 1, 7),
('email', 'from_name', 'Manage Inc.', 'text', 'From Name', 'Default sender name', 1, 8),
('email', 'reply_to', '<EMAIL>', 'email', 'Reply To Email', 'Default reply-to email address', 0, 9);

-- Insert logo settings for different page types
INSERT IGNORE INTO `system_settings` (`category`, `setting_key`, `setting_value`, `setting_type`, `display_name`, `description`, `is_required`, `order_index`) VALUES
('appearance', 'admin_logo', '', 'file', 'Admin Panel Logo', 'Logo displayed in the admin panel header', 0, 1),
('appearance', 'login_logo', '', 'file', 'Login Page Logo', 'Logo displayed on login and authentication pages', 0, 2),
('appearance', 'main_logo', '', 'file', 'Main Website Logo', 'Logo displayed on the main website pages', 0, 3),
('appearance', 'small_logo', '', 'file', 'Small Pages Logo', 'Logo for small pages like index.php, logout.php, reset-password.php', 0, 4),
('appearance', 'favicon', '', 'file', 'Favicon', 'Website favicon (ICO format recommended)', 0, 5);

-- Add SMTP security options
UPDATE `system_settings` SET `options` = '{"tls":"TLS","ssl":"SSL","none":"None"}'
WHERE `setting_key` = 'smtp_security' AND (`options` IS NULL OR `options` = '');

-- Fix 4: Ensure contact_submissions table exists
CREATE TABLE IF NOT EXISTS `contact_submissions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(50) DEFAULT NULL,
    `message` text NOT NULL,
    `source` varchar(50) NOT NULL DEFAULT 'Unknown',
    `is_read` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_email` (`email`),
    KEY `idx_is_read` (`is_read`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Fix 5: Ensure file_locks table has proper expires_at column
-- Check if expires_at column needs to be fixed for MySQL compatibility
SET @expires_at_nullable = (SELECT IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS
                           WHERE TABLE_SCHEMA = DATABASE()
                           AND TABLE_NAME = 'file_locks'
                           AND COLUMN_NAME = 'expires_at');

SET @sql = IF(@expires_at_nullable = 'NO',
              'ALTER TABLE file_locks MODIFY COLUMN `expires_at` timestamp NULL DEFAULT NULL',
              'SELECT ''Expires_at column already nullable'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 6: Ensure proper indexes exist
-- Add missing indexes if they don't exist
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'news'
                    AND INDEX_NAME = 'idx_status');

SET @sql = IF(@index_exists = 0,
              'ALTER TABLE news ADD INDEX `idx_status` (`status`)',
              'SELECT ''Status index already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add published_at index if it doesn't exist
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'news'
                    AND INDEX_NAME = 'idx_published_at');

SET @sql = IF(@index_exists = 0,
              'ALTER TABLE news ADD INDEX `idx_published_at` (`published_at`)',
              'SELECT ''Published_at index already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 7: Update existing notifications without icons
UPDATE `notifications` SET `icon` = 'fas fa-bell' WHERE `icon` IS NULL OR `icon` = '';

-- Fix 8: Ensure news table has slug column
-- Check if slug column exists and add it if missing
SET @slug_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = DATABASE()
                   AND TABLE_NAME = 'news'
                   AND COLUMN_NAME = 'slug');

SET @sql = IF(@slug_exists = 0,
              'ALTER TABLE news ADD COLUMN `slug` varchar(255) DEFAULT NULL AFTER `content`',
              'SELECT ''Slug column already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add unique index for slug if it doesn't exist
SET @slug_index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                         WHERE TABLE_SCHEMA = DATABASE()
                         AND TABLE_NAME = 'news'
                         AND INDEX_NAME = 'slug');

SET @sql = IF(@slug_index_exists = 0,
              'ALTER TABLE news ADD UNIQUE KEY `slug` (`slug`)',
              'SELECT ''Slug index already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 9: Ensure news table has author_id column
-- Check if author_id column exists and add it if missing
SET @author_id_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'news'
                        AND COLUMN_NAME = 'author_id');

SET @sql = IF(@author_id_exists = 0,
              'ALTER TABLE news ADD COLUMN `author_id` int(11) NOT NULL DEFAULT 1 AFTER `category_id`',
              'SELECT ''Author_id column already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for author_id if it doesn't exist
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'news'
                    AND INDEX_NAME = 'idx_author_id');

SET @sql = IF(@index_exists = 0,
              'ALTER TABLE news ADD INDEX `idx_author_id` (`author_id`)',
              'SELECT ''Author_id index already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 10: Ensure proper foreign key constraints exist
-- Add foreign key for news.author_id if it doesn't exist
SET @fk_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                 WHERE TABLE_SCHEMA = DATABASE()
                 AND TABLE_NAME = 'news'
                 AND CONSTRAINT_NAME = 'fk_news_author');

SET @sql = IF(@fk_exists = 0,
              'ALTER TABLE news ADD CONSTRAINT `fk_news_author` FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE CASCADE',
              'SELECT ''News author foreign key already exists'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
