<?php
/**
 * Test Include Files
 * This script tests if all required files can be included without errors
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Testing Include Files</h1>";

// Test 1: Check if files exist
echo "<h2>📁 File Existence Check</h2>";

$files_to_check = [
    'admin/config.php',
    'admin/includes/email-functions.php',
    'admin/lib/Notifications.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p>✅ <strong>$file</strong> - EXISTS</p>";
    } else {
        echo "<p>❌ <strong>$file</strong> - NOT FOUND</p>";
    }
}

// Test 2: Try to include config
echo "<h2>⚙️ Config Include Test</h2>";
try {
    if (file_exists('admin/config.php')) {
        require_once 'admin/config.php';
        echo "<p>✅ Config loaded successfully</p>";
        
        // Test database connection
        if (isset($conn) && $conn) {
            echo "<p>✅ Database connection established</p>";
            echo "<p>Database info: " . $conn->server_info . "</p>";
        } else {
            echo "<p>❌ Database connection not established</p>";
        }
    } else {
        echo "<p>❌ Config file not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception loading config: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal error loading config: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Try to include email functions
echo "<h2>📧 Email Functions Include Test</h2>";
try {
    if (file_exists('admin/includes/email-functions.php')) {
        require_once 'admin/includes/email-functions.php';
        echo "<p>✅ Email functions loaded successfully</p>";
        
        // Test if functions exist
        if (function_exists('send_email')) {
            echo "<p>✅ send_email() function available</p>";
        } else {
            echo "<p>❌ send_email() function not found</p>";
        }
        
        if (function_exists('get_email_settings')) {
            echo "<p>✅ get_email_settings() function available</p>";
            
            // Test getting email settings
            try {
                $settings = get_email_settings();
                echo "<p>✅ Email settings retrieved successfully</p>";
                echo "<pre>" . htmlspecialchars(print_r($settings, true)) . "</pre>";
            } catch (Exception $e) {
                echo "<p>❌ Error getting email settings: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p>❌ get_email_settings() function not found</p>";
        }
    } else {
        echo "<p>❌ Email functions file not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception loading email functions: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal error loading email functions: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Try to include Notifications
echo "<h2>🔔 Notifications Include Test</h2>";
try {
    if (file_exists('admin/lib/Notifications.php')) {
        require_once 'admin/lib/Notifications.php';
        echo "<p>✅ Notifications class loaded successfully</p>";
        
        // Test if class exists
        if (class_exists('Notifications')) {
            echo "<p>✅ Notifications class available</p>";
            
            // Test creating instance
            if (isset($conn) && $conn) {
                try {
                    $notifications = new Notifications($conn);
                    echo "<p>✅ Notifications instance created successfully</p>";
                } catch (Exception $e) {
                    echo "<p>❌ Error creating Notifications instance: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                echo "<p>⚠️ Cannot test Notifications instance - no database connection</p>";
            }
        } else {
            echo "<p>❌ Notifications class not found</p>";
        }
    } else {
        echo "<p>❌ Notifications file not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception loading Notifications: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal error loading Notifications: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Test basic contact form processing
echo "<h2>📝 Basic Contact Form Processing Test</h2>";

if (isset($conn) && function_exists('send_email')) {
    echo "<p>✅ Prerequisites met for contact form processing</p>";
    
    // Test sanitize function
    function test_sanitize($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    // Test form data processing
    $test_data = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '************',
        'message' => 'This is a test message',
        'source' => 'Include Test'
    ];
    
    echo "<p>✅ Test data prepared:</p>";
    echo "<pre>" . htmlspecialchars(print_r($test_data, true)) . "</pre>";
    
    // Test email settings
    try {
        $settings = get_email_settings();
        $recipient = get_setting('admin_email', '');
        if (empty($recipient)) {
            $recipient = $settings['from_email'];
        }
        if (empty($recipient)) {
            $recipient = '<EMAIL>';
        }
        
        echo "<p>✅ Email recipient determined: " . htmlspecialchars($recipient) . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error determining email recipient: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} else {
    echo "<p>❌ Prerequisites not met for contact form processing</p>";
    if (!isset($conn)) echo "<p>  - Database connection missing</p>";
    if (!function_exists('send_email')) echo "<p>  - send_email() function missing</p>";
}

// Test 6: PHP Version and Extensions
echo "<h2>🐘 PHP Environment</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Extensions:</strong></p>";
$required_extensions = ['mysqli', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ $ext extension loaded</p>";
    } else {
        echo "<p>❌ $ext extension NOT loaded</p>";
    }
}

echo "<h2>📋 Summary</h2>";
echo "<p>If all tests show ✅, then the includes should work properly in process-contact.php</p>";
echo "<p>If any tests show ❌, those issues need to be resolved first.</p>";

?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
p { margin: 10px 0; }
</style>
