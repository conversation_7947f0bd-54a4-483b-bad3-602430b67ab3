<?php
// Get current page filename
$current_page = basename($_SERVER['PHP_SELF']);

// Define submenu items for different sections
$news_submenu = [
    ['url' => 'all_news.php', 'icon' => 'fas fa-list', 'text' => 'All News', 'mobile_text' => 'All'],
    ['url' => 'create_news.php', 'icon' => 'fas fa-plus-circle', 'text' => 'Add News', 'mobile_text' => 'Add'],
    ['url' => 'categories.php', 'icon' => 'fas fa-folder', 'text' => 'Categories', 'mobile_text' => 'Categories']
];

// Define frontend editor submenu
$frontend_editor_submenu = [
    ['url' => 'frontend_editor.php?dir=../', 'icon' => 'fas fa-home', 'text' => 'Root', 'mobile_text' => 'Root'],
    ['url' => 'frontend_editor.php?dir=../images/', 'icon' => 'fas fa-images', 'text' => 'Images', 'mobile_text' => 'Images'],
    ['url' => 'frontend_editor.php?dir=../css/', 'icon' => 'fas fa-paint-brush', 'text' => 'CSS', 'mobile_text' => 'CSS']
];

// Define HTML editor submenu
$html_editor_submenu = [
    ['url' => 'html_editor.php', 'icon' => 'fas fa-file-code', 'text' => 'Editor', 'mobile_text' => 'Editor'],
    ['url' => 'html_editor.php?dir=../', 'icon' => 'fas fa-home', 'text' => 'Root', 'mobile_text' => 'Root']
];

// Define settings submenu
$settings_submenu = [
    ['url' => 'settings.php', 'icon' => 'fas fa-cog', 'text' => 'System Settings', 'mobile_text' => 'Settings'],
    ['url' => 'email_templates.php', 'icon' => 'fas fa-envelope', 'text' => 'Email Templates', 'mobile_text' => 'Templates']
];

// Handle edit_news.php - mark all_news.php as active
$active_override = null;
if ($current_page == 'edit_news.php') {
    $active_override = 'all_news.php';
}

// Determine which submenu to show based on current page
$show_submenu = false;
$submenu_items = [];
$submenu_title = '';

if (in_array($current_page, ['all_news.php', 'create_news.php', 'edit_news.php', 'categories.php'])) {
    $show_submenu = true;
    $submenu_items = $news_submenu;
    $submenu_title = 'News';
} elseif ($current_page == 'frontend_editor.php') {
    $show_submenu = true;
    $submenu_items = $frontend_editor_submenu;
    $submenu_title = 'Frontend Editor';
} elseif ($current_page == 'html_editor.php') {
    $show_submenu = true;
    $submenu_items = $html_editor_submenu;
    $submenu_title = 'HTML Editor';
} elseif (in_array($current_page, ['settings.php', 'email_templates.php'])) {
    $show_submenu = true;
    $submenu_items = $settings_submenu;
    $submenu_title = 'Settings';
}

// Only show submenu if we have items to display
if ($show_submenu && !empty($submenu_items)):
?>
<div class="admin-submenu" id="adminSubmenu">
    <div class="admin-submenu-container">
        <div class="admin-submenu-items">
            <?php foreach ($submenu_items as $item):
                $is_active = ($active_override && $active_override == $item['url']) ||
                             (!$active_override && $current_page == $item['url']);
            ?>
                <a href="<?php echo $item['url']; ?>" class="admin-submenu-item <?php echo $is_active ? 'active' : ''; ?>">
                    <i class="<?php echo $item['icon']; ?>"></i>
                    <span class="desktop-text"><?php echo $item['text']; ?></span>
                    <span class="mobile-text"><?php echo $item['mobile_text']; ?></span>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
