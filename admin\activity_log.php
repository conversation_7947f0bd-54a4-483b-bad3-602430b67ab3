<?php
session_start();
require_once 'config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM activity_log WHERE user_id = ?";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->bind_param("i", $user_id);
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get activity log entries
$sql = "SELECT * FROM activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iii", $user_id, $per_page, $offset);
$stmt->execute();
$result = $stmt->get_result();
$activities = $result->fetch_all(MYSQLI_ASSOC);

// Function to get activity icon
function getActivityIcon($action) {
    switch (strtolower($action)) {
        case 'login':
            return 'fas fa-sign-in-alt';
        case 'logout':
            return 'fas fa-sign-out-alt';
        case 'create':
            return 'fas fa-plus-circle';
        case 'update':
        case 'edit':
            return 'fas fa-edit';
        case 'delete':
            return 'fas fa-trash';
        case 'view':
            return 'fas fa-eye';
        case 'upload':
            return 'fas fa-upload';
        case 'download':
            return 'fas fa-download';
        default:
            return 'fas fa-info-circle';
    }
}

// Function to get activity color
function getActivityColor($action) {
    switch (strtolower($action)) {
        case 'login':
            return 'success';
        case 'logout':
            return 'info';
        case 'create':
            return 'primary';
        case 'update':
        case 'edit':
            return 'warning';
        case 'delete':
            return 'danger';
        case 'view':
            return 'info';
        case 'upload':
        case 'download':
            return 'secondary';
        default:
            return 'light';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Log - Admin Panel</title>
    <link rel="stylesheet" href="styles/admin_main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-content-header">
                <div class="admin-content-title-group">
                    <h2 class="admin-content-title">
                        <i class="fas fa-history"></i>
                        Activity Log
                    </h2>
                    <p class="admin-content-subtitle">View your recent activities and actions</p>
                </div>
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 class="admin-card-title">
                        <i class="fas fa-list"></i>
                        Recent Activities
                    </h3>
                    <div class="admin-card-actions">
                        <span class="badge badge-info"><?php echo $total_records; ?> total entries</span>
                    </div>
                </div>
                <div class="admin-card-body">
                    <?php if (empty($activities)): ?>
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <h3>No Activity Found</h3>
                            <p>Your activity log is empty. Start using the admin panel to see your activities here.</p>
                        </div>
                    <?php else: ?>
                        <div class="activity-timeline">
                            <?php foreach ($activities as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon <?php echo getActivityColor($activity['action']); ?>">
                                        <i class="<?php echo getActivityIcon($activity['action']); ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-header">
                                            <h4 class="activity-title"><?php echo htmlspecialchars($activity['description']); ?></h4>
                                            <span class="activity-time"><?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?></span>
                                        </div>
                                        <?php if (!empty($activity['details'])): ?>
                                            <p class="activity-details"><?php echo htmlspecialchars($activity['details']); ?></p>
                                        <?php endif; ?>
                                        <div class="activity-meta">
                                            <span class="activity-action"><?php echo ucfirst($activity['action']); ?></span>
                                            <?php if (!empty($activity['ip_address'])): ?>
                                                <span class="activity-ip">IP: <?php echo htmlspecialchars($activity['ip_address']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if ($total_pages > 1): ?>
                            <div class="pagination-wrapper">
                                <nav class="pagination">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?php echo $page - 1; ?>" class="pagination-link">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <a href="?page=<?php echo $i; ?>" class="pagination-link <?php echo $i === $page ? 'active' : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <a href="?page=<?php echo $page + 1; ?>" class="pagination-link">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </nav>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="js/alert-auto-dismiss.js"></script>
</body>
</html>
