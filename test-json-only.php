<?php
// Simple JSON-only test endpoint
// This file tests if basic JSON responses work

// Start output buffering
ob_start();

// Clear any existing output
while (ob_get_level()) {
    ob_end_clean();
}

// Start fresh
ob_start();

// Set headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Simple test response
$response = [
    'success' => true,
    'message' => 'JSON test successful',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
    'is_ajax' => !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
];

// If POST data exists, include it
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
    $response['post_data'] = $_POST;
}

// Output JSON and exit
echo json_encode($response, JSON_PRETTY_PRINT);
exit;
?>
