<?php
/**
 * Email Templates Table Repair Script
 * This script ensures the email_templates table has the correct structure
 */

// Include necessary files
require_once 'config.php';

// Check if user is logged in and is admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: index.php');
    exit;
}

$messages = [];

// Check if email_templates table exists
$check_table = "SHOW TABLES LIKE 'email_templates'";
$result = $conn->query($check_table);

if ($result->num_rows == 0) {
    // Create email_templates table
    $create_table = "
    CREATE TABLE `email_templates` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `template_key` varchar(100) NOT NULL,
        `name` varchar(255) NOT NULL,
        `subject` varchar(255) NOT NULL,
        `content` text NOT NULL,
        `variables` text,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `template_key` (`template_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    if ($conn->query($create_table)) {
        $messages[] = "✅ Created email_templates table successfully.";
    } else {
        $messages[] = "❌ Failed to create email_templates table: " . $conn->error;
    }
} else {
    $messages[] = "ℹ️ Email templates table already exists.";
}

// Check table structure
$columns = $conn->query("SHOW COLUMNS FROM email_templates");
$existing_columns = [];
while ($row = $columns->fetch_assoc()) {
    $existing_columns[] = $row['Field'];
}

// Add missing columns if needed
$required_columns = [
    'template_key' => "ADD COLUMN `template_key` varchar(100) NOT NULL AFTER `id`",
    'name' => "ADD COLUMN `name` varchar(255) NOT NULL AFTER `template_key`",
    'subject' => "ADD COLUMN `subject` varchar(255) NOT NULL AFTER `name`",
    'content' => "ADD COLUMN `content` text NOT NULL AFTER `subject`",
    'variables' => "ADD COLUMN `variables` text AFTER `content`"
];

foreach ($required_columns as $column => $sql) {
    if (!in_array($column, $existing_columns)) {
        if ($conn->query("ALTER TABLE email_templates $sql")) {
            $messages[] = "✅ Added missing column: $column";
        } else {
            $messages[] = "❌ Failed to add column $column: " . $conn->error;
        }
    }
}

// Insert default email templates if they don't exist
$default_templates = [
    [
        'template_key' => 'contact-form',
        'name' => 'Contact Form Notification',
        'subject' => 'New Contact Form Submission from {{contact_name}}',
        'content' => "Hello,\n\nYou have received a new message from your website contact form.\n\nName: {{contact_name}}\nEmail: {{contact_email}}\nSubject: {{contact_subject}}\n\nMessage:\n{{contact_message}}\n\nBest regards,\n{{site_name}}",
        'variables' => '{{contact_name}}, {{contact_email}}, {{contact_subject}}, {{contact_message}}, {{site_name}}'
    ],
    [
        'template_key' => 'welcome',
        'name' => 'Welcome Email',
        'subject' => 'Welcome to {{site_name}}!',
        'content' => "Dear {{user_name}},\n\nWelcome to {{site_name}}! Your account has been created successfully.\n\nYou can now log in using the following link:\n{{login_link}}\n\nThank you for joining us!\n\nBest regards,\nThe {{site_name}} Team",
        'variables' => '{{user_name}}, {{user_email}}, {{login_link}}, {{site_name}}'
    ],
    [
        'template_key' => 'email-verification',
        'name' => 'Email Verification',
        'subject' => 'Verify Your Email - {{site_name}}',
        'content' => "Dear {{user_name}},\n\nThank you for creating an account with {{site_name}}. Please click the link below to verify your email address:\n\n{{verification_link}}\n\nThis link will expire in 24 hours.\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\nThe {{site_name}} Team",
        'variables' => '{{user_name}}, {{user_email}}, {{verification_link}}, {{site_name}}'
    ],
    [
        'template_key' => 'password-reset',
        'name' => 'Password Reset',
        'subject' => 'Password Reset Request - {{site_name}}',
        'content' => "Dear {{user_name}},\n\nYou have requested to reset your password for {{site_name}}. Please click the link below to reset your password:\n\n{{reset_link}}\n\nThis link will expire in 1 hour.\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe {{site_name}} Team",
        'variables' => '{{user_name}}, {{user_email}}, {{reset_link}}, {{site_name}}'
    ],
    [
        'template_key' => 'notification',
        'name' => 'General Notification',
        'subject' => '{{notification_subject}}',
        'content' => "{{notification_message}}\n\nBest regards,\n{{site_name}}",
        'variables' => '{{notification_subject}}, {{notification_message}}, {{site_name}}'
    ]
];

foreach ($default_templates as $template) {
    // Check if template already exists
    $check_stmt = $conn->prepare("SELECT id FROM email_templates WHERE template_key = ?");
    $check_stmt->bind_param("s", $template['template_key']);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows == 0) {
        // Insert template
        $insert_stmt = $conn->prepare("INSERT INTO email_templates (template_key, name, subject, content, variables) VALUES (?, ?, ?, ?, ?)");
        $insert_stmt->bind_param("sssss", $template['template_key'], $template['name'], $template['subject'], $template['content'], $template['variables']);
        
        if ($insert_stmt->execute()) {
            $messages[] = "✅ Added email template: " . $template['name'];
        } else {
            $messages[] = "❌ Failed to add template " . $template['name'] . ": " . $conn->error;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Templates Repair</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f1ca2f;
            color: #333;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            display: inline-block;
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 3px;
            font-weight: bold;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #e0b929;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Email Templates Repair</h1>
        <p>This script ensures the email_templates table has the correct structure and default templates.</p>
    </div>

    <h2>📋 Repair Results</h2>
    <?php foreach ($messages as $message): ?>
        <?php
        $class = 'info';
        if (strpos($message, '✅') !== false) $class = 'success';
        if (strpos($message, '❌') !== false) $class = 'error';
        ?>
        <div class="message <?php echo $class; ?>"><?php echo htmlspecialchars($message); ?></div>
    <?php endforeach; ?>

    <h2>🔗 Next Steps</h2>
    <p>The email templates table has been repaired. You can now:</p>
    <ul>
        <li>Test the unified email system</li>
        <li>Manage email templates in the admin panel</li>
        <li>Use template-based emails throughout the system</li>
    </ul>

    <div>
        <a href="test-unified-email.php" class="btn">🧪 Test Email System</a>
        <a href="email_templates.php" class="btn">📧 Manage Templates</a>
        <a href="dashboard.php" class="btn">🏠 Dashboard</a>
    </div>
</body>
</html>
