<?php
/**
 * Frontend Editor
 *
 * A clean, modern editor for HTML files with support for:
 * - Content extraction and injection
 * - Version history
 * - Collaborative editing
 * - Image handling
 */

// Include required files
require_once 'config.php';
require_once 'includes/security.php';
require_once 'lib/Permissions.php';
require_once 'lib/FileVersions.php';
require_once 'lib/CollaborativeEditing.php';
require_once 'lib/ContentExtractor.php';

// Define a constant to indicate this is the frontend editor
define('IS_FRONTEND_EDITOR', true);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view files
if (!$permissions->hasPermission('view_files')) {
    $_SESSION['error_message'] = "You do not have permission to access the Frontend Editor.";
    redirect('dashboard.php');
}

// Initialize file versions and collaborative editing
$file_versions = new FileVersions($conn, $_SESSION['user_id']);
$collaborative = new CollaborativeEditing($conn, $_SESSION['user_id']);

// Set page title and metadata
$page_title = "Frontend Editor";
$page_subtitle = "Edit HTML files and manage website content";
$page_icon = "fas fa-code";

// Add page-specific CSS
$extra_css = '<link rel="stylesheet" href="assets/css/pages/frontend-editor.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/pages/frontend-editor-fix.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/components/frontend-editor-header.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/components/admin_content-header.css?v=' . time() . '">';

// Add meta tags for no caching and to allow external resources
$extra_head = '
<!-- Ensure no caching for dynamic content -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<!-- Allow external resources for the editor -->
<meta http-equiv="Content-Security-Policy" content="default-src * \'unsafe-inline\' \'unsafe-eval\'; img-src * data: blob:; script-src * \'unsafe-inline\' \'unsafe-eval\'; style-src * \'unsafe-inline\';">
';

// Add page-specific JavaScript
$extra_js = '<script src="js/editor.js?v=' . time() . '"></script>
<script src="js/file-versions.js?v=' . time() . '"></script>
<script src="js/collaborative-editing.js?v=' . time() . '"></script>
<script src="js/frontend-editor.js?v=' . time() . '"></script>';

// Define allowed directories and file extensions
$allowed_dirs = [
    '../' => 'Root'
];

// Only allow HTML files for editing
$allowed_extensions = [
    'html' => 'HTML Files'
];

// Define prohibited files (header and footer)
$prohibited_files = [
    '../header.html',
    '../footer.html',
    './header.html',
    './footer.html',
    'header.html',
    'footer.html'
];

// Initialize variables
$current_dir = isset($_GET['dir']) ? $_GET['dir'] : '../';
$current_file = isset($_GET['file']) ? $_GET['file'] : '';
$file_content = '';
$file_type = '';
$success_message = '';
$error_message = '';
$lock_message = '';
$is_locked_by_other = false;

// Validate current directory
if (!array_key_exists($current_dir, $allowed_dirs)) {
    $current_dir = '../';
}

// Helper functions
function isHeaderOrFooterFile($filename) {
    $basename = strtolower(basename($filename));
    return $basename === 'header.html' || $basename === 'footer.html';
}

/**
 * Check if an HTML file contains dynamic content
 *
 * @param string $filepath Path to the HTML file
 * @return bool True if the file contains dynamic content, false otherwise
 */
function containsDynamicContent($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }

    $content = file_get_contents($filepath);

    // Check for PHP tags
    if (strpos($content, '<?php') !== false ||
        strpos($content, '<?=') !== false ||
        strpos($content, '<%') !== false) {
        return true;
    }

    // Check for JavaScript dynamic content patterns
    $js_patterns = [
        // AJAX calls
        '/\$.ajax\s*\(/i',
        '/fetch\s*\(/i',
        '/XMLHttpRequest/i',
        // Dynamic data loading
        '/\.load\s*\(/i',
        '/\.getJSON\s*\(/i',
        // URL parameters
        '/window\.location\.search/i',
        '/URLSearchParams/i',
        '/new\s+URL\s*\(/i',
        // Common dynamic content markers
        '/\{\{\s*.*?\s*\}\}/i',  // Handlebars/Mustache style
        '/\{\%\s*.*?\s*\%\}/i',  // Liquid/Jekyll style
        '/\[\[\s*.*?\s*\]\]/i',  // Angular style
        // PHP-like variable syntax in JavaScript
        '/\$_GET/i',
        '/\$_POST/i',
        '/\$_REQUEST/i'
    ];

    foreach ($js_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            return true;
        }
    }

    return false;
}

/**
 * Get HTML files from a directory recursively
 *
 * @param string $dir Directory to scan
 * @param string $base_url Base URL for links
 * @param array $processed_dirs Array of already processed directories to prevent duplicates
 * @return array Updated array of processed directories
 */
function getHtmlFiles($dir, $base_url, $processed_dirs = []) {
    // Skip if already processed
    if (in_array($dir, $processed_dirs)) {
        return $processed_dirs;
    }

    // Add to processed list
    $processed_dirs[] = $dir;

    // Check if directory exists
    if (!is_dir($dir)) {
        return $processed_dirs;
    }

    // Get directory contents
    $items = scandir($dir);

    // Process each item
    foreach ($items as $item) {
        // Skip . and ..
        if ($item === '.' || $item === '..') {
            continue;
        }

        $path = $dir . '/' . $item;

        // Skip admin folder
        if (is_dir($path) && (strtolower($item) === 'admin' || strpos(strtolower($path), '/admin') !== false)) {
            continue;
        }

        // If it's a directory, process it recursively
        if (is_dir($path)) {
            $processed_dirs = getHtmlFiles($path, $base_url, $processed_dirs);
        }
        // If it's an HTML file, add it to the dropdown
        elseif (strtolower(pathinfo($item, PATHINFO_EXTENSION)) === 'html') {
            // Skip header and footer files
            if (isHeaderOrFooterFile($item)) {
                continue;
            }

            // Skip dynamic HTML files
            if (containsDynamicContent($path)) {
                continue;
            }

            // Create option
            echo '<option value="' . $base_url . '?file=' . urlencode($path) . '"';

            // Mark as selected if it's the current file
            if (isset($GLOBALS['current_file']) && $GLOBALS['current_file'] === $path) {
                echo ' selected';
            }

            echo '>' . htmlspecialchars($path) . '</option>';
        }
    }

    return $processed_dirs;
}

// Handle file save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_file'])) {
    $file_path = $_POST['file_path'];

    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($file_path, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    // Check if file is prohibited (header or footer)
    if (in_array($file_path, $prohibited_files) || isHeaderOrFooterFile($file_path)) {
        $error_message = "This file is protected and cannot be edited.";
    }
    else if (!$valid_path) {
        $error_message = "Invalid file path.";
    } else {
        // Get file extension
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if file extension is allowed (only HTML)
        if ($file_extension !== 'html') {
            $error_message = "Only HTML files can be edited in this interface.";
        }
        // Check if file contains dynamic content
        else if (containsDynamicContent($file_path)) {
            $error_message = "This file contains dynamic content and cannot be edited in this interface.";
        } else {
            // Handle file content update
            if (isset($_POST['file_content'])) {
                // Check if user has write permission for this file
                if (!$permissions->canWriteFile($file_path)) {
                    $error_message = "You do not have permission to edit this file.";
                } else {
                    $new_content = $_POST['file_content'];
                    $comment = isset($_POST['version_comment']) ? $_POST['version_comment'] : '';

                    // Check if file is locked by another user
                    $lock = $collaborative->getLock($file_path);
                    if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                        $error_message = "This file is currently being edited by {$lock['username']}. Please try again later.";
                    } else {
                        // For HTML files, we need to inject the edited content back into the original structure
                        $content_to_save = $new_content;

                        if ($file_extension === 'html') {
                            // Get the original HTML structure from session
                            $original_html = isset($_SESSION['original_html_' . md5($file_path)])
                                ? $_SESSION['original_html_' . md5($file_path)]
                                : '';

                            // If we have the original HTML, inject the edited content
                            if (!empty($original_html)) {
                                // Use our ContentExtractor class to handle the injection
                                $content_to_save = ContentExtractor::injectContent($original_html, $new_content);
                            }
                        }

                        // Write content to file
                        if (file_put_contents($file_path, $content_to_save) !== false) {
                            // Save version history
                            $file_versions->saveVersion($file_path, $content_to_save, $comment);

                            // Release lock if it exists
                            if ($lock && $lock['user_id'] == $_SESSION['user_id']) {
                                $collaborative->releaseLock($file_path);
                            }

                            $success_message = "File saved successfully.";
                            $current_file = $file_path;
                            $file_content = $new_content;
                        } else {
                            $error_message = "Failed to save file. Check file permissions.";
                        }
                    }
                }
            }
        }
    }
}

// Get file content if a file is selected
if (!empty($current_file)) {
    // Validate file path
    $valid_path = false;
    foreach (array_keys($allowed_dirs) as $dir) {
        if (strpos($current_file, $dir) === 0) {
            $valid_path = true;
            break;
        }
    }

    // Check if file is prohibited (header or footer)
    if (in_array($current_file, $prohibited_files) || isHeaderOrFooterFile($current_file)) {
        $error_message = "This file is protected and cannot be edited.";
        $current_file = '';
    }
    // Check if file is not HTML
    else if (strtolower(pathinfo($current_file, PATHINFO_EXTENSION)) !== 'html') {
        $error_message = "Only HTML files can be edited in this interface.";
        $current_file = '';
    }
    // Check if file contains dynamic content
    else if (containsDynamicContent($current_file)) {
        $error_message = "This file contains dynamic content and cannot be edited in this interface.";
        $current_file = '';
    }
    else if ($valid_path && file_exists($current_file)) {
        // Check if user has permission to read this file
        if (!$permissions->canReadFile($current_file)) {
            $error_message = "You do not have permission to access this file.";
            $current_file = '';
        } else {
            // Get file content
            $original_html = file_get_contents($current_file);
            $file_type = strtolower(pathinfo($current_file, PATHINFO_EXTENSION));

            // Extract only the content area from HTML files
            if ($file_type === 'html') {
                // Store the original HTML for later use when saving
                $_SESSION['original_html_' . md5($current_file)] = $original_html;
                // Extract only the content area
                $file_content = ContentExtractor::extractContent($original_html);
            } else {
                $file_content = $original_html;
            }

            // Check if file is locked by another user
            $lock = $collaborative->getLock($current_file);
            if ($lock && $lock['user_id'] != $_SESSION['user_id']) {
                $lock_message = "This file is currently being edited by {$lock['username']}. You can view it, but you cannot save changes.";
                $is_locked_by_other = true;
            } else {
                // Acquire lock for editing
                if (!$lock && $permissions->canWriteFile($current_file)) {
                    $collaborative->acquireLock($current_file);
                }
                $is_locked_by_other = false;
            }

            // Get file versions
            if ($permissions->hasPermission('manage_file_versions')) {
                $file_versions_list = $file_versions->getAllVersions($current_file);
            }
        }
    } else {
        $error_message = "Invalid file or file does not exist.";
        $current_file = '';
    }
}

// Get files in current directory
$files = [];
if (is_dir($current_dir)) {
    $dir_contents = scandir($current_dir);
    foreach ($dir_contents as $item) {
        if ($item != '.' && $item != '..') {
            $full_path = $current_dir . $item;
            $is_dir = is_dir($full_path);
            $extension = $is_dir ? '' : strtolower(pathinfo($item, PATHINFO_EXTENSION));

            // Skip prohibited files (header and footer)
            if (in_array($full_path, $prohibited_files) || isHeaderOrFooterFile($full_path)) {
                continue;
            }

            // Only include directories and HTML files
            if ($is_dir || $extension === 'html') {
                $files[] = [
                    'name' => $item,
                    'path' => $full_path,
                    'is_dir' => $is_dir,
                    'extension' => $extension,
                    'size' => $is_dir ? '' : filesize($full_path),
                    'modified' => date("Y-m-d H:i:s", filemtime($full_path))
                ];
            }
        }
    }
}

// Sort files: directories first, then files
usort($files, function($a, $b) {
    if ($a['is_dir'] && !$b['is_dir']) {
        return -1;
    } elseif (!$a['is_dir'] && $b['is_dir']) {
        return 1;
    } else {
        return strcasecmp($a['name'], $b['name']);
    }
});

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <?php
    // Set up variables for enhanced header
    $page_icon = 'fas fa-code';
    $page_description = 'Edit frontend HTML files and manage website content';

    // Include the frontend editor header
    include 'includes/frontend-editor-header.php';
    ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible" id="successAlert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Success</div>
                <div class="alert-text"><?php echo $success_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <script>
            // Auto-hide success message after 5 seconds
            setTimeout(function() {
                const alert = document.getElementById('successAlert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                }
            }, 5000);
        </script>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible" id="errorAlert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">Error</div>
                <div class="alert-text"><?php echo $error_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <?php if (!empty($lock_message)): ?>
        <div class="alert alert-warning alert-dismissible" id="lockAlert">
            <div class="alert-icon">
                <i class="fas fa-lock"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">File Locked</div>
                <div class="alert-text"><?php echo $lock_message; ?></div>
            </div>
            <button type="button" class="alert-dismiss" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>

    <div class="admin-content">
        <div class="row">
            <!-- Editor Section -->
            <div class="col-md-12">
                <?php if (!empty($current_file)): ?>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i>
                                Editing: <?php echo htmlspecialchars(basename($current_file)); ?>
                            </h5>
                            <div class="card-actions">
                                <?php if ($permissions->hasPermission('manage_file_versions')): ?>
                                    <button type="button" id="showVersionsBtn" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-history"></i> Version History
                                    </button>
                                <?php endif; ?>

                                <?php if ($is_locked_by_other): ?>
                                    <?php if ($permissions->hasPermission('force_unlock_files')): ?>
                                        <button type="button" id="forceUnlockBtn" class="btn btn-sm btn-warning">
                                            <i class="fas fa-unlock"></i> Force Unlock
                                        </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <button type="button" id="releaseLockBtn" class="btn btn-sm btn-success" style="display: none;">
                                        <i class="fas fa-unlock"></i> Release Lock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <form id="editorForm" method="post" action="frontend_editor.php">
                                <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($current_file); ?>">

                                <div class="editor-container">
                                    <div class="editor-toolbar">
                                        <!-- Editor toolbar will be inserted by JavaScript -->
                                    </div>

                                    <div class="editor-content-wrapper">
                                        <div class="editor-content wysiwyg-editor" id="editorContent" contenteditable="true">
                                            <?php echo $file_content; ?>
                                        </div>
                                    </div>
                                </div>

                                <textarea name="file_content" id="fileContent" style="display: none;"><?php echo htmlspecialchars($file_content); ?></textarea>

                                <script>
                                    // Ensure content is properly synced with the hidden textarea
                                    document.getElementById('editorForm').addEventListener('submit', function() {
                                        const editorContent = document.getElementById('editorContent');
                                        const fileContent = document.getElementById('fileContent');
                                        fileContent.value = editorContent.innerHTML;
                                    });

                                    // Handle editor input
                                    document.getElementById('editorContent').addEventListener('input', function() {
                                        const fileContent = document.getElementById('fileContent');
                                        fileContent.value = this.innerHTML;
                                        window.hasContentChanged = true;

                                        // Show the release lock button when content changes
                                        const releaseLockBtn = document.getElementById('releaseLockBtn');
                                        if (releaseLockBtn) {
                                            releaseLockBtn.style.display = 'inline-block';
                                        }
                                    });
                                </script>

                                <div class="card-footer d-flex justify-content-between align-items-center">
                                    <div class="version-comment">
                                        <input type="text" name="version_comment" class="form-control" placeholder="Version comment (optional)">
                                    </div>
                                    <div class="editor-actions">
                                        <button type="button" class="btn btn-secondary" onclick="window.location.href='frontend_editor.php'">
                                            <i class="fas fa-times"></i> Cancel
                                        </button>
                                        <?php if (!$is_locked_by_other): ?>
                                            <button type="submit" name="save_file" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Save Changes
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-code fa-4x text-muted mb-3"></i>
                                <h3>No File Selected</h3>
                                <p class="text-muted">Please select an HTML file from the file browser to edit its content.</p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Version History Modal -->
    <div id="versionHistoryModal" class="modal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Version History</h5>
                    <button type="button" id="closeVersionsBtn" class="close">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <?php if (isset($file_versions_list) && !empty($file_versions_list)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Version</th>
                                        <th>Date</th>
                                        <th>User</th>
                                        <th>Comment</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($file_versions_list as $version): ?>
                                        <tr>
                                            <td><?php echo $version['version']; ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', strtotime($version['created_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($version['username']); ?></td>
                                            <td><?php echo htmlspecialchars($version['comment'] ?: 'No comment'); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary view-version-btn" data-version="<?php echo $version['version']; ?>">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-success restore-version-btn" data-version="<?php echo $version['version']; ?>">
                                                    <i class="fas fa-undo"></i> Restore
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No version history available for this file.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Version Preview Modal -->
    <div id="versionPreviewModal" class="modal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Version Preview</h5>
                    <button type="button" id="closePreviewBtn" class="close">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="version-info mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Version:</strong> <span id="previewVersion"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>Date:</strong> <span id="previewDate"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>User:</strong> <span id="previewUser"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>Comment:</strong> <span id="previewComment"></span>
                            </div>
                        </div>
                    </div>
                    <div class="version-preview">
                        <pre id="previewContent" class="code-preview"></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="closePreviewFooterBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Close
                    </button>
                    <button type="button" id="restorePreviewBtn" class="btn btn-success">
                        <i class="fas fa-undo"></i> Restore This Version
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" style="display: none;">
        <div class="spinner-container">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Hidden iframe for preloading images -->
    <iframe id="preloadFrame" style="display: none;"></iframe>

    <script>
        // Set current file path for JavaScript
        const currentFilePath = <?php echo json_encode($current_file); ?>;
    </script>
</div>

<?php include 'includes/footer.php'; ?>