<?php
session_start();
require_once 'config.php';
require_once 'lib/Mailer.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    redirect('dashboard.php');
}

$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email']);

    // Validate input
    if (empty($email)) {
        $error = "Please enter your email address";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Please enter a valid email address";
    } else {
        // Check if email exists
        $sql = "SELECT id, username FROM users WHERE email = '$email'";
        $result = $conn->query($sql);

        if ($result->num_rows == 1) {
            $user = $result->fetch_assoc();

            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Update user with reset token
            $update_sql = "UPDATE users SET reset_token = '$token', reset_token_expires = '$expires' WHERE id = " . $user['id'];

            if ($conn->query($update_sql) === TRUE) {
                // Initialize mailer
                $mailer = new Mailer($conn);

                // Send password reset email
                $result = $mailer->sendPasswordResetEmail($email, $user['username'], $token);

                if ($result['success']) {
                    $success = "Password reset instructions have been sent to your email address.";
                } else {
                    $error = "There was a problem sending the password reset email. Please try again later.";
                }
            } else {
                $error = "An error occurred. Please try again later.";
            }
        } else {
            // Don't reveal that the email doesn't exist for security reasons
            $success = "If your email address exists in our database, you will receive a password reset link shortly.";
        }
    }
}

// Set page title
$page_title = "Forgot Password";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | Manage Inc.</title>
    <link rel="stylesheet" href="../css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="css/admin-style.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            font-family: 'Open Sans', sans-serif;
        }

        .login-container {
            max-width: 420px;
            width: 100%;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: #f1ca2f;
        }

        .login-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .login-header img {
            max-width: 200px;
            margin-bottom: 25px;
        }

        .login-header h2 {
            color: #3c3c45;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .login-header p {
            color: #666;
            margin-top: 0;
            font-size: 14px;
        }

        .admin-alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 25px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .admin-alert.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }

        .admin-alert.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }

        .admin-alert i {
            margin-right: 10px;
            font-size: 16px;
        }

        .login-form .form-group {
            margin-bottom: 20px;
        }

        .login-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #3c3c45;
            font-size: 14px;
        }

        .login-form .input-group {
            position: relative;
        }

        .login-form .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .login-form input {
            width: 100%;
            padding: 14px 15px 14px 45px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 15px;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .login-form input:focus {
            border-color: #f1ca2f;
            background-color: #fff;
            box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.2);
            outline: none;
        }

        .login-form button {
            width: 100%;
            padding: 14px;
            background-color: #3c3c45;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-form button i {
            margin-right: 8px;
        }

        .login-form button:hover {
            background-color: #4a4a52;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .login-footer {
            text-align: center;
            margin-top: 35px;
            color: #777;
            font-size: 13px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .back-to-login {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 14px;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .back-to-login:hover {
            color: #f1ca2f;
            text-decoration: underline;
        }

        .back-to-login i {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="../images/logo.png" alt="Manage Incorporated">
            <h2>Forgot Password</h2>
            <p>Enter your email address to reset your password</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="admin-alert error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="admin-alert success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <form class="login-form" method="post" action="">
            <div class="form-group">
                <label for="email">Email Address</label>
                <div class="input-group">
                    <i class="fas fa-envelope input-icon"></i>
                    <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                </div>
            </div>
            <button type="submit">
                <i class="fas fa-paper-plane"></i> Send Reset Link
            </button>
        </form>

        <a href="index.php" class="back-to-login">
            <i class="fas fa-arrow-left"></i> Back to Login
        </a>

        <div class="login-footer">
            <p>&copy; <?php echo date('Y'); ?> Manage Inc. All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>
