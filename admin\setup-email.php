<?php
/**
 * Email Configuration Setup Script
 * 
 * This script ensures basic email settings are configured in the system_settings table
 */

session_start();
require_once 'config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please log in as admin.');
}

// Function to insert or update a setting
function insertOrUpdateSetting($conn, $key, $value, $category = 'email') {
    // Check if setting exists
    $check_sql = "SELECT id FROM system_settings WHERE setting_key = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("s", $key);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Update existing setting
        $update_sql = "UPDATE system_settings SET setting_value = ? WHERE setting_key = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ss", $value, $key);
        return $update_stmt->execute();
    } else {
        // Insert new setting
        $insert_sql = "INSERT INTO system_settings (setting_key, setting_value, category, display_name, setting_type, is_required) VALUES (?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        
        // Define setting properties
        $settings_config = [
            'use_smtp' => ['display_name' => 'Use SMTP', 'type' => 'checkbox', 'required' => 0],
            'smtp_host' => ['display_name' => 'SMTP Host', 'type' => 'text', 'required' => 0],
            'smtp_port' => ['display_name' => 'SMTP Port', 'type' => 'number', 'required' => 0],
            'smtp_username' => ['display_name' => 'SMTP Username', 'type' => 'text', 'required' => 0],
            'smtp_password' => ['display_name' => 'SMTP Password', 'type' => 'password', 'required' => 0],
            'smtp_security' => ['display_name' => 'SMTP Security', 'type' => 'select', 'required' => 0],
            'from_email' => ['display_name' => 'From Email', 'type' => 'email', 'required' => 1],
            'from_name' => ['display_name' => 'From Name', 'type' => 'text', 'required' => 1],
            'reply_to' => ['display_name' => 'Reply To Email', 'type' => 'email', 'required' => 0]
        ];
        
        $config = $settings_config[$key] ?? ['display_name' => ucfirst(str_replace('_', ' ', $key)), 'type' => 'text', 'required' => 0];
        
        $insert_stmt->bind_param("sssssi", $key, $value, $category, $config['display_name'], $config['type'], $config['required']);
        return $insert_stmt->execute();
    }
}

// Default email settings
$default_settings = [
    'use_smtp' => '0',
    'smtp_host' => '',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_security' => 'tls',
    'from_email' => '<EMAIL>',
    'from_name' => 'Manage Inc.',
    'reply_to' => '<EMAIL>'
];

echo "<!DOCTYPE html>
<html>
<head>
    <title>Email Configuration Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; margin: 10px 0; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; margin: 10px 0; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Email Configuration Setup</h1>";

if (isset($_POST['setup_email'])) {
    echo "<h2>Setting up email configuration...</h2>";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($default_settings as $key => $value) {
        if (insertOrUpdateSetting($conn, $key, $value, 'email')) {
            echo "<div class='success'>✓ Configured: $key</div>";
            $success_count++;
        } else {
            echo "<div class='error'>✗ Failed to configure: $key</div>";
            $error_count++;
        }
    }
    
    // Add SMTP security options
    $smtp_security_options = json_encode([
        'tls' => 'TLS',
        'ssl' => 'SSL',
        'none' => 'None'
    ]);
    
    $update_options_sql = "UPDATE system_settings SET options = ? WHERE setting_key = 'smtp_security'";
    $update_options_stmt = $conn->prepare($update_options_sql);
    $update_options_stmt->bind_param("s", $smtp_security_options);
    $update_options_stmt->execute();
    
    echo "<div class='info'>📝 Added SMTP security options</div>";
    
    echo "<h3>Summary:</h3>";
    echo "<div class='success'>✓ Successfully configured: $success_count settings</div>";
    if ($error_count > 0) {
        echo "<div class='error'>✗ Failed to configure: $error_count settings</div>";
    }
    
    echo "<p><a href='settings.php?category=email'>→ Go to Email Settings</a></p>";
    echo "<p><a href='test-email.php'>→ Test Email Configuration</a></p>";
    
} else {
    echo "<p>This script will set up basic email configuration in the system_settings table.</p>";
    echo "<p>The following settings will be configured with default values:</p>";
    echo "<ul>";
    foreach ($default_settings as $key => $value) {
        $display_value = ($key === 'smtp_password') ? '(empty)' : ($value ?: '(empty)');
        echo "<li><strong>$key:</strong> $display_value</li>";
    }
    echo "</ul>";
    
    echo "<form method='post'>
        <button type='submit' name='setup_email' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>Setup Email Configuration</button>
    </form>";
}

echo "</body></html>";
?>
