<?php
/**
 * Sample Configuration File
 * 
 * This file serves as a template for the main config.php file.
 * Copy this file to config.php and update the values according to your setup.
 * 
 * This file is automatically generated during the installation process,
 * but you can also create it manually using this template.
 */

// =============================================================================
// DATABASE CONFIGURATION
// =============================================================================

// Database connection settings
define('DB_HOST', 'localhost');           // Database host (usually localhost)
define('DB_USER', 'your_db_username');    // Database username
define('DB_PASS', 'your_db_password');    // Database password
define('DB_NAME', 'your_db_name');        // Database name

// Installation status (set to true after successful installation)
define('DB_INSTALLED', false);

// =============================================================================
// SECURITY CONFIGURATION
// =============================================================================

// Secret key for encryption and security (generate a random 64-character string)
define('SECRET_KEY', 'your_64_character_random_secret_key_here_change_this_value');

// CSRF Protection
define('CSRF_PROTECTION', true);          // Enable CSRF protection
define('CSRF_TOKEN_EXPIRY', 3600);        // CSRF token expiry time in seconds (1 hour)

// Session Configuration
define('SESSION_TIMEOUT', 3600);          // Session timeout in seconds (1 hour)

// Login Security
define('MAX_LOGIN_ATTEMPTS', 5);          // Maximum login attempts before lockout
define('LOGIN_LOCKOUT_TIME', 900);        // Login lockout time in seconds (15 minutes)
define('LOGIN_LOCKOUT_DURATION', 900);    // Login lockout duration in seconds (15 minutes)

// Password Requirements
define('PASSWORD_MIN_LENGTH', 8);         // Minimum password length
define('PASSWORD_REQUIRES_MIXED_CASE', true);  // Require uppercase and lowercase
define('PASSWORD_REQUIRES_NUMBERS', true);     // Require numbers
define('PASSWORD_REQUIRES_SYMBOLS', true);     // Require special characters

// Security Headers
define('XSS_PROTECTION', true);           // Enable XSS protection headers
define('CONTENT_SECURITY_POLICY', true);  // Enable Content Security Policy

// =============================================================================
// EMAIL CONFIGURATION
// =============================================================================

// SMTP Settings (leave empty to use PHP mail() function)
define('SMTP_HOST', '');                  // SMTP server hostname (e.g., smtp.gmail.com)
define('SMTP_PORT', 587);                 // SMTP port (587 for TLS, 465 for SSL, 25 for no encryption)
define('SMTP_USERNAME', '');              // SMTP username
define('SMTP_PASSWORD', '');              // SMTP password
define('SMTP_ENCRYPTION', 'tls');         // SMTP encryption (tls, ssl, or none)

// Default Email Settings
define('FROM_EMAIL', '<EMAIL>');    // Default sender email
define('FROM_NAME', 'Your Website Name');          // Default sender name

// =============================================================================
// SITE CONFIGURATION
// =============================================================================

// Site Information
define('SITE_NAME', 'Your Website Name');
define('SITE_DESCRIPTION', 'Your website description');
define('SITE_URL', 'https://yourdomain.com');      // Your website URL (without trailing slash)
define('ADMIN_EMAIL', '<EMAIL>');     // Main administrator email

// =============================================================================
// FILE UPLOAD CONFIGURATION
// =============================================================================

// File Upload Limits
define('MAX_FILE_SIZE', 10485760);        // Maximum file size in bytes (10MB)

// Allowed File Types
define('ALLOWED_FILE_TYPES', array(
    'jpg', 'jpeg', 'png', 'gif',          // Images
    'pdf',                                 // Documents
    'doc', 'docx',                        // Word documents
    'txt'                                 // Text files
));

// Upload Directories
define('UPLOAD_DIR', 'uploads/');         // Main upload directory
define('ADMIN_UPLOAD_DIR', 'uploads/admin/');  // Admin uploads directory

// =============================================================================
// SYSTEM CONFIGURATION
// =============================================================================

// Timezone
date_default_timezone_set('UTC');        // Set your timezone (e.g., 'America/New_York', 'Europe/London')

// Error Reporting
ini_set('display_errors', 0);            // Set to 1 for development, 0 for production
ini_set('log_errors', 1);                // Always log errors
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

// =============================================================================
// DATABASE CONNECTION
// =============================================================================

// Initialize database connection
$conn = null;
$max_retries = 3;
$retry_count = 0;
$connection_error = '';

// Skip database connection if we're in the installation process
if (!defined('INSTALLING') || !INSTALLING) {
    while ($retry_count < $max_retries && $conn === null) {
        try {
            // Create connection with error suppression for graceful handling
            $conn = @new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

            // Check connection
            if ($conn->connect_error) {
                throw new Exception('Connection failed: ' . $conn->connect_error);
            }

            // Set charset
            $conn->set_charset('utf8mb4');

        } catch (Exception $e) {
            $connection_error = $e->getMessage();
            $retry_count++;

            if ($retry_count >= $max_retries) {
                error_log('Database connection failed after ' . $max_retries . ' attempts: ' . $connection_error);

                // Don't exit immediately, allow the page to load with an error message
                $conn = null;
                $db_error = true;
                $db_error_message = $connection_error;

                // Only show error page if not on index.php or login.php
                $current_script = basename($_SERVER['SCRIPT_NAME']);
                if ($current_script !== 'index.php' && $current_script !== 'login.php') {
                    // Show error page
                    $error_file = __DIR__ . '/includes/db-error.php';
                    if (file_exists($error_file)) {
                        include_once $error_file;
                    } else {
                        echo "Database connection failed: " . $connection_error;
                    }
                    exit;
                }
            }

            // Wait before retrying
            sleep(1);
        }
    }
} else {
    // We're in the installation process, log this
    error_log('Skipping database connection during installation');
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Include helpers.php if it exists and is not already included
 */
$helpers_file = __DIR__ . '/includes/helpers.php';
if (file_exists($helpers_file)) {
    require_once $helpers_file;
}

/**
 * Sanitize input data to prevent SQL injection
 *
 * @param mixed $input Input to sanitize
 * @return mixed Sanitized input
 */
function sanitize($input) {
    global $conn;

    // Handle arrays recursively
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
        return $input;
    }

    // Handle strings
    if (is_string($input)) {
        if ($conn) {
            return $conn->real_escape_string(trim($input));
        }
        return trim($input);
    }

    // Return as is for other types
    return $input;
}

/**
 * Sanitize output to prevent XSS
 *
 * @param string $output String to sanitize
 * @return string Sanitized output
 */
function html_escape($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

// =============================================================================
// SECURITY FUNCTIONS
// =============================================================================

// Include security functions if they exist
$security_file = __DIR__ . '/includes/security.php';
if (file_exists($security_file)) {
    require_once $security_file;
} else {
    // Define basic security functions as fallback

    /**
     * Generate CSRF token
     *
     * @return string CSRF token
     */
    if (!function_exists('generate_csrf_token')) {
        function generate_csrf_token() {
            // Make sure session is started
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            if (empty($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            }
            return $_SESSION['csrf_token'];
        }
    }

    /**
     * Validate CSRF token
     *
     * @param string $token Token to validate
     * @return bool True if valid, false otherwise
     */
    if (!function_exists('validate_csrf_token')) {
        function validate_csrf_token($token) {
            // Make sure session is started
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // If CSRF protection is disabled, always return true
            if (!defined('CSRF_PROTECTION') || !CSRF_PROTECTION) {
                return true;
            }

            // If token is empty, return false
            if (empty($token)) {
                error_log("CSRF validation failed: Empty token provided");
                return false;
            }

            // If session token is not set, return false
            if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
                error_log("CSRF validation failed: No token in session");
                return false;
            }

            // Compare tokens
            return hash_equals($_SESSION['csrf_token'], $token);
        }
    }
}

// =============================================================================
// INSTALLATION NOTES
// =============================================================================

/*
INSTALLATION INSTRUCTIONS:

1. Copy this file to config.php
2. Update the database settings (DB_HOST, DB_USER, DB_PASS, DB_NAME)
3. Generate a secure SECRET_KEY (64 random characters)
4. Update SITE_NAME, SITE_DESCRIPTION, SITE_URL
5. Configure email settings if using SMTP
6. Set your timezone
7. Run the installation script at admin/install.php

SECURITY NOTES:

- Change the SECRET_KEY to a unique random string
- Use strong database credentials
- Set display_errors to 0 in production
- Configure SMTP for reliable email delivery
- Regularly update passwords and review security settings

EMAIL CONFIGURATION:

For Gmail SMTP:
- SMTP_HOST: smtp.gmail.com
- SMTP_PORT: 587
- SMTP_ENCRYPTION: tls
- Use App Password instead of regular password

For other providers, check their SMTP documentation.
*/
?>
