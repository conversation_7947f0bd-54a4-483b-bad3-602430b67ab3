/* Enhanced Settings Page Styles */

/* Settings Container */
.settings-container {
    display: flex;
    gap: 30px;
    min-height: 600px;
}

/* Enhanced Sidebar */
.settings-sidebar {
    width: 320px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.settings-sidebar-header {
    padding: 25px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f1ca2f 0%, #e0b929 100%);
    border-radius: 12px 12px 0 0;
    color: #333;
}

.settings-sidebar-header h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-sidebar-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
}

/* Enhanced Navigation */
.settings-nav {
    list-style: none;
    margin: 0;
    padding: 15px 0;
}

.settings-nav-item {
    margin: 0;
}

.settings-nav-link {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    gap: 15px;
}

.settings-nav-link:hover {
    background: rgba(241, 202, 47, 0.1);
    color: #333;
    border-left-color: #f1ca2f;
}

.settings-nav-item.active .settings-nav-link {
    background: rgba(241, 202, 47, 0.15);
    color: #333;
    border-left-color: #f1ca2f;
    font-weight: 500;
}

.nav-icon {
    width: 40px;
    height: 40px;
    background: rgba(241, 202, 47, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #f1ca2f;
    flex-shrink: 0;
}

.settings-nav-item.active .nav-icon {
    background: #f1ca2f;
    color: #333;
}

.nav-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nav-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
}

.nav-desc {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.3;
}

.nav-arrow {
    color: #ccc;
    font-size: 12px;
    transition: all 0.3s ease;
}

.settings-nav-item.active .nav-arrow {
    color: #f1ca2f;
    transform: rotate(90deg);
}

/* Sidebar Footer */
.settings-sidebar-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.help-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.help-card i {
    font-size: 24px;
    color: #f1ca2f;
    margin-bottom: 10px;
}

.help-card h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.help-card p {
    margin: 0 0 15px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.help-link {
    color: #f1ca2f;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.help-link:hover {
    color: #e0b929;
}

/* Settings Content */
.settings-content {
    flex: 1;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.settings-header {
    padding: 30px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.settings-title-group h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-title-group h3 i {
    color: #f1ca2f;
}

.settings-section-desc {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Settings Subsections */
.settings-subsection {
    padding: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.settings-subsection:last-child {
    border-bottom: none;
}

.subsection-title {
    margin: 0 0 25px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1ca2f;
}

.subsection-title i {
    color: #f1ca2f;
}

/* Form Grids */
.form-grid {
    display: grid;
    gap: 25px;
}

.grid-1-col {
    grid-template-columns: 1fr;
}

.grid-2-col {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3-col {
    grid-template-columns: repeat(3, 1fr);
}

/* Enhanced Form Elements */
.password-input-group {
    position: relative;
    display: flex;
}

.password-input-group .form-control {
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #f1ca2f;
    background: rgba(241, 202, 47, 0.1);
}

.color-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.color-picker {
    width: 50px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    padding: 0;
}

.color-text {
    flex: 1;
}

/* Enhanced Checkboxes */
.checkbox-wrapper {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    background: #f8f9fa;
}

.checkbox-label:hover {
    background: rgba(241, 202, 47, 0.1);
    border-color: #f1ca2f;
}

.checkbox-label.checked {
    background: rgba(241, 202, 47, 0.15);
    border-color: #f1ca2f;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    background: #fff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #f1ca2f;
    border-color: #f1ca2f;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    font-weight: 500;
    color: #333;
}

/* Enhanced Form Actions */
.form-actions {
    padding: 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.admin-btn-primary {
    background: #f1ca2f;
    color: #333;
    border: 1px solid #f1ca2f;
}

.admin-btn-primary:hover {
    background: #e0b929;
    border-color: #e0b929;
}

.admin-btn-secondary {
    background: #fff;
    color: #666;
    border: 1px solid #ddd;
}

.admin-btn-secondary:hover {
    background: #f8f9fa;
    color: #333;
}

/* No Settings State */
.no-settings {
    text-align: center;
    padding: 60px 30px;
    color: #666;
}

.no-settings-icon {
    font-size: 48px;
    color: #f1ca2f;
    margin-bottom: 20px;
}

.no-settings h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #333;
}

.no-settings p {
    margin: 0;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .settings-container {
        flex-direction: column;
        gap: 20px;
    }
    
    .settings-sidebar {
        width: 100%;
        position: static;
    }
    
    .grid-3-col {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-2-col,
    .grid-3-col {
        grid-template-columns: 1fr;
    }
    
    .settings-sidebar-header,
    .settings-header,
    .settings-subsection,
    .form-actions {
        padding: 20px;
    }
    
    .nav-desc {
        display: none;
    }
}
