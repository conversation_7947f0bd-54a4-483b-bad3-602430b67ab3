/**
 * HTML Editor - Version History
 *
 * Handles version history functionality for the HTML editor
 */

// Initialize version history
function initVersionHistory() {
    // Get elements
    const versionHistoryToggle = document.getElementById('versionHistoryToggle');
    const versionHistoryContent = document.getElementById('versionHistoryContent');
    const closeVersionsBtn = document.getElementById('closeVersionsBtn');
    const versionHistoryModal = document.getElementById('versionHistoryModal');
    const closePreviewBtn = document.getElementById('closePreviewBtn');
    const closePreviewFooterBtn = document.getElementById('closePreviewFooterBtn');
    const versionPreviewModal = document.getElementById('versionPreviewModal');
    const restorePreviewBtn = document.getElementById('restorePreviewBtn');

    // Toggle version history section
    if (versionHistoryToggle && versionHistoryContent) {
        versionHistoryToggle.addEventListener('click', function() {
            // Toggle display
            if (versionHistoryContent.style.display === 'none') {
                versionHistoryContent.style.display = 'block';
                versionHistoryToggle.querySelector('.toggle-icon').classList.add('rotate');
            } else {
                versionHistoryContent.style.display = 'none';
                versionHistoryToggle.querySelector('.toggle-icon').classList.remove('rotate');
            }
        });
    }

    // Close version history modal
    if (closeVersionsBtn) {
        closeVersionsBtn.addEventListener('click', function() {
            versionHistoryModal.classList.remove('show');
        });
    }

    // Close version preview modal
    if (closePreviewBtn) {
        closePreviewBtn.addEventListener('click', function() {
            versionPreviewModal.classList.remove('show');
        });
    }

    // Close version preview modal from footer button
    if (closePreviewFooterBtn) {
        closePreviewFooterBtn.addEventListener('click', function() {
            versionPreviewModal.classList.remove('show');
        });
    }

    // View version buttons
    const viewVersionBtns = document.querySelectorAll('.view-version-btn');
    viewVersionBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const version = this.dataset.version;
            viewVersion(version);
        });
    });

    // Restore version buttons
    const restoreVersionBtns = document.querySelectorAll('.restore-version-btn');
    restoreVersionBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const version = this.dataset.version;
            if (confirm('Are you sure you want to restore version ' + version + '? This will overwrite the current file.')) {
                restoreVersion(version);
            }
        });
    });

    // Restore from preview button
    if (restorePreviewBtn) {
        restorePreviewBtn.addEventListener('click', function() {
            const version = document.getElementById('previewVersion').textContent;
            if (confirm('Are you sure you want to restore version ' + version + '? This will overwrite the current file.')) {
                restoreVersion(version);
            }
        });
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === versionHistoryModal) {
            versionHistoryModal.classList.remove('show');
        }
        if (event.target === versionPreviewModal) {
            versionPreviewModal.classList.remove('show');
        }
    });
}

// View a specific version of a file
function viewVersion(version) {
    // Get elements
    const versionPreviewModal = document.getElementById('versionPreviewModal');
    const previewVersion = document.getElementById('previewVersion');
    const previewDate = document.getElementById('previewDate');
    const previewUser = document.getElementById('previewUser');
    const previewComment = document.getElementById('previewComment');
    const previewContent = document.getElementById('previewContent');

    // Show loading state
    previewVersion.textContent = 'Loading...';
    previewDate.textContent = '';
    previewUser.textContent = '';
    previewComment.textContent = '';
    previewContent.textContent = 'Loading...';

    // Show modal
    versionPreviewModal.classList.add('show');

    // Fetch version data
    fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'action=get_version&file_path=' + encodeURIComponent(currentFilePath) + '&version=' + version
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update preview
            previewVersion.textContent = data.version.version;
            previewDate.textContent = new Date(data.version.created_at).toLocaleString();
            previewUser.textContent = data.version.username;
            previewComment.textContent = data.version.comment || 'No comment';

            // Set content
            previewContent.textContent = data.version.content;

            // Initialize CodeMirror for preview
            if (typeof CodeMirror !== 'undefined') {
                if (window.previewEditor) {
                    window.previewEditor.toTextArea();
                }

                window.previewEditor = CodeMirror.fromTextArea(previewContent, {
                    lineNumbers: true,
                    mode: 'htmlmixed',
                    theme: 'material',
                    readOnly: true
                });
            }
        } else {
            // Show error
            previewVersion.textContent = 'Error';
            previewContent.textContent = data.message || 'Failed to load version';
        }
    })
    .catch(error => {
        console.error('Error fetching version:', error);
        previewVersion.textContent = 'Error';
        previewContent.textContent = 'Failed to load version: ' + error.message;
    });
}

// Restore a specific version of a file
function restoreVersion(version) {
    // Show loading overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // Restore version
    fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'action=restore_version&file_path=' + encodeURIComponent(currentFilePath) + '&version=' + version
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to show restored version
            window.location.reload();
        } else {
            // Hide loading overlay
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }

            // Show error
            alert(data.message || 'Failed to restore version');
        }
    })
    .catch(error => {
        console.error('Error restoring version:', error);

        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        // Show error
        alert('Failed to restore version: ' + error.message);
    });
}

// Get all versions of a file
function getAllVersions(filePath) {
    return fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'action=get_all_versions&file_path=' + encodeURIComponent(filePath)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.versions;
        } else {
            console.error('Failed to get versions:', data.message);
            return [];
        }
    })
    .catch(error => {
        console.error('Error getting versions:', error);
        return [];
    });
}

// Load version history for current file
function loadVersionHistory() {
    // Check if we have a current file path
    if (!currentFilePath) {
        console.log('No current file path, skipping version history load');
        return;
    }

    // Get all versions for the current file
    getAllVersions(currentFilePath).then(function(versions) {
        if (versions && versions.length > 0) {
            updateVersionHistory(versions);
        } else {
            console.log('No versions found for file:', currentFilePath);
        }
    }).catch(function(error) {
        console.error('Error loading version history:', error);
    });
}

// Update version history table
function updateVersionHistory(versions) {
    const versionHistoryTable = document.getElementById('versionHistoryTable');
    if (versionHistoryTable && versions.length > 0) {
        const tbody = versionHistoryTable.querySelector('tbody');
        tbody.innerHTML = '';

        versions.forEach(function(version) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${version.version}</td>
                <td>${new Date(version.created_at).toLocaleString()}</td>
                <td>${version.username}</td>
                <td>${version.comment || 'No comment'}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary view-version-btn" data-version="${version.version}">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button type="button" class="btn btn-sm btn-warning restore-version-btn" data-version="${version.version}">
                        <i class="fas fa-undo"></i> Restore
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Reinitialize event listeners
        const viewVersionBtns = versionHistoryTable.querySelectorAll('.view-version-btn');
        viewVersionBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const version = this.dataset.version;
                viewVersion(version);
            });
        });

        const restoreVersionBtns = versionHistoryTable.querySelectorAll('.restore-version-btn');
        restoreVersionBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const version = this.dataset.version;
                if (confirm('Are you sure you want to restore version ' + version + '? This will overwrite the current file.')) {
                    restoreVersion(version);
                }
            });
        });
    }
}
