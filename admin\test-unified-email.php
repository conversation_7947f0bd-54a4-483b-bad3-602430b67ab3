<?php
/**
 * Test Unified Email System
 * This file tests the new unified email system
 */

// Include necessary files
require_once 'config.php';
require_once 'includes/email-functions.php';
require_once 'includes/helpers.php';

// Check if user is logged in and is admin
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: index.php');
    exit;
}

$test_results = [];
$test_email = '';

// Process test email form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $test_email = sanitize_input($_POST['test_email']);
    
    if (!empty($test_email) && filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        // Test 1: Basic email sending
        $subject = 'Test Email - Unified System';
        $message = 'This is a test email from the unified email system. If you receive this, the system is working correctly.';
        
        $result1 = send_email($test_email, $subject, $message);
        $test_results['basic'] = $result1;
        
        // Test 2: HTML email sending
        $html_message = '
        <html>
        <head><title>Test HTML Email</title></head>
        <body>
            <h2>HTML Email Test</h2>
            <p>This is a <strong>HTML email</strong> test from the unified system.</p>
            <p>Features tested:</p>
            <ul>
                <li>HTML formatting</li>
                <li>Bold text</li>
                <li>Lists</li>
            </ul>
        </body>
        </html>';
        
        $result2 = send_email($test_email, 'Test HTML Email - Unified System', $html_message);
        $test_results['html'] = $result2;
        
        // Test 3: Template email (if contact form template exists)
        $result3 = send_contact_form_email('Test User', $test_email, 'Test Subject', 'This is a test message from the unified email system.');
        $test_results['template'] = $result3;
        
    } else {
        $error = 'Please enter a valid email address.';
    }
}

// Get current email settings
$email_settings = get_email_settings();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unified Email System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f1ca2f;
            color: #333;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #e0b929;
        }
        .settings-table {
            width: 100%;
            border-collapse: collapse;
        }
        .settings-table th,
        .settings-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .settings-table th {
            background: #f1ca2f;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Unified Email System Test</h1>
        <p>Test the new unified email system that uses mail() by default and SMTP when enabled.</p>
    </div>

    <div class="section">
        <h2>📧 Current Email Settings</h2>
        <table class="settings-table">
            <tr>
                <th>Setting</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>SMTP Enabled</td>
                <td><?php echo $email_settings['use_smtp'] === '1' ? '✅ Yes' : '❌ No (using PHP mail())'; ?></td>
            </tr>
            <tr>
                <td>From Email</td>
                <td><?php echo htmlspecialchars($email_settings['from_email']); ?></td>
            </tr>
            <tr>
                <td>From Name</td>
                <td><?php echo htmlspecialchars($email_settings['from_name']); ?></td>
            </tr>
            <?php if ($email_settings['use_smtp'] === '1'): ?>
            <tr>
                <td>SMTP Host</td>
                <td><?php echo htmlspecialchars($email_settings['smtp_host']); ?></td>
            </tr>
            <tr>
                <td>SMTP Port</td>
                <td><?php echo htmlspecialchars($email_settings['smtp_port']); ?></td>
            </tr>
            <tr>
                <td>SMTP Security</td>
                <td><?php echo htmlspecialchars($email_settings['smtp_security']); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

    <div class="section">
        <h2>🧪 Run Email Tests</h2>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label for="test_email">Test Email Address:</label>
                <input type="email" id="test_email" name="test_email" value="<?php echo htmlspecialchars($test_email); ?>" required>
            </div>
            <button type="submit">🚀 Run Email Tests</button>
        </form>
        
        <?php if (!empty($test_results)): ?>
            <h3>📊 Test Results</h3>
            
            <div class="<?php echo $test_results['basic'] ? 'success' : 'error'; ?>">
                <strong>Basic Email Test:</strong> <?php echo $test_results['basic'] ? '✅ Success' : '❌ Failed'; ?>
            </div>
            
            <div class="<?php echo $test_results['html'] ? 'success' : 'error'; ?>">
                <strong>HTML Email Test:</strong> <?php echo $test_results['html'] ? '✅ Success' : '❌ Failed'; ?>
            </div>
            
            <div class="<?php echo $test_results['template'] ? 'success' : 'error'; ?>">
                <strong>Template Email Test:</strong> <?php echo $test_results['template'] ? '✅ Success' : '❌ Failed'; ?>
            </div>
            
            <div class="info">
                <strong>Note:</strong> Check your email inbox (including spam folder) to verify the emails were received.
            </div>
        <?php endif; ?>
    </div>

    <div class="section">
        <h2>📝 How It Works</h2>
        <ul>
            <li><strong>Default Mode:</strong> Uses PHP's built-in <code>mail()</code> function (same as installation process)</li>
            <li><strong>SMTP Mode:</strong> Uses SMTP when "Use SMTP" is enabled in Email Settings</li>
            <li><strong>Fallback:</strong> If SMTP fails, automatically falls back to <code>mail()</code></li>
            <li><strong>Unified API:</strong> All email functions use the same <code>send_email()</code> function</li>
        </ul>
    </div>

    <div class="section">
        <h2>⚙️ Configuration</h2>
        <p>To configure email settings:</p>
        <ol>
            <li>Go to <strong>Settings → Email</strong> in the admin panel</li>
            <li>Configure your email preferences</li>
            <li>Enable SMTP if you want to use SMTP instead of PHP mail()</li>
            <li>Test your configuration using this page</li>
        </ol>
        <p><a href="settings.php?category=email">📧 Go to Email Settings</a></p>
    </div>

    <div class="section">
        <p><a href="dashboard.php">← Back to Dashboard</a></p>
    </div>
</body>
</html>
