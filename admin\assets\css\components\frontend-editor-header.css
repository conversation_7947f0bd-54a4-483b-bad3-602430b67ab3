/**
 * Frontend Editor Header CSS
 *
 * Custom styles for the frontend editor header with file selector button
 */

/* Content Header */
.content-header {
  margin-bottom: var(--spacing-4);
}

/* Header Main */
.content-header .header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

/* Header Title */
.content-header .header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.content-header .header-icon {
  color: var(--primary-color);
}

/* File Selector Container */
.content-header .file-selector-container {
  position: relative;
}

/* File Selector Trigger */
.content-header .file-selector-trigger {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--radius-md);
  color: var(--white);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.content-header .file-selector-trigger:hover {
  background-color: var(--primary-dark);
}

.content-header .file-selector-trigger.active {
  background-color: var(--primary-dark);
}

/* Compact File Explorer */
.content-header #compactFileExplorer {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  max-height: 500px;
  overflow-y: auto;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  padding: var(--spacing-3);
}

.content-header #fileSelector {
  width: 100%;
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-2);
}

.content-header .compact-file-info {
  color: var(--text-light);
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .content-header .header-main {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .content-header .file-selector-container {
    width: 100%;
  }
  
  .content-header .file-selector-trigger {
    width: 100%;
    justify-content: center;
  }
  
  .content-header #compactFileExplorer {
    width: 100%;
    left: 0;
    right: 0;
  }
}
