<?php
/**
 * Category Management Page
 *
 * This page handles both adding new categories and editing existing ones.
 */

// Include necessary files
session_start();
require_once 'config.php';
require_once 'includes/admin-functions.php';

// Check if user is logged in and has permission
require_login();

// Initialize variables
$page_title = "Add New Category";
$page_icon = "fas fa-folder-plus";
$page_subtitle = "Create a new content category";
$category_id = 0;
$category_name = '';
$category_slug = '';
$category_description = '';
$error_message = '';
$success_message = '';
$is_edit_mode = false;

// Check if we're in edit mode
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $category_id = (int)$_GET['id'];
    $is_edit_mode = true;
    $page_title = "Edit Category";
    $page_icon = "fas fa-edit";
    $page_subtitle = "Modify an existing content category";

    // Get category data
    $stmt = $conn->prepare("SELECT id, name, slug, description FROM categories WHERE id = ?");
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Category not found, redirect to categories list
        header("Location: categories.php?error=Category+not+found");
        exit;
    }

    $category = $result->fetch_assoc();
    $category_name = $category['name'];
    $category_slug = $category['slug'];
    $category_description = $category['description'];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $category_name = trim($_POST['name']);
    $category_slug = trim($_POST['slug']);
    $category_description = trim($_POST['description']);

    // Validate form data
    if (empty($category_name)) {
        $error_message = "Category name is required";
    } else {
        // Generate slug if not provided
        if (empty($category_slug)) {
            $category_slug = strtolower(str_replace(' ', '-', $category_name));
            $category_slug = preg_replace('/[^a-z0-9\-]/', '', $category_slug);
        }

        // Check if slug is already in use by another category
        $check_sql = "SELECT id FROM categories WHERE slug = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $category_slug, $category_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = "Category slug is already in use. Please choose a different one.";
        } else {
            // Save category
            if ($is_edit_mode) {
                // Update existing category
                $update_sql = "UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("sssi", $category_name, $category_slug, $category_description, $category_id);

                if ($update_stmt->execute()) {
                    $success_message = "Category updated successfully";
                } else {
                    $error_message = "Error updating category: " . $conn->error;
                }
            } else {
                // Insert new category
                $insert_sql = "INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param("sss", $category_name, $category_slug, $category_description);

                if ($insert_stmt->execute()) {
                    $category_id = $conn->insert_id;
                    $success_message = "Category created successfully";

                    // Clear form for new entry
                    if (!isset($_POST['save_and_continue'])) {
                        $category_name = '';
                        $category_slug = '';
                        $category_description = '';
                    } else {
                        // Switch to edit mode
                        $is_edit_mode = true;
                        $page_title = "Edit Category";
                        $page_icon = "fas fa-edit";
                        $page_subtitle = "Modify an existing content category";
                    }
                } else {
                    $error_message = "Error creating category: " . $conn->error;
                }
            }
        }
    }
}

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
        <div class="admin-content-actions">
            <a href="categories.php" class="admin-btn">
                <i class="fas fa-arrow-left"></i> Back to Categories
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
    <div class="admin-alert success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="admin-alert error">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 class="admin-card-title">Category Information</h3>
            </div>
            <div class="admin-card-body">
                <form class="admin-form" method="post" action="">
                    <input type="hidden" name="id" value="<?php echo $category_id; ?>">

                    <div class="form-group">
                        <label for="name">Category Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($category_name); ?>" required>
                        <p class="form-hint">The name of the category as it will appear on the site</p>
                    </div>

                    <div class="form-group">
                        <label for="slug">Slug</label>
                        <input type="text" id="slug" name="slug" value="<?php echo htmlspecialchars($category_slug); ?>">
                        <p class="form-hint">The URL-friendly version of the name. Leave blank to generate automatically.</p>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4"><?php echo htmlspecialchars($category_description); ?></textarea>
                        <p class="form-hint">A brief description of the category (optional)</p>
                    </div>

                    <div class="form-actions">
                        <?php if ($is_edit_mode): ?>
                        <button type="submit" class="admin-btn">
                            <i class="fas fa-save"></i> Update Category
                        </button>
                        <?php else: ?>
                        <button type="submit" class="admin-btn">
                            <i class="fas fa-plus-circle"></i> Add Category
                        </button>
                        <button type="submit" name="save_and_continue" class="admin-btn">
                            <i class="fas fa-save"></i> Save & Continue Editing
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-generate slug from name
        const nameInput = document.getElementById('name');
        const slugInput = document.getElementById('slug');

        if (nameInput && slugInput) {
            nameInput.addEventListener('input', function() {
                // Only generate slug if it's empty or hasn't been manually edited
                if (slugInput.value === '' || slugInput._autoGenerated) {
                    const slug = this.value.toLowerCase()
                        .replace(/[^\w\s-]/g, '') // Remove special chars except spaces and hyphens
                        .replace(/\s+/g, '-')     // Replace spaces with hyphens
                        .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen

                    slugInput.value = slug;
                    slugInput._autoGenerated = true;
                }
            });

            // Mark slug as manually edited
            slugInput.addEventListener('input', function() {
                this._autoGenerated = false;
            });
        }

        // Auto-dismiss alerts after 5 seconds
        const alerts = document.querySelectorAll('.admin-alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });
    });
</script>

<?php include 'includes/footer.php'; ?>
