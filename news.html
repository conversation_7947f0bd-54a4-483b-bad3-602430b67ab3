
<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>News | Manage Inc.</title>
    <link rel="stylesheet" href="css/style.css?v=20250541">
    <link rel="stylesheet" href="css/carousel.css?v=20250541">
    <link rel="stylesheet" href="css/submenu.css?v=20250541">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Ensure mobile menu toggle is visible */
        /* News grid styling */
        .news-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }

        .news-item {
            background: #fff;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .news-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .news-image {
            height: 200px;
            overflow: hidden;
        }

        .news-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .news-item:hover .news-image img {
            transform: scale(1.05);
        }

        .news-content {
            padding: 20px;
            text-align: left; /* Ensure all content is left-aligned */
            position: relative;
            min-height: 300px; /* Increased height for more content */
        }

        .news-content h2 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
            font-weight: 600;
        }

        .news-category {
            display: inline-block;
            background: #f1ca2f;
            color: #333;
            padding: 3px 10px;
            border-radius: 3px;
            font-size: 12px;
            margin-bottom: 15px;
            font-weight: 600;
            text-align: left;
            width: auto;
            margin-right: auto; /* Add this to ensure it doesn't take full width */
            position: relative; /* Add this for better positioning */
            left: 0; /* Explicitly align to the left */
        }

        .news-content p {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
            max-height: 200px; /* Allow for more text */
            overflow: auto; /* Add scrollbar if needed */
        }

        .read-more-btn {
            display: inline-block;
            background-color: #f1ca2f;
            color: #333;
            font-weight: 600;
            padding: 10px 25px;
            border: none;
            border-radius: 0;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
            font-size: 14px;
            text-transform: uppercase;
            text-align: center;
            width: auto !important;
            max-width: fit-content !important;
            margin-top: 10px;
            float: none;
        }

        .read-more-btn:hover {
            background-color: #e0bc20;
            color: #333; /* Ensure text color doesn't change on hover */
        }

        @media (max-width: 992px) {
            .news-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* News date styles */
        .news-date {
            display: block;
            margin-bottom: 10px;
            color: #666;
            font-size: 12px;
            text-align: left;
        }

        /* Pagination styles */
        .pagination {
            margin-top: 40px;
            margin-bottom: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .pagination-info {
            margin-bottom: 15px;
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .pagination-btn {
            background-color: #3c3c45;
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.3s;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: #4a4a52;
        }

        .pagination-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .page-numbers {
            display: flex;
            gap: 5px;
        }

        .page-number {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background-color: #f5f5f5;
            color: #333;
            cursor: pointer;
            transition: all 0.3s;
        }

        .page-number:hover {
            background-color: #e0e0e0;
        }

        .page-number.active {
            background-color: #f1ca2f;
            color: #333;
            font-weight: bold;
        }

        .page-ellipsis {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block !important;
            }

            .main-nav {
                display: none !important;
            }

            .main-nav.active {
                display: block !important;
            }

            .logo {
                text-align: left !important;
            }

            .main-header .container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .news-page-banner {
                margin-top: 60px !important;
                padding: 40px 0 !important;
            }

            .news-grid {
                grid-template-columns: 1fr !important;
            }

            .news-item {
                margin-bottom: 30px !important;
            }

            .pagination-controls {
                flex-wrap: wrap;
            }

            .page-numbers {
                order: 3;
                width: 100%;
                justify-content: center;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="Manage Incorporated - Established in 1995">
                </a>
            </div>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html">HOME</a></li>
                    <li><a href="cloud.html">CLOUD</a></li>
                    <li><a href="managed-services.html">MANAGED SERVICES</a></li>
                    <li><a href="infrastructure.html">INFRASTRUCTURE</a></li>
                    <li><a href="services.html">SERVICES</a></li>
                    <li class="active"><a href="news.html">NEWS</a></li>
                    <li><a href="contact-us.html">CONTACT US</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="news-page-banner">
            <div class="container">
                <h1>News</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <div id="noNewsMessage" style="text-align: center; padding: 80px 0; display: none;">
                    <h2>Stay Tuned for Updates</h2>
                    <p>We're working on exciting news and updates. Check back soon!</p>
                    <div style="margin-top: 30px;">
                        <i class="fas fa-newspaper" style="font-size: 48px; color: #f1ca2f; margin-bottom: 15px;"></i>
                    </div>
                </div>

                <div id="loadingMessage" style="text-align: center; padding: 50px 0;">
                    <h2>Loading news...</h2>
                    <p>Please wait while we fetch the latest news.</p>
                </div>

                <div id="noNewsMessage" style="text-align: center; padding: 50px 0; display: none;">
                    <h2>Stay Tuned for Updates</h2>
                    <p>We're working on exciting news and updates. Check back soon!</p>
                </div>

                <div id="newsGrid" class="news-grid" style="display: none;">
                    <!-- News items will be loaded here via JavaScript -->
                </div>

                <!-- Pagination controls -->
                <div id="pagination" class="pagination" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text">Showing page 1 of 1</span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prev-page" class="pagination-btn" disabled><i class="fas fa-chevron-left"></i> Previous</button>
                        <div id="page-numbers" class="page-numbers">
                            <!-- Page numbers will be added here -->
                        </div>
                        <button id="next-page" class="pagination-btn" disabled>Next <i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>

                <!-- Fallback content in case AJAX fails -->
                <div id="fallbackNewsGrid" class="news-grid" style="display: none;">
                    <div id="errorMessage" style="text-align: center; padding: 30px; background-color: #f8f8f8; border-radius: 5px; width: 100%; grid-column: 1 / -1;">
                        <h3>Unable to Load News</h3>
                        <p>We're having trouble loading the latest news. Please try refreshing the page or check back later.</p>
                        <button onclick="location.reload()" style="background: #f1ca2f; border: none; padding: 10px 20px; border-radius: 4px; margin-top: 15px; cursor: pointer;">Refresh Page</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <!-- Footer Top Section -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-top-content">
                    <div class="footer-info">
                        <h2>BUILD A FUTURE-READY IT INFRASTRUCTURE</h2>
                        <p>Leverage cutting-edge technology to optimize performance, security, and scalability.</p>
                    </div>
                    <div class="footer-form">
                        <form method="post" action="process-contact.php" id="footer-contact-form">
                            <input type="hidden" name="source" value="Footer Form">
                            <div class="form-row">
                                <input type="text" name="name" placeholder="Name" required>
                                <input type="email" name="email" placeholder="Email Address" required>
                            </div>
                            <textarea name="message" placeholder="Message" required></textarea>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section -->
        <div class="footer-middle">
            <div class="container">
                <div class="footer-middle-content">
                    <div class="footer-logo">
                        <img src="images/footer/manage-inc-logo.png" alt="Manage Incorporated">
                    </div>
                    <div class="footer-services">
                        <h4>SERVICES</h4>
                        <ul>
                            <li><a href="cloud.html">Cloud</a></li>
                            <li><a href="managed-services.html">Managed Services</a></li>
                            <li><a href="infrastructure.html">Infrastructure</a></li>
                            <li><a href="services.html">Services</a></li>
                        </ul>
                    </div>
                    <div class="footer-contact">
                        <h4>Seattle, WA Office</h4>
                        <p><img src="images/footer/phone-icon.svg" alt="Phone"> (*************</p>
                        <p><img src="images/footer/fax-icon.svg" alt="Fax"> (*************</p>
                        <p><img src="images/footer/email-icon.svg" alt="Email"> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><img src="images/footer/location-icon.svg" alt="Location"> 600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <p>© Copyright 2025. Manage Inc. all Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/carousel.js?v=20250541"></script>
    <script src="js/mobile-menu.js?v=20250541"></script>
    <script src="js/submenu-mobile.js?v=20250541"></script>
    <script src="js/footer-form.js?v=20250546"></script>
    <script>
        // Version 1.0.1 - Updated to fix caching issues
        // Ensure mobile menu is working
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.toggle('active');
                    document.getElementById('mainNav').classList.toggle('active');
                });
            }

            // Fetch news data
            fetchNews();
        });

        // Current page
        let currentPage = 1;

        // Function to fetch news data from news-data.php with pagination
        function fetchNews(page = 1) {
            // Add timestamp to prevent caching
            const timestamp = new Date().getTime();

            // Use news-data.php as the primary data source
            const path = `news-data.php?page=${page}&t=${timestamp}`;

            // Define paths for reference
            const paths = [
                `news-data.php?page=${page}&t=${timestamp}`
            ];

            // Log debugging information
            console.log('Current URL:', window.location.href);
            console.log('Current path:', window.location.pathname);
            console.log('Trying paths:', paths);

            console.log('Trying to fetch news from path:', path);

            // Show loading message
            document.getElementById('loadingMessage').style.display = 'block';
            document.getElementById('newsGrid').style.display = 'none';
            document.getElementById('pagination').style.display = 'none';

            // Fetch news data from the server
            fetch(path)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    // Hide loading message
                    document.getElementById('loadingMessage').style.display = 'none';

                    // Show "no news" message
                    document.getElementById('noNewsMessage').style.display = 'block';
                    console.error('Error fetching news:', error);

                    return {
                        items: [],
                        no_news: true,
                        error: 'Failed to fetch news: ' + error.message
                    };
                })
                .then(data => {
                    // Hide loading message
                    document.getElementById('loadingMessage').style.display = 'none';

                    // Log the response for debugging
                    console.log('News data response:', data);

                    // Log detailed debug information
                    if (data && data.debug) {
                        console.log('Debug info:', data.debug);
                        if (data.debug.database_info) {
                            console.log('Database info:', data.debug.database_info);
                        }
                    }

                    // Log error information
                    if (data && data.error) {
                        console.error('Error from API:', data.error);
                    }

                    // Check if there are news items
                    if (!data || !data.items || data.items.length === 0 || data.no_news === true) {
                        console.log('No news items found or error in response');
                        document.getElementById('noNewsMessage').style.display = 'block';

                        // Show error message if available
                        if (data && data.error) {
                            console.error('API Error:', data.error);
                            document.getElementById('errorMessage').textContent = 'Error: ' + data.error;
                            document.getElementById('fallbackNewsGrid').style.display = 'grid';
                        }
                        return;
                    }

                    console.log('Found ' + data.items.length + ' news items to display');

                    // Display news items
                    const newsGrid = document.getElementById('newsGrid');
                    newsGrid.innerHTML = ''; // Clear any existing content

                    data.items.forEach(news => {
                        const newsItem = document.createElement('div');
                        newsItem.className = 'news-item';

                        // Create image element
                        const imageUrl = 'images/news/' + news.image;

                        // Build HTML based on settings
                        let newsHTML = `
                            <div class="news-image">
                                <img src="${imageUrl}"
                                     alt="${news.title}"
                                     onerror="this.src='images/news/news-banner.jpg'">
                            </div>
                            <div class="news-content">
                                <h2>${news.title}</h2>`;

                        // Only show date if setting is enabled
                        if (news.show_date) {
                            newsHTML += `
                                <div class="news-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span class="news-date-formatted">${news.created_at}</span>
                                </div>`;
                        }

                        // Only show category if setting is enabled
                        if (news.show_category) {
                            newsHTML += `
                                <div style="text-align: left; width: 100%;">
                                    <div class="news-category">
                                        ${news.category_name}
                                    </div>
                                </div>`;
                        }

                        newsHTML += `
                                <p>${news.content}</p>
                                <div style="text-align: left; width: 100%;">
                                    <a href="${news.slug}.html" class="read-more-btn">Read More</a>
                                </div>
                            </div>`;

                        newsItem.innerHTML = newsHTML;

                        newsGrid.appendChild(newsItem);
                    });

                    // Show the news grid
                    newsGrid.style.display = 'grid';

                    // Get pagination position from settings
                    const paginationPosition = data.pagination_position || 'bottom';

                    // Show pagination at top if needed
                    if (paginationPosition === 'top' || paginationPosition === 'both') {
                        // Move pagination before news grid
                        const pagination = document.getElementById('pagination');
                        const newsGridParent = newsGrid.parentNode;
                        newsGridParent.insertBefore(pagination, newsGrid);
                    }

                    // Update pagination if total items > items_per_page
                    if (data.total_items > data.items_per_page) {
                        updatePagination(data);
                    }
                })
                .catch(error => {
                    // Hide loading message
                    document.getElementById('loadingMessage').style.display = 'none';

                    // Show "no news" message instead of error
                    document.getElementById('noNewsMessage').style.display = 'block';
                    console.error('Error fetching news:', error);
                });
        }

        // Function to update pagination controls
        function updatePagination(data) {
            const pagination = document.getElementById('pagination');
            const paginationInfo = document.getElementById('pagination-info-text');
            const pageNumbers = document.getElementById('page-numbers');
            const prevButton = document.getElementById('prev-page');
            const nextButton = document.getElementById('next-page');

            // Update current page
            currentPage = data.current_page;

            // Update pagination info text
            paginationInfo.textContent = `Showing page ${currentPage} of ${data.total_pages}`;

            // Clear existing page numbers
            pageNumbers.innerHTML = '';

            // Generate page numbers
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(data.total_pages, startPage + maxVisiblePages - 1);

            // Adjust start page if we're near the end
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // Add first page if not included
            if (startPage > 1) {
                const firstPage = document.createElement('div');
                firstPage.className = 'page-number';
                firstPage.textContent = '1';
                firstPage.addEventListener('click', () => fetchNews(1));
                pageNumbers.appendChild(firstPage);

                // Add ellipsis if needed
                if (startPage > 2) {
                    const ellipsis = document.createElement('div');
                    ellipsis.className = 'page-ellipsis';
                    ellipsis.textContent = '...';
                    pageNumbers.appendChild(ellipsis);
                }
            }

            // Add page numbers
            for (let i = startPage; i <= endPage; i++) {
                const pageNumber = document.createElement('div');
                pageNumber.className = 'page-number';
                if (i === currentPage) {
                    pageNumber.classList.add('active');
                }
                pageNumber.textContent = i;
                pageNumber.addEventListener('click', () => fetchNews(i));
                pageNumbers.appendChild(pageNumber);
            }

            // Add last page if not included
            if (endPage < data.total_pages) {
                // Add ellipsis if needed
                if (endPage < data.total_pages - 1) {
                    const ellipsis = document.createElement('div');
                    ellipsis.className = 'page-ellipsis';
                    ellipsis.textContent = '...';
                    pageNumbers.appendChild(ellipsis);
                }

                const lastPage = document.createElement('div');
                lastPage.className = 'page-number';
                lastPage.textContent = data.total_pages;
                lastPage.addEventListener('click', () => fetchNews(data.total_pages));
                pageNumbers.appendChild(lastPage);
            }

            // Update prev/next buttons
            prevButton.disabled = currentPage === 1;
            nextButton.disabled = currentPage === data.total_pages;

            // Add event listeners to prev/next buttons
            prevButton.onclick = () => fetchNews(currentPage - 1);
            nextButton.onclick = () => fetchNews(currentPage + 1);

            // Show pagination
            pagination.style.display = 'flex';
        }
    </script>
</body>
</html>
