<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_settings')) {
    $_SESSION['error_message'] = "You do not have permission to manage system settings.";
    header('Location: dashboard.php');
    exit;
}

// Define settings categories
$categories = [
    'general' => 'General',
    'email' => 'Email',
    'appearance' => 'Appearance',
    'fonts' => 'Fonts',
    'news' => 'News',
    'security' => 'Security'
];

// Define category icons
$category_icons = [
    'general' => 'fa-cog',
    'email' => 'fa-envelope',
    'appearance' => 'fa-palette',
    'fonts' => 'fa-font',
    'news' => 'fa-newspaper',
    'security' => 'fa-shield-alt'
];

// Get active category from URL or default to first category
$active_category = isset($_GET['category']) && array_key_exists($_GET['category'], $categories) ? $_GET['category'] : 'general';

// Initialize variables
$success = '';
$error = '';

// Function to get settings by category
function getSettingsByCategory($conn, $category) {
    $stmt = $conn->prepare("SELECT * FROM system_settings WHERE category = ? ORDER BY order_index");
    $stmt->bind_param("s", $category);
    $stmt->execute();
    $result = $stmt->get_result();

    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row;
    }

    return $settings;
}

// Function to update a setting
function updateSetting($conn, $category, $key, $value) {
    $stmt = $conn->prepare("UPDATE system_settings SET setting_value = ? WHERE category = ? AND setting_key = ?");
    $stmt->bind_param("sss", $value, $category, $key);
    return $stmt->execute();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $category = $_POST['category'];
    $category_settings = getSettingsByCategory($conn, $category);

    foreach ($category_settings as $key => $setting) {
        if ($setting['setting_type'] === 'checkbox') {
            $value = isset($_POST[$key]) ? '1' : '0';
        } else {
            $value = isset($_POST[$key]) ? trim($_POST[$key]) : '';
        }

        // Validate required fields
        if ($setting['is_required'] && empty($value)) {
            $error = $setting['display_name'] . " is required";
            break;
        }

        // Validate email fields
        if ($setting['setting_type'] === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format for " . $setting['display_name'];
            break;
        }

        // Update setting
        if (!updateSetting($conn, $category, $key, $value)) {
            $error = "Failed to save " . $setting['display_name'];
            break;
        }
    }

    if (empty($error)) {
        $success = "Settings saved successfully";
    }
}

// Get settings for the active category
$category_settings = getSettingsByCategory($conn, $active_category);

// Set page title and metadata
$page_title = "Settings";
$page_icon = 'fas fa-cog';
$page_subtitle = 'Configure system settings and preferences';
?>
<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
    </div>

    <?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="settings-container">
            <div class="settings-sidebar">
                <ul class="settings-nav">
                    <?php foreach ($categories as $category_key => $category_name): ?>
                    <li class="settings-nav-item <?php echo $active_category === $category_key ? 'active' : ''; ?>" 
                        data-target="<?php echo $category_key; ?>-settings">
                        <a href="settings.php?category=<?php echo $category_key; ?>">
                            <i class="fas <?php echo $category_icons[$category_key]; ?>"></i> <?php echo $category_name; ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="settings-content">
                <form method="post" action="settings.php?category=<?php echo $active_category; ?>" class="settings-form">
                    <input type="hidden" name="category" value="<?php echo $active_category; ?>">
                    
                    <div class="settings-section active" id="<?php echo $active_category; ?>-settings">
                        <h3 class="settings-section-title">
                            <i class="fas <?php echo $category_icons[$active_category]; ?>"></i>
                            <?php echo $categories[$active_category]; ?> Settings
                        </h3>

                        <?php if (empty($category_settings)): ?>
                            <div class="no-settings">
                                <i class="fas fa-info-circle"></i>
                                <p>No settings available for this category.</p>
                            </div>
                        <?php else: ?>
                            <!-- Simple settings display like user_settings.php -->
                            <?php foreach ($category_settings as $key => $setting): ?>
                                <div class="form-group">
                                    <label for="<?php echo $key; ?>">
                                        <?php echo htmlspecialchars($setting['display_name']); ?>
                                        <?php if ($setting['is_required']): ?>
                                            <span class="required">*</span>
                                        <?php endif; ?>
                                    </label>
                                    
                                    <?php if (!empty($setting['description'])): ?>
                                        <p class="form-hint"><?php echo htmlspecialchars($setting['description']); ?></p>
                                    <?php endif; ?>

                                    <?php if ($setting['setting_type'] === 'text'): ?>
                                        <input type="text" id="<?php echo $key; ?>" name="<?php echo $key; ?>" 
                                               class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                               <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                    <?php elseif ($setting['setting_type'] === 'email'): ?>
                                        <input type="email" id="<?php echo $key; ?>" name="<?php echo $key; ?>" 
                                               class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                               <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                    <?php elseif ($setting['setting_type'] === 'number'): ?>
                                        <input type="number" id="<?php echo $key; ?>" name="<?php echo $key; ?>" 
                                               class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                               <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                    <?php elseif ($setting['setting_type'] === 'password'): ?>
                                        <input type="password" id="<?php echo $key; ?>" name="<?php echo $key; ?>" 
                                               class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">

                                    <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                        <textarea id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control" rows="3"
                                                  <?php echo $setting['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                    <?php elseif ($setting['setting_type'] === 'checkbox'): ?>
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="<?php echo $key; ?>" value="1" 
                                                   <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                            <span>Enable</span>
                                        </label>

                                    <?php elseif ($setting['setting_type'] === 'select'): ?>
                                        <select id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control"
                                                <?php echo $setting['is_required'] ? 'required' : ''; ?>>
                                            <?php
                                            $options = json_decode($setting['options'], true);
                                            if ($options):
                                                foreach ($options as $value => $label): ?>
                                                    <option value="<?php echo htmlspecialchars($value); ?>"
                                                            <?php echo $setting['setting_value'] === $value ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($label); ?>
                                                    </option>
                                                <?php endforeach;
                                            endif; ?>
                                        </select>

                                    <?php elseif ($setting['setting_type'] === 'color'): ?>
                                        <input type="color" id="<?php echo $key; ?>" name="<?php echo $key; ?>" 
                                               class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">

                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="form-actions">
                            <button type="submit" name="save_settings" class="admin-btn">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Alert auto-dismissal is handled by the global alert-auto-dismiss.js script
</script>

<?php include 'includes/footer.php'; ?>
