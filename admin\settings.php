<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_settings')) {
    $_SESSION['error_message'] = "You do not have permission to manage system settings.";
    header('Location: dashboard.php');
    exit;
}

// Define settings categories
$categories = [
    'general' => 'General',
    'email' => 'Email',
    'appearance' => 'Appearance',
    'fonts' => 'Fonts',
    'news' => 'News',
    'security' => 'Security'
];

// Define category icons
$category_icons = [
    'general' => 'fa-cog',
    'email' => 'fa-envelope',
    'appearance' => 'fa-palette',
    'fonts' => 'fa-font',
    'news' => 'fa-newspaper',
    'security' => 'fa-shield-alt'
];

// Get active category from URL or default to first category
$active_category = isset($_GET['category']) && array_key_exists($_GET['category'], $categories) ? $_GET['category'] : 'general';

// Initialize variables
$success = '';
$error = '';

// Function to get settings by category
function getSettingsByCategory($conn, $category) {
    $stmt = $conn->prepare("SELECT * FROM system_settings WHERE category = ? ORDER BY order_index");
    $stmt->bind_param("s", $category);
    $stmt->execute();
    $result = $stmt->get_result();

    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row;
    }

    return $settings;
}

// Function to update a setting
function updateSetting($conn, $category, $key, $value) {
    $stmt = $conn->prepare("UPDATE system_settings SET setting_value = ? WHERE category = ? AND setting_key = ?");
    $stmt->bind_param("sss", $value, $category, $key);
    return $stmt->execute();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $category = $_POST['category'];
    $category_settings = getSettingsByCategory($conn, $category);

    foreach ($category_settings as $key => $setting) {
        if ($setting['setting_type'] === 'checkbox') {
            $value = isset($_POST[$key]) ? '1' : '0';
        } else {
            $value = isset($_POST[$key]) ? trim($_POST[$key]) : '';
        }

        // Validate required fields
        if ($setting['is_required'] && empty($value)) {
            $error = $setting['display_name'] . " is required";
            break;
        }

        // Validate email fields
        if ($setting['setting_type'] === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format for " . $setting['display_name'];
            break;
        }

        // Update setting
        if (!updateSetting($conn, $category, $key, $value)) {
            $error = "Failed to save " . $setting['display_name'];
            break;
        }
    }

    // Process email templates if this is the email category
    if (empty($error) && $category === 'email') {
        // Handle template updates
        if (isset($_POST['template_subject']) && isset($_POST['template_content'])) {
            require_once 'lib/EmailTemplates.php';
            $emailTemplates = new EmailTemplates($conn);

            foreach ($_POST['template_subject'] as $template_id => $subject) {
                $content = $_POST['template_content'][$template_id] ?? '';
                if (!empty($subject) && !empty($content)) {
                    $emailTemplates->updateTemplate($template_id, $subject, $content);
                }
            }
        }
    }

    if (empty($error)) {
        $success = "Settings saved successfully";
    }
}

// Get settings for the active category
$category_settings = getSettingsByCategory($conn, $active_category);

// Set page title and metadata
$page_title = "Settings";
$page_icon = 'fas fa-cog';
$page_subtitle = 'Configure system settings and preferences';
?>
<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content">
        <?php include 'includes/content-header.php'; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <div class="admin-content-full">
            <!-- Settings Navigation Tabs -->
            <div class="settings-tabs">
                <?php foreach ($categories as $category_key => $category_name): ?>
                    <a href="settings.php?category=<?php echo $category_key; ?>"
                       class="settings-tab <?php echo $active_category === $category_key ? 'active' : ''; ?>">
                        <i class="fas <?php echo $category_icons[$category_key]; ?>"></i>
                        <span><?php echo $category_name; ?></span>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- Settings Form -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <h3>
                        <i class="fas <?php echo $category_icons[$active_category]; ?>"></i>
                        <?php echo $categories[$active_category]; ?> Settings
                    </h3>
                </div>
                <div class="settings-card-body">
                <form method="post" action="settings.php?category=<?php echo $active_category; ?>">
                    <input type="hidden" name="category" value="<?php echo $active_category; ?>">

                    <?php if (empty($category_settings)): ?>
                        <div class="no-settings">
                            <i class="fas fa-info-circle"></i>
                            <p>No settings available for this category.</p>
                        </div>
                    <?php else: ?>
                        <?php
                        // Determine grid columns based on number of settings
                        $setting_count = count($category_settings);
                        $grid_class = 'settings-grid-2'; // Default to 2 columns

                        if ($setting_count >= 9) {
                            $grid_class = 'settings-grid-3'; // 3 columns for 9+ settings
                        } elseif ($setting_count >= 4) {
                            $grid_class = 'settings-grid-2'; // 2 columns for 4-8 settings
                        } else {
                            $grid_class = 'settings-grid-1'; // 1 column for 1-3 settings
                        }
                        ?>

                        <?php
                        // Organize email settings into sections
                        $email_sections = [
                            'basic' => [
                                'title' => 'Basic Email Settings',
                                'settings' => ['from_email', 'from_name', 'reply_to']
                            ],
                            'smtp' => [
                                'title' => 'SMTP Configuration',
                                'settings' => ['use_smtp', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_security']
                            ],
                            'templates' => [
                                'title' => 'Email Templates',
                                'settings' => [] // Will be populated with template settings
                            ]
                        ];

                        // Check if this is the email category
                        $is_email_category = $active_category === 'email';
                        ?>

                        <?php if ($is_email_category): ?>
                            <?php foreach ($email_sections as $section_key => $section): ?>
                                <div class="settings-section">
                                    <h3 class="settings-section-title">
                                        <i class="fas <?php echo $section_key === 'basic' ? 'fa-envelope' : ($section_key === 'smtp' ? 'fa-server' : 'fa-file-alt'); ?>"></i>
                                        <?php echo $section['title']; ?>
                                    </h3>
                                    <div class="settings-section-grid">
                                        <?php foreach ($section['settings'] as $setting_key): ?>
                                            <?php if (isset($category_settings[$setting_key])): ?>
                                                <?php $setting = $category_settings[$setting_key]; ?>
                                                <div class="setting-item <?php echo (strpos($setting_key, 'smtp_') === 0 && $setting_key !== 'use_smtp') ? 'smtp-setting' : ''; ?>">
                                                    <div class="setting-label">
                                                        <label for="<?php echo $setting_key; ?>">
                                                            <?php echo htmlspecialchars($setting['display_name']); ?>
                                                            <?php if ($setting['is_required']): ?>
                                                                <span class="required">*</span>
                                                            <?php endif; ?>
                                                        </label>
                                                        <?php if (!empty($setting['description'])): ?>
                                                            <small class="setting-description">
                                                                <?php echo htmlspecialchars($setting['description']); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>

                                    <div class="setting-input">
                                        <?php if ($setting['setting_type'] === 'text'): ?>
                                            <input type="text"
                                                   id="<?php echo $setting_key; ?>"
                                                   name="<?php echo $setting_key; ?>"
                                                   class="form-control"
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                   <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                        <?php elseif ($setting['setting_type'] === 'email'): ?>
                                            <input type="email"
                                                   id="<?php echo $setting_key; ?>"
                                                   name="<?php echo $setting_key; ?>"
                                                   class="form-control"
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                   <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                        <?php elseif ($setting['setting_type'] === 'number'): ?>
                                            <input type="number"
                                                   id="<?php echo $setting_key; ?>"
                                                   name="<?php echo $setting_key; ?>"
                                                   class="form-control"
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                   <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                        <?php elseif ($setting['setting_type'] === 'password'): ?>
                                            <div class="password-input-group">
                                                <input type="password"
                                                       id="<?php echo $setting_key; ?>"
                                                       name="<?php echo $setting_key; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                <button type="button" class="password-toggle" onclick="togglePassword('<?php echo $setting_key; ?>')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>

                                        <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                            <textarea id="<?php echo $setting_key; ?>"
                                                      name="<?php echo $setting_key; ?>"
                                                      class="form-control"
                                                      rows="3"
                                                      <?php echo $setting['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                        <?php elseif ($setting['setting_type'] === 'checkbox'): ?>
                                            <div class="form-check-custom">
                                                <input type="checkbox"
                                                       id="<?php echo $setting_key; ?>"
                                                       name="<?php echo $setting_key; ?>"
                                                       class="form-check-input <?php echo $setting_key === 'use_smtp' ? 'smtp-toggle' : ''; ?>"
                                                       value="1"
                                                       <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="<?php echo $setting_key; ?>">
                                                    <span class="checkmark"></span>
                                                    Enable
                                                </label>
                                            </div>

                                        <?php elseif ($setting['setting_type'] === 'select'): ?>
                                            <select id="<?php echo $setting_key; ?>"
                                                    name="<?php echo $setting_key; ?>"
                                                    class="form-control"
                                                    <?php echo $setting['is_required'] ? 'required' : ''; ?>>
                                                <?php
                                                $options = json_decode($setting['options'], true);
                                                if ($options):
                                                    foreach ($options as $value => $label): ?>
                                                        <option value="<?php echo htmlspecialchars($value); ?>"
                                                                <?php echo $setting['setting_value'] === $value ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($label); ?>
                                                        </option>
                                                    <?php endforeach;
                                                endif; ?>
                                            </select>

                                        <?php elseif ($setting['setting_type'] === 'color'): ?>
                                            <div class="color-input-group">
                                                <input type="color"
                                                       id="<?php echo $setting_key; ?>"
                                                       name="<?php echo $setting_key; ?>"
                                                       class="form-control color-picker"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                <input type="text"
                                                       class="form-control color-text"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       onchange="document.getElementById('<?php echo $setting_key; ?>').value = this.value">
                                            </div>

                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>

                                    <?php if ($section_key === 'templates'): ?>
                                        <!-- Email Templates Section -->
                                        <?php
                                        // Load email templates
                                        require_once 'lib/EmailTemplates.php';
                                        $emailTemplates = new EmailTemplates($conn);
                                        $templates = $emailTemplates->getAllTemplates();
                                        ?>

                                        <div class="email-templates-container">
                                            <?php if (!empty($templates)): ?>
                                                <div class="templates-grid">
                                                    <?php foreach ($templates as $template): ?>
                                                        <div class="template-card">
                                                            <div class="template-card-header">
                                                                <div class="template-card-title">
                                                                    <h4><?php echo htmlspecialchars($template['template_name']); ?></h4>
                                                                    <span class="template-key"><?php echo htmlspecialchars($template['template_key']); ?></span>
                                                                </div>
                                                                <div class="template-card-actions">
                                                                    <a href="edit_template.php?id=<?php echo $template['id']; ?>"
                                                                       class="template-edit-btn"
                                                                       title="Edit Template">
                                                                        <i class="fas fa-edit"></i>
                                                                    </a>
                                                                </div>
                                                            </div>

                                                            <div class="template-card-body">
                                                                <div class="template-preview">
                                                                    <div class="template-subject">
                                                                        <strong>Subject:</strong>
                                                                        <span><?php echo htmlspecialchars($template['subject']); ?></span>
                                                                    </div>
                                                                    <div class="template-content-preview">
                                                                        <strong>Content Preview:</strong>
                                                                        <p><?php echo htmlspecialchars(substr($template['content'], 0, 150)) . (strlen($template['content']) > 150 ? '...' : ''); ?></p>
                                                                    </div>
                                                                    <?php if (!empty($template['variables'])): ?>
                                                                        <div class="template-variables">
                                                                            <strong>Variables:</strong>
                                                                            <span class="variables-list"><?php echo htmlspecialchars($template['variables']); ?></span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>

                                                                <div class="template-meta">
                                                                    <span class="template-status <?php echo $template['is_active'] ? 'active' : 'inactive'; ?>">
                                                                        <i class="fas fa-circle"></i>
                                                                        <?php echo $template['is_active'] ? 'Active' : 'Inactive'; ?>
                                                                    </span>
                                                                    <span class="template-updated">
                                                                        Updated: <?php echo date('M j, Y', strtotime($template['updated_at'])); ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="no-templates">
                                                    <div class="no-templates-icon">
                                                        <i class="fas fa-envelope-open"></i>
                                                    </div>
                                                    <h3>No Email Templates Found</h3>
                                                    <p>Default templates will be created automatically when you save settings.</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- Non-email categories use regular grid layout -->
                            <div class="settings-grid <?php echo $grid_class; ?>">
                                <?php foreach ($category_settings as $key => $setting): ?>
                                    <div class="setting-item">
                                        <div class="setting-label">
                                            <label for="<?php echo $key; ?>">
                                                <?php echo htmlspecialchars($setting['display_name']); ?>
                                                <?php if ($setting['is_required']): ?>
                                                    <span class="required">*</span>
                                                <?php endif; ?>
                                            </label>
                                            <?php if (!empty($setting['description'])): ?>
                                                <small class="setting-description">
                                                    <?php echo htmlspecialchars($setting['description']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>

                                        <div class="setting-input">
                                            <?php if ($setting['setting_type'] === 'text'): ?>
                                                <input type="text"
                                                       id="<?php echo $key; ?>"
                                                       name="<?php echo $key; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'email'): ?>
                                                <input type="email"
                                                       id="<?php echo $key; ?>"
                                                       name="<?php echo $key; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'number'): ?>
                                                <input type="number"
                                                       id="<?php echo $key; ?>"
                                                       name="<?php echo $key; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'password'): ?>
                                                <div class="password-input-group">
                                                    <input type="password"
                                                           id="<?php echo $key; ?>"
                                                           name="<?php echo $key; ?>"
                                                           class="form-control"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <button type="button" class="password-toggle" onclick="togglePassword('<?php echo $key; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                                <textarea id="<?php echo $key; ?>"
                                                          name="<?php echo $key; ?>"
                                                          class="form-control"
                                                          rows="3"
                                                          <?php echo $setting['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                            <?php elseif ($setting['setting_type'] === 'checkbox'): ?>
                                                <div class="form-check-custom">
                                                    <input type="checkbox"
                                                           id="<?php echo $key; ?>"
                                                           name="<?php echo $key; ?>"
                                                           class="form-check-input"
                                                           value="1"
                                                           <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="<?php echo $key; ?>">
                                                        <span class="checkmark"></span>
                                                        Enable
                                                    </label>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'select'): ?>
                                                <select id="<?php echo $key; ?>"
                                                        name="<?php echo $key; ?>"
                                                        class="form-control"
                                                        <?php echo $setting['is_required'] ? 'required' : ''; ?>>
                                                    <?php
                                                    $options = json_decode($setting['options'], true);
                                                    if ($options):
                                                        foreach ($options as $value => $label): ?>
                                                            <option value="<?php echo htmlspecialchars($value); ?>"
                                                                    <?php echo $setting['setting_value'] === $value ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($label); ?>
                                                            </option>
                                                        <?php endforeach;
                                                    endif; ?>
                                                </select>

                                            <?php elseif ($setting['setting_type'] === 'color'): ?>
                                                <div class="color-input-group">
                                                    <input type="color"
                                                           id="<?php echo $key; ?>"
                                                           name="<?php echo $key; ?>"
                                                           class="form-control color-picker"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <input type="text"
                                                           class="form-control color-text"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                           onchange="document.getElementById('<?php echo $key; ?>').value = this.value">
                                                </div>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <div class="form-actions">
                            <button type="submit" name="save_settings" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        </div>
    </div>
</div>

<style>
/* Full-width container */
.admin-container {
    width: 100%;
    max-width: none;
    margin: 0;
    min-height: calc(100vh - 80px); /* Account for header height */
    display: flex;
    flex-direction: column;
}

.admin-content {
    width: 100%;
    max-width: none;
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.admin-content-full {
    width: 100%;
    max-width: none;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Settings tabs */
.settings-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 6px;
    margin-bottom: 24px;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    width: 100%;
    overflow-x: auto;
}

.settings-tab {
    flex: 1;
    min-width: 120px;
    padding: 14px 20px;
    text-decoration: none;
    color: #6c757d;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 15px;
    white-space: nowrap;
}

.settings-tab.active {
    background: #f1ca2f;
    color: #333;
    font-weight: 700;
}

.settings-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.settings-tab.active:hover {
    background: #f1ca2f;
    color: #333;
}

.settings-tab i {
    font-size: 16px;
}

/* Settings card */
.settings-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flex shrinking */
}

.settings-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px 32px;
    border-bottom: 1px solid #dee2e6;
}

.settings-card-header h3 {
    margin: 0;
    color: #495057;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-card-header i {
    color: #f1ca2f;
    font-size: 28px;
}

.settings-card-body {
    padding: 32px;
    flex: 1;
    overflow-y: auto;
    min-height: 0; /* Allow flex shrinking */
}

/* Dynamic grid system */
.settings-grid {
    display: grid;
    gap: 32px;
    margin-bottom: 40px;
}

.settings-grid-1 {
    grid-template-columns: 1fr;
    max-width: 600px;
    margin: 0 auto;
}

.settings-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.settings-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

/* Settings sections */
.settings-section {
    margin-bottom: 40px;
}

.settings-section-title {
    color: #495057;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1ca2f;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-section-title i {
    color: #f1ca2f;
    font-size: 22px;
}

.settings-section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

/* Setting items */
.setting-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    border: 2px solid transparent;
}

.setting-label {
    margin-bottom: 16px;
}

.setting-label label {
    font-weight: 700;
    color: #495057;
    margin-bottom: 6px;
    display: block;
    font-size: 16px;
}

.setting-description {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
    display: block;
    margin-top: 4px;
}

.required {
    color: #dc3545;
    margin-left: 4px;
    font-weight: 700;
}

/* Form controls */
.form-control {
    width: 100%;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    border-color: #f1ca2f;
    box-shadow: 0 0 0 0.2rem rgba(241, 202, 47, 0.25);
    outline: none;
}

.form-control:hover {
    border-color: #adb5bd;
}

/* Password input group */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #f1ca2f;
}

/* Color input group */
.color-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.color-picker {
    width: 60px;
    height: 45px;
    padding: 4px;
    border-radius: 8px;
    cursor: pointer;
}

.color-text {
    flex: 1;
    font-family: monospace;
    text-transform: uppercase;
}

/* Custom checkbox */
.form-check-custom {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    position: relative;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.form-check-custom:hover {
    background-color: rgba(241, 202, 47, 0.1);
}

.form-check-custom input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    z-index: 1;
}

.checkmark {
    width: 24px;
    height: 24px;
    background: #fff;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.form-check-custom:hover .checkmark {
    border-color: #f1ca2f;
}

.form-check-custom input[type="checkbox"]:checked + label .checkmark {
    background: #f1ca2f;
    border-color: #f1ca2f;
}

.form-check-custom input[type="checkbox"]:checked + label .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-weight: bold;
    font-size: 14px;
}

.form-check-label {
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    position: relative;
    z-index: 2;
}

/* Ensure the entire checkbox area is clickable */
.form-check-custom,
.form-check-custom *,
.form-check-input {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Form actions */
.form-actions {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 2px solid #f8f9fa;
    display: flex;
    gap: 16px;
    justify-content: flex-start;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 15px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #f1ca2f;
    color: #fff;
    border-color: #f1ca2f;
}

.btn-primary:hover {
    background: #e6b800;
    border-color: #e6b800;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(241, 202, 47, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* No settings state */
.no-settings {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 12px;
}

.no-settings i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
    color: #adb5bd;
}

.no-settings p {
    font-size: 18px;
    margin: 0;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 1200px) {
    .settings-grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .admin-content {
        padding: 15px;
    }

    .settings-tabs {
        flex-wrap: wrap;
        gap: 4px;
    }

    .settings-tab {
        justify-content: flex-start;
        padding: 12px 16px;
        min-width: calc(50% - 2px);
    }

    .settings-grid-2,
    .settings-grid-3 {
        grid-template-columns: 1fr;
    }

    .settings-card-header {
        padding: 20px 24px;
    }

    .settings-card-header h3 {
        font-size: 20px;
    }

    .settings-card-body {
        padding: 24px;
    }

    .setting-item {
        padding: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .admin-content {
        padding: 10px;
    }

    .settings-tabs {
        flex-direction: column;
    }

    .settings-tab {
        min-width: 100%;
    }

    .settings-card-header {
        padding: 16px 20px;
    }

    .settings-card-body {
        padding: 20px;
    }

    .setting-item {
        padding: 16px;
    }

    .settings-grid {
        gap: 20px;
    }
}

/* Remove conflicting footer styles - let the default footer behavior work */

/* Email Templates Styles */
.email-templates-container {
    margin-top: 20px;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.template-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.template-card:hover {
    border-color: #f1ca2f;
    box-shadow: 0 4px 16px rgba(241, 202, 47, 0.15);
    transform: translateY(-2px);
}

.template-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.template-card-title h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 700;
}

.template-key {
    background: #e9ecef;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    font-weight: 600;
    text-transform: uppercase;
}

.template-card-actions {
    display: flex;
    gap: 8px;
}

.template-edit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #f1ca2f;
    color: #333;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 14px;
}

.template-edit-btn:hover {
    background: #e6b82a;
    color: #333;
    transform: scale(1.05);
    text-decoration: none;
}

.template-card-body {
    padding: 20px;
}

.template-preview {
    margin-bottom: 16px;
}

.template-subject {
    margin-bottom: 12px;
    font-size: 14px;
}

.template-subject strong {
    color: #495057;
}

.template-subject span {
    color: #6c757d;
}

.template-content-preview {
    margin-bottom: 12px;
    font-size: 14px;
}

.template-content-preview strong {
    color: #495057;
}

.template-content-preview p {
    margin: 4px 0 0 0;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

.template-variables {
    font-size: 13px;
    margin-bottom: 12px;
}

.template-variables strong {
    color: #495057;
}

.variables-list {
    color: #6c757d;
    font-family: monospace;
    font-size: 12px;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
    font-size: 12px;
}

.template-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    text-transform: uppercase;
}

.template-status.active {
    color: #28a745;
}

.template-status.inactive {
    color: #dc3545;
}

.template-status i {
    font-size: 8px;
}

.template-updated {
    color: #6c757d;
}

.no-templates {
    text-align: center;
    padding: 60px 40px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-templates-icon {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 20px;
}

.no-templates h3 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.no-templates p {
    margin: 0;
    font-size: 14px;
}

@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: 1fr;
    }

    .template-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .template-card-actions {
        align-self: flex-end;
    }
}
</style>

<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Reset form to original values
function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.querySelector('form').reset();

        // Reset color inputs
        document.querySelectorAll('.color-text').forEach(input => {
            const colorPicker = input.parentElement.querySelector('.color-picker');
            input.value = colorPicker.value;
        });
    }
}

// Enhanced form interactions
document.addEventListener('DOMContentLoaded', function() {
    // Color picker synchronization
    document.querySelectorAll('.color-input-group').forEach(group => {
        const colorPicker = group.querySelector('.color-picker');
        const colorText = group.querySelector('.color-text');

        if (colorPicker && colorText) {
            colorPicker.addEventListener('input', function() {
                colorText.value = this.value.toUpperCase();
            });

            colorText.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    colorPicker.value = this.value;
                }
            });
        }
    });

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let hasErrors = false;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#dc3545';
                    hasErrors = true;
                } else {
                    field.style.borderColor = '#dee2e6';
                }
            });

            if (hasErrors) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    }

    // Enhanced setting item interactions (focus effects removed)
    document.querySelectorAll('.setting-item').forEach(item => {
        const input = item.querySelector('input, select, textarea');

        if (input) {
            // Change detection only (no focus effects)
            const originalValue = input.value;
            input.addEventListener('input', function() {
                if (this.value !== originalValue) {
                    item.classList.add('changed');
                } else {
                    item.classList.remove('changed');
                }
            });
        }
    });

    // SMTP Settings Visibility Control
    initSMTPVisibility();

    // Smooth tab transitions
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            // Add loading state
            const content = document.querySelector('.settings-card-body');
            content.style.opacity = '0.7';
            content.style.pointerEvents = 'none';

            // Restore after navigation (this will be handled by page reload)
            setTimeout(() => {
                content.style.opacity = '1';
                content.style.pointerEvents = 'auto';
            }, 300);
        });
    });
});

// SMTP Settings Visibility Control Function
function initSMTPVisibility() {
    const useSmtpCheckbox = document.querySelector('input[name="use_smtp"]');

    if (!useSmtpCheckbox) {
        console.log('SMTP toggle not found - not on email settings page');
        return; // Exit if not on email settings page
    }

    console.log('SMTP toggle found:', useSmtpCheckbox);

    // Get all SMTP-related setting items (including smtp_security select)
    const smtpInputs = document.querySelectorAll('input[name^="smtp_"], select[name^="smtp_"]');
    const smtpSettings = Array.from(smtpInputs).map(input => {
        return input.closest('.setting-item');
    }).filter(item => item !== null);

    // Also get items with smtp-setting class
    const smtpClassItems = document.querySelectorAll('.smtp-setting');
    smtpClassItems.forEach(item => {
        if (!smtpSettings.includes(item)) {
            smtpSettings.push(item);
        }
    });

    // Function to toggle SMTP settings visibility
    function toggleSMTPSettings(show) {
        smtpSettings.forEach(item => {
            if (show) {
                item.style.display = '';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
                item.style.transition = 'all 0.3s ease';
            } else {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-10px)';
                item.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    if (!useSmtpCheckbox.checked) {
                        item.style.display = 'none';
                    }
                }, 300);
            }
        });

        // Update grid layout after visibility change
        setTimeout(() => {
            updateGridLayout();
        }, 350);
    }

    // Function to update grid layout based on visible items
    function updateGridLayout() {
        const settingsGrid = document.querySelector('.settings-grid');
        const visibleItems = Array.from(settingsGrid.children).filter(item => {
            return item.style.display !== 'none' && item.offsetHeight > 0;
        });

        const itemCount = visibleItems.length;

        // Remove existing grid classes
        settingsGrid.classList.remove('settings-grid-1', 'settings-grid-2', 'settings-grid-3');

        // Apply appropriate grid class based on visible item count
        if (itemCount >= 9) {
            settingsGrid.classList.add('settings-grid-3');
        } else if (itemCount >= 4) {
            settingsGrid.classList.add('settings-grid-2');
        } else {
            settingsGrid.classList.add('settings-grid-1');
        }
    }

    // Initial setup - set visibility based on current checkbox state
    toggleSMTPSettings(useSmtpCheckbox.checked);

    // Add event listener for checkbox changes
    useSmtpCheckbox.addEventListener('change', function() {
        toggleSMTPSettings(this.checked);

        // Add visual feedback to the Use SMTP setting item
        const useSmtpItem = this.closest('.setting-item');
        if (useSmtpItem) {
            if (this.checked) {
                useSmtpItem.style.borderColor = '#28a745';
                useSmtpItem.style.backgroundColor = '#f8fff9';
            } else {
                useSmtpItem.style.borderColor = '#dc3545';
                useSmtpItem.style.backgroundColor = '#fff8f8';
            }

            // Reset colors after animation
            setTimeout(() => {
                useSmtpItem.style.borderColor = 'transparent';
                useSmtpItem.style.backgroundColor = '#f8f9fa';
            }, 2000);
        }
    });

    // Add helper text to SMTP settings when disabled
    if (!useSmtpCheckbox.checked) {
        smtpSettings.forEach(item => {
            const label = item.querySelector('.setting-label label');
            if (label && !label.querySelector('.smtp-disabled-note')) {
                const note = document.createElement('small');
                note.className = 'smtp-disabled-note';
                note.style.color = '#6c757d';
                note.style.fontStyle = 'italic';
                note.textContent = ' (Enable "Use SMTP" to configure)';
                label.appendChild(note);
            }
        });
    }

    // Remove helper text when SMTP is enabled
    useSmtpCheckbox.addEventListener('change', function() {
        const notes = document.querySelectorAll('.smtp-disabled-note');
        if (this.checked) {
            notes.forEach(note => note.remove());
        } else {
            // Add notes back when disabled
            smtpSettings.forEach(item => {
                const label = item.querySelector('.setting-label label');
                if (label && !label.querySelector('.smtp-disabled-note')) {
                    const note = document.createElement('small');
                    note.className = 'smtp-disabled-note';
                    note.style.color = '#6c757d';
                    note.style.fontStyle = 'italic';
                    note.textContent = ' (Enable "Use SMTP" to configure)';
                    label.appendChild(note);
                }
            });
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
