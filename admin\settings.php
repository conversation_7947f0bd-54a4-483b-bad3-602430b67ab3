<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage settings
if (!$permissions->hasPermission('manage_settings')) {
    $_SESSION['error_message'] = "You do not have permission to manage system settings.";
    header('Location: dashboard.php');
    exit;
}

// Define settings categories
$categories = [
    'general' => 'General',
    'email' => 'Email',
    'appearance' => 'Appearance',
    'fonts' => 'Fonts',
    'news' => 'News',
    'security' => 'Security'
];

// Define category icons
$category_icons = [
    'general' => 'fa-cog',
    'email' => 'fa-envelope',
    'appearance' => 'fa-palette',
    'fonts' => 'fa-font',
    'news' => 'fa-newspaper',
    'security' => 'fa-shield-alt'
];

// Get active category from URL or default to first category
$active_category = isset($_GET['category']) && array_key_exists($_GET['category'], $categories) ? $_GET['category'] : 'general';

// Initialize variables
$success = '';
$error = '';

// Function to get settings by category
function getSettingsByCategory($conn, $category) {
    $stmt = $conn->prepare("SELECT * FROM system_settings WHERE category = ? ORDER BY order_index");
    $stmt->bind_param("s", $category);
    $stmt->execute();
    $result = $stmt->get_result();

    $settings = [];
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row;
    }

    return $settings;
}

// Function to update a setting
function updateSetting($conn, $category, $key, $value) {
    $stmt = $conn->prepare("UPDATE system_settings SET setting_value = ? WHERE category = ? AND setting_key = ?");
    $stmt->bind_param("sss", $value, $category, $key);
    return $stmt->execute();
}

// Function to get category descriptions
function getCategoryDescription($category) {
    $descriptions = [
        'general' => 'Basic site configuration and global settings',
        'email' => 'Email delivery and SMTP configuration',
        'appearance' => 'Visual customization and branding options',
        'fonts' => 'Typography and font settings',
        'news' => 'Content management and publishing options',
        'security' => 'Security policies and access controls'
    ];
    return $descriptions[$category] ?? 'Configure settings for this category';
}

// Function to organize settings by logical sections
function organizeSettingsBySection($settings, $category) {
    $organized = [];

    switch ($category) {
        case 'general':
            $organized['Site Information'] = [];
            $organized['Regional Settings'] = [];
            $organized['System Configuration'] = [];
            break;
        case 'email':
            $organized['Basic Email Settings'] = [];
            $organized['SMTP Configuration'] = [];
            $organized['Email Templates'] = [];
            break;
        case 'appearance':
            $organized['Branding'] = [];
            $organized['Colors & Themes'] = [];
            $organized['Layout Options'] = [];
            break;
        case 'fonts':
            $organized['Typography'] = [];
            $organized['Font Families'] = [];
            $organized['Font Sizes'] = [];
            break;
        case 'news':
            $organized['Publishing'] = [];
            $organized['Content Options'] = [];
            $organized['SEO Settings'] = [];
            break;
        case 'security':
            $organized['Authentication'] = [];
            $organized['Access Control'] = [];
            $organized['Security Policies'] = [];
            break;
        default:
            $organized['General'] = [];
            break;
    }

    // Distribute settings into sections based on their keys
    foreach ($settings as $key => $setting) {
        $section = categorizeSettingByKey($key, $category);
        if (isset($organized[$section])) {
            $organized[$section][$key] = $setting;
        } else {
            // Fallback to first section
            $first_section = array_key_first($organized);
            $organized[$first_section][$key] = $setting;
        }
    }

    // Remove empty sections
    return array_filter($organized, function($section) {
        return !empty($section);
    });
}

// Function to categorize settings by their key
function categorizeSettingByKey($key, $category) {
    $patterns = [
        'general' => [
            'Site Information' => ['site_name', 'site_description', 'site_url', 'admin_email'],
            'Regional Settings' => ['timezone', 'language', 'date_format', 'time_format'],
            'System Configuration' => ['maintenance_mode', 'debug_mode', 'cache_enabled']
        ],
        'email' => [
            'Basic Email Settings' => ['from_email', 'from_name', 'reply_to'],
            'SMTP Configuration' => ['use_smtp', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_security'],
            'Email Templates' => ['email_template', 'notification_template']
        ],
        'appearance' => [
            'Branding' => ['logo', 'favicon', 'brand_color'],
            'Colors & Themes' => ['primary_color', 'secondary_color', 'theme'],
            'Layout Options' => ['layout', 'sidebar_position', 'header_style']
        ],
        'fonts' => [
            'Typography' => ['body_font', 'heading_font'],
            'Font Families' => ['font_family', 'google_fonts'],
            'Font Sizes' => ['font_size', 'heading_size']
        ],
        'news' => [
            'Publishing' => ['auto_publish', 'moderation'],
            'Content Options' => ['excerpt_length', 'comments_enabled'],
            'SEO Settings' => ['meta_description', 'keywords']
        ],
        'security' => [
            'Authentication' => ['password_policy', 'two_factor'],
            'Access Control' => ['login_attempts', 'session_timeout'],
            'Security Policies' => ['ssl_required', 'csrf_protection']
        ]
    ];

    if (isset($patterns[$category])) {
        foreach ($patterns[$category] as $section => $keys) {
            if (in_array($key, $keys) ||
                array_filter($keys, function($pattern) use ($key) {
                    return strpos($key, $pattern) !== false;
                })) {
                return $section;
            }
        }
    }

    // Default section
    return array_key_first($patterns[$category] ?? ['General' => []]) ?: 'General';
}

// Function to get section icons
function getSectionIcon($section) {
    $icons = [
        'Site Information' => 'fa-info-circle',
        'Regional Settings' => 'fa-globe',
        'System Configuration' => 'fa-cogs',
        'Basic Email Settings' => 'fa-envelope',
        'SMTP Configuration' => 'fa-server',
        'Email Templates' => 'fa-file-alt',
        'Branding' => 'fa-paint-brush',
        'Colors & Themes' => 'fa-palette',
        'Layout Options' => 'fa-layout',
        'Typography' => 'fa-font',
        'Font Families' => 'fa-text-height',
        'Font Sizes' => 'fa-text-width',
        'Publishing' => 'fa-newspaper',
        'Content Options' => 'fa-edit',
        'SEO Settings' => 'fa-search',
        'Authentication' => 'fa-key',
        'Access Control' => 'fa-shield-alt',
        'Security Policies' => 'fa-lock'
    ];
    return $icons[$section] ?? 'fa-cog';
}

// Function to get grid class based on number of items
function getGridClass($count) {
    if ($count <= 2) return 'grid-1-col';
    if ($count <= 4) return 'grid-2-col';
    return 'grid-3-col';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $category = $_POST['category'];
    $category_settings = getSettingsByCategory($conn, $category);

    foreach ($category_settings as $key => $setting) {
        if ($setting['setting_type'] === 'checkbox') {
            $value = isset($_POST[$key]) ? '1' : '0';
        } else {
            $value = isset($_POST[$key]) ? trim($_POST[$key]) : '';
        }

        // Validate required fields
        if ($setting['is_required'] && empty($value)) {
            $error = $setting['display_name'] . " is required";
            break;
        }

        // Validate email fields
        if ($setting['setting_type'] === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format for " . $setting['display_name'];
            break;
        }

        // Update setting
        if (!updateSetting($conn, $category, $key, $value)) {
            $error = "Failed to save " . $setting['display_name'];
            break;
        }
    }

    if (empty($error)) {
        // Log the activity
        require_once 'includes/admin-functions.php';
        log_activity('update', 'Updated ' . $categories[$category] . ' settings', $_SESSION['user_id']);

        $success = "Settings saved successfully";
    }
}

// Get settings for the active category
$category_settings = getSettingsByCategory($conn, $active_category);

// Set page title and metadata
$page_title = "Settings";
$page_icon = 'fas fa-cog';
$page_subtitle = 'Configure system settings and preferences';
?>
<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>
            <p class="admin-content-subtitle"><?php echo $page_subtitle; ?></p>
        </div>
    </div>

    <?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
    </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="settings-container">
            <div class="settings-sidebar">
                <div class="settings-sidebar-header">
                    <h3><i class="fas fa-cog"></i> Settings Categories</h3>
                    <p>Configure your system preferences</p>
                </div>

                <ul class="settings-nav">
                    <?php foreach ($categories as $category_key => $category_name): ?>
                    <li class="settings-nav-item <?php echo $active_category === $category_key ? 'active' : ''; ?>">
                        <a href="settings.php?category=<?php echo $category_key; ?>" class="settings-nav-link">
                            <div class="nav-icon">
                                <i class="fas <?php echo $category_icons[$category_key]; ?>"></i>
                            </div>
                            <div class="nav-content">
                                <span class="nav-title"><?php echo $category_name; ?></span>
                                <span class="nav-desc"><?php echo getCategoryDescription($category_key); ?></span>
                            </div>
                            <div class="nav-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>

                <div class="settings-sidebar-footer">
                    <div class="help-card">
                        <i class="fas fa-question-circle"></i>
                        <h4>Need Help?</h4>
                        <p>Check our documentation for detailed configuration guides.</p>
                        <a href="help.html" class="help-link">View Help <i class="fas fa-external-link-alt"></i></a>
                    </div>
                </div>
            </div>

            <div class="settings-content">
                <form method="post" action="settings.php?category=<?php echo $active_category; ?>" class="settings-form">
                    <input type="hidden" name="category" value="<?php echo $active_category; ?>">

                    <div class="settings-section active" id="<?php echo $active_category; ?>-settings">
                        <div class="settings-header">
                            <div class="settings-title-group">
                                <h3 class="settings-section-title">
                                    <i class="fas <?php echo $category_icons[$active_category]; ?>"></i>
                                    <?php echo $categories[$active_category]; ?> Settings
                                </h3>
                                <p class="settings-section-desc"><?php echo getCategoryDescription($active_category); ?></p>
                            </div>
                        </div>

                        <?php if (empty($category_settings)): ?>
                            <div class="no-settings">
                                <div class="no-settings-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <h4>No Settings Available</h4>
                                <p>No settings are configured for this category yet.</p>
                            </div>
                        <?php else: ?>
                            <!-- Organize settings into logical sections -->
                            <?php
                            $organized_settings = organizeSettingsBySection($category_settings, $active_category);
                            foreach ($organized_settings as $section_name => $section_settings):
                            ?>
                            <div class="settings-subsection">
                                <h4 class="subsection-title">
                                    <i class="fas <?php echo getSectionIcon($section_name); ?>"></i>
                                    <?php echo $section_name; ?>
                                </h4>

                                <div class="form-grid <?php echo getGridClass(count($section_settings)); ?>">
                                    <?php foreach ($section_settings as $key => $setting): ?>
                                        <div class="form-group">
                                            <label for="<?php echo $key; ?>">
                                                <?php echo htmlspecialchars($setting['display_name']); ?>
                                                <?php if ($setting['is_required']): ?>
                                                    <span class="required">*</span>
                                                <?php endif; ?>
                                            </label>

                                            <?php
                                            // Only show descriptions for complex fields that need explanation
                                            $show_description = in_array($setting['setting_type'], ['password', 'select', 'color']) ||
                                                              in_array($key, ['smtp_port', 'smtp_security', 'timezone', 'date_format']);
                                            if (!empty($setting['description']) && $show_description):
                                            ?>
                                                <p class="form-hint"><?php echo htmlspecialchars($setting['description']); ?></p>
                                            <?php endif; ?>

                                            <?php if ($setting['setting_type'] === 'text'): ?>
                                                <input type="text" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'email'): ?>
                                                <input type="email" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'number'): ?>
                                                <input type="number" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                       class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                       <?php echo $setting['is_required'] ? 'required' : ''; ?>>

                                            <?php elseif ($setting['setting_type'] === 'password'): ?>
                                                <div class="password-input-group">
                                                    <input type="password" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                           class="form-control" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <button type="button" class="password-toggle" onclick="togglePassword('<?php echo $key; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                                <textarea id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control" rows="3"
                                                          <?php echo $setting['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                            <?php elseif ($setting['setting_type'] === 'checkbox'): ?>
                                                <div class="checkbox-wrapper">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="<?php echo $key; ?>" value="1"
                                                               <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                                        <span class="checkmark"></span>
                                                        <span class="checkbox-text">Enable</span>
                                                    </label>
                                                </div>

                                            <?php elseif ($setting['setting_type'] === 'select'): ?>
                                                <select id="<?php echo $key; ?>" name="<?php echo $key; ?>" class="form-control"
                                                        <?php echo $setting['is_required'] ? 'required' : ''; ?>>
                                                    <?php
                                                    $options = json_decode($setting['options'], true);
                                                    if ($options):
                                                        foreach ($options as $value => $label): ?>
                                                            <option value="<?php echo htmlspecialchars($value); ?>"
                                                                    <?php echo $setting['setting_value'] === $value ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($label); ?>
                                                            </option>
                                                        <?php endforeach;
                                                    endif; ?>
                                                </select>

                                            <?php elseif ($setting['setting_type'] === 'color'): ?>
                                                <div class="color-input-group">
                                                    <input type="color" id="<?php echo $key; ?>" name="<?php echo $key; ?>"
                                                           class="color-picker" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                                    <input type="text" class="color-text form-control"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                           onchange="document.getElementById('<?php echo $key; ?>').value = this.value">
                                                </div>

                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="form-actions">
                            <button type="submit" name="save_settings" class="admin-btn admin-btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                            <button type="button" class="admin-btn admin-btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> Reset Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Password toggle functionality
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('i');

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Reset form functionality
    function resetForm() {
        if (confirm('Are you sure you want to reset all changes? This will reload the page and lose any unsaved changes.')) {
            window.location.reload();
        }
    }

    // Color picker synchronization
    document.addEventListener('DOMContentLoaded', function() {
        const colorPickers = document.querySelectorAll('.color-picker');
        colorPickers.forEach(picker => {
            const textInput = picker.parentElement.querySelector('.color-text');

            picker.addEventListener('change', function() {
                textInput.value = this.value;
            });

            textInput.addEventListener('change', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    picker.value = this.value;
                }
            });
        });

        // Enhanced checkbox styling
        const checkboxes = document.querySelectorAll('.checkbox-label input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const label = this.closest('.checkbox-label');
                if (this.checked) {
                    label.classList.add('checked');
                } else {
                    label.classList.remove('checked');
                }
            });

            // Initialize state
            if (checkbox.checked) {
                checkbox.closest('.checkbox-label').classList.add('checked');
            }
        });
    });

    // Alert auto-dismissal is handled by the global alert-auto-dismiss.js script
</script>

<?php include 'includes/footer.php'; ?>
