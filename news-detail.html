
<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Cloud Computing Trends for 2023 | Manage Inc.</title>
    <link rel="stylesheet" href="css/style.css?v=20250540">
    <link rel="stylesheet" href="css/carousel.css?v=20250540">
    <link rel="stylesheet" href="css/submenu.css?v=20250540">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Read the latest news and updates from Manage Inc." id="metaDescription">
    <meta name="keywords" content="manage inc, news, updates, articles, cloud computing" id="metaKeywords">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="Cloud Computing Trends for 2023 | Manage Inc." id="ogTitle">
    <meta property="og:description" content="Read the latest news and updates from Manage Inc." id="ogDescription">
    <meta property="og:image" content="images/news/news-banner.jpg" id="ogImage">
    <meta property="og:url" content="" id="ogUrl">
    <meta property="og:type" content="article">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Cloud Computing Trends for 2023 | Manage Inc." id="twitterTitle">
    <meta name="twitter:description" content="Read the latest news and updates from Manage Inc." id="twitterDescription">
    <meta name="twitter:image" content="images/news/news-banner.jpg" id="twitterImage">

    <!-- Canonical Tag -->
    <link rel="canonical" href="" id="canonicalLink">
    <!-- News detail styles are now in the main CSS file -->
    <style>
        .news-detail-header h1 {
            font-size: 36px !important;
            color: #3c3c45;
            margin-bottom: 20px;
            font-weight: 600;
            line-height: 1.2;
        }

        .news-detail-container {
            padding: 0 40px;
        }

        .news-detail-header {
            margin-top: 20px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .news-detail-content {
            font-size: 16px !important;
            line-height: 1.8 !important;
        }

        .image-wrapper {
            margin: 25px 0;
            text-align: center;
        }

        .responsive-image {
            max-width: 100% !important;
            height: auto !important;
            display: inline-block;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .news-detail-content p {
            margin-bottom: 24px !important;
        }

        .news-detail-content h2 {
            font-size: 28px !important;
            margin-top: 40px !important;
            margin-bottom: 20px !important;
            color: #3c3c45 !important;
            font-weight: 600 !important;
        }

        .news-detail-content h3 {
            font-size: 22px !important;
            margin-top: 30px !important;
            margin-bottom: 15px !important;
        }

        .news-detail-meta {
            margin-bottom: 25px !important;
            font-size: 15px !important;
            color: #666;
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
        }

        /* Remove any ::before pseudo-elements */
        .news-detail-meta::before,
        .news-date::before,
        .news-category::before {
            display: none !important;
            content: none !important;
        }

        .news-date, .news-category {
            display: inline-flex;
            align-items: center;
            background-color: #f9f9f9;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }

        .news-date i {
            margin-right: 6px;
            color: #f1ca2f;
        }

        .news-category {
            color: #333;
            background-color: #f1ca2f;
            font-weight: 600;
        }

        .news-detail-image {
            margin-top: 5px !important;
            margin-bottom: 40px !important;
        }

        .news-detail-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .back-to-news {
            margin: 0 !important;
            display: inline-block !important;
        }

        /* Ensure mobile menu toggle is visible */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block !important;
            }

            .main-nav {
                display: none !important;
            }

            .main-nav.active {
                display: block !important;
            }

            .logo {
                text-align: left !important;
            }

            .main-header .container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .news-detail-container {
                padding: 20px !important;
            }

            .news-detail-header {
                margin-top: 15px !important;
                margin-bottom: 10px !important;
                padding-bottom: 10px !important;
            }

            .news-detail-header h1 {
                font-size: 26px !important;
                margin-bottom: 15px !important;
            }

            .news-detail-meta {
                margin-bottom: 20px !important;
                font-size: 14px !important;
                flex-wrap: wrap;
                gap: 10px;
            }

            .news-date, .news-category {
                padding: 5px 10px !important;
                border-radius: 3px !important;
            }

            .news-detail-image {
                margin-top: 0 !important;
                margin-bottom: 25px !important;
            }

            .news-detail-image img {
                height: auto !important;
                border-radius: 0 !important;
            }

            .news-detail-content {
                font-size: 16px !important;
            }

            .image-wrapper {
                margin: 20px 0;
                overflow: hidden;
            }

            .responsive-image {
                box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
                border-radius: 2px;
            }

            .news-detail-content p {
                margin-bottom: 20px !important;
            }

            .news-detail-content h2 {
                font-size: 24px !important;
                margin-top: 30px !important;
                margin-bottom: 15px !important;
            }

            .news-detail-content h3 {
                font-size: 20px !important;
                margin-top: 25px !important;
                margin-bottom: 12px !important;
            }

            .news-detail-footer {
                margin-top: 30px;
                padding-top: 15px;
            }

            .back-to-news {
                margin: 0 !important;
                display: block !important;
                text-align: center !important;
                width: 100% !important;
            }
        }

        @media (max-width: 576px) {
            .news-detail-container {
                padding: 15px !important;
            }

            .news-detail-header {
                margin-top: 10px !important;
                margin-bottom: 8px !important;
                padding-bottom: 8px !important;
            }

            .news-detail-header h1 {
                font-size: 22px !important;
            }

            .news-detail-meta {
                margin-bottom: 15px !important;
                font-size: 12px !important;
                gap: 8px;
            }

            .news-date, .news-category {
                padding: 4px 8px !important;
                border-radius: 3px !important;
            }

            .news-date i {
                margin-right: 4px !important;
            }

            .news-detail-content h2 {
                font-size: 22px !important;
            }

            .news-detail-content h3 {
                font-size: 18px !important;
            }

            .news-detail-content {
                font-size: 15px !important;
            }

            .image-wrapper {
                margin: 15px 0;
            }

            .responsive-image {
                box-shadow: none;
                border: 1px solid #eee;
            }

            .news-detail-footer {
                margin-top: 25px;
                padding-top: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="Manage Incorporated - Established in 1995">
                </a>
            </div>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html">HOME</a></li>
                    <li><a href="cloud.html">CLOUD</a></li>
                    <li><a href="managed-services.html">MANAGED SERVICES</a></li>
                    <li><a href="infrastructure.html">INFRASTRUCTURE</a></li>
                    <li><a href="services.html">SERVICES</a></li>
                    <li class="active"><a href="news.html">NEWS</a></li>
                    <li><a href="contact-us.html">CONTACT US</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="news-page-banner" style="margin-top: 95px;">
            <div class="container">
                <h1>News</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <div id="loadingMessage" style="text-align: center; padding: 50px 0;">
                    <h2>Loading article...</h2>
                    <p>Please wait while we fetch the article content.</p>
                </div>

                <div id="errorMessage" style="color: #e74c3c; background-color: rgba(231, 76, 60, 0.1); border: 1px solid #e74c3c; padding: 15px; border-radius: 4px; margin-bottom: 20px; display: none;">
                    <p id="errorText">Error loading article</p>
                </div>

                <div id="newsDetailContainer" class="news-detail-container" style="display: none;">
                    <div class="news-detail-header">
                        <h1 id="newsTitle">Article Title</h1>
                        <div class="news-detail-meta">
                            <span class="news-date"><i class="fas fa-calendar-alt"></i> Published on <span id="newsDate">Date</span></span>
                            <span class="news-category"><span id="newsCategory">Category</span></span>
                        </div>
                    </div>
                    <div class="news-detail-image">
                        <img id="newsImage" src="images/news/news-banner.jpg" alt="News Image">
                    </div>
                    <div id="newsContent" class="news-detail-content">
                        <!-- Content will be loaded here -->
                    </div>
                    <div class="news-detail-footer">
                        <a href="news.html" class="back-to-news"><span class="back-arrow"></span> Back to News</a>
                    </div>
                </div>

                <!-- Generic error fallback content -->
                <div id="fallbackContent" class="news-detail-container" style="display: none;">
                    <div class="news-detail-header">
                        <h1>Article Not Found</h1>
                        <div class="news-detail-meta">
                            <span class="news-category">Error</span>
                        </div>
                    </div>
                    <div class="news-detail-image">
                        <img src="images/news/news-banner.jpg" alt="Article Not Found">
                    </div>
                    <div class="news-detail-content">
                        <p>We couldn't load the requested article. This could be due to:</p>
                        <ul>
                            <li>The article may have been removed or relocated</li>
                            <li>There might be a temporary server issue</li>
                            <li>The URL might be incorrect</li>
                        </ul>
                        <p>Please try again later or browse our other news articles.</p>
                    </div>
                    <div class="news-detail-footer">
                        <a href="news.html" class="back-to-news"><span class="back-arrow"></span> Back to News</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <!-- Footer Top Section -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-top-content">
                    <div class="footer-info">
                        <h2>BUILD A FUTURE-READY IT INFRASTRUCTURE</h2>
                        <p>Leverage cutting-edge technology to optimize performance, security, and scalability.</p>
                    </div>
                    <div class="footer-form">
                        <form method="post" action="process-contact.php" id="footer-contact-form">
                            <input type="hidden" name="source" value="Footer Form">
                            <div class="form-row">
                                <input type="text" name="name" placeholder="Name" required>
                                <input type="email" name="email" placeholder="Email Address" required>
                            </div>
                            <textarea name="message" placeholder="Message" required></textarea>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section -->
        <div class="footer-middle">
            <div class="container">
                <div class="footer-middle-content">
                    <div class="footer-logo">
                        <img src="images/footer/manage-inc-logo.png" alt="Manage Incorporated">
                    </div>
                    <div class="footer-services">
                        <h4>SERVICES</h4>
                        <ul>
                            <li><a href="cloud.html">Cloud</a></li>
                            <li><a href="managed-services.html">Managed Services</a></li>
                            <li><a href="infrastructure.html">Infrastructure</a></li>
                            <li><a href="services.html">Services</a></li>
                        </ul>
                    </div>
                    <div class="footer-contact">
                        <h4>Seattle, WA Office</h4>
                        <p><img src="images/footer/phone-icon.svg" alt="Phone"> (*************</p>
                        <p><img src="images/footer/fax-icon.svg" alt="Fax"> (*************</p>
                        <p><img src="images/footer/email-icon.svg" alt="Email"> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><img src="images/footer/location-icon.svg" alt="Location"> 600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <p>© Copyright 2025. Manage Inc. all Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/carousel.js?v=20250540"></script>
    <script src="js/mobile-menu.js?v=20250540"></script>
    <script src="js/submenu-mobile.js?v=20250540"></script>
    <script src="js/footer-form.js?v=20250546"></script>

    <!-- Structured Data for Article -->
    <script type="application/ld+json" id="structuredData">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "Cloud Computing Trends for 2023",
        "description": "Read the latest news and updates from Manage Inc.",
        "image": "images/news/news-banner.jpg",
        "datePublished": "2023-01-01",
        "dateModified": "2023-01-01",
        "author": {
            "@type": "Organization",
            "name": "Manage Inc"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Manage Inc",
            "logo": {
                "@type": "ImageObject",
                "url": "images/logo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": ""
        }
    }
    </script>
    <script>
        // Version 1.0.1 - Updated to fix caching issues
        // Ensure mobile menu is working
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.toggle('active');
                    document.getElementById('mainNav').classList.toggle('active');
                });
            }

            // Get the slug from URL - support both formats
            let slug;

            // First try to get it from query parameters (old format)
            const urlParams = new URLSearchParams(window.location.search);
            slug = urlParams.get('slug');

            // If not found, try to get it from the path (new format)
            if (!slug) {
                const path = window.location.pathname;
                const filename = path.split('/').pop(); // Get the last part of the path

                // Check if the filename has a .html extension
                if (filename && filename.endsWith('.html')) {
                    // Remove the .html extension to get the slug
                    slug = filename.replace('.html', '');
                }
            }

            // If still no slug is provided, redirect to news page
            if (!slug) {
                window.location.href = 'news.html';
                return;
            }

            // Fetch the news article data
            fetchNewsArticle(slug);
        });

        // Function to fetch news article data
        function fetchNewsArticle(slug) {
            // Add timestamp to prevent caching
            const timestamp = new Date().getTime();

            // Update canonical link
            updateCanonicalLink(slug);

            // Use news-detail.php as the data source
            const paths = [
                `news-detail.php?slug=${encodeURIComponent(slug)}&format=json&t=${timestamp}`
            ];

            // Use the first path for initial attempt
            const path = paths[0];

            // Log debugging information
            console.log('Current URL:', window.location.href);
            console.log('Current path:', window.location.pathname);
            console.log('Trying paths:', paths);

            // Fetch the article data
            fetch(path)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading message
                    document.getElementById('loadingMessage').style.display = 'none';

                    // No test response handling - we'll only use database data

                    // Check for errors
                    if (data.error) {
                        document.getElementById('errorText').textContent = 'Error: ' + data.error;
                        document.getElementById('errorMessage').style.display = 'block';
                        return;
                    }

                    // Check if article exists
                    if (!data.item) {
                        document.getElementById('errorText').textContent = 'Article not found';
                        document.getElementById('errorMessage').style.display = 'block';
                        return;
                    }

                    // Update page title
                    document.title = data.item.title + ' | Manage Inc.';

                    // Update article content
                    document.getElementById('newsTitle').textContent = data.item.title;
                    document.getElementById('newsDate').textContent = data.item.formatted_date;
                    document.getElementById('newsCategory').textContent = data.item.category_name;

                    // Update SEO tags
                    updateSEOTags(data.item);

                    // Set image
                    const imageElement = document.getElementById('newsImage');
                    imageElement.src = 'images/news/' + data.item.image;
                    imageElement.alt = data.item.title;
                    imageElement.onerror = function() {
                        this.src = 'images/news/news-banner.jpg';
                    };

                    // Set content
                    document.getElementById('newsContent').innerHTML = data.item.content;

                    // Add responsive image classes for mobile
                    const contentImages = document.querySelectorAll('#newsContent img');
                    contentImages.forEach(img => {
                        img.classList.add('responsive-image');
                        // Wrap images in a div for better mobile display
                        const wrapper = document.createElement('div');
                        wrapper.className = 'image-wrapper';
                        img.parentNode.insertBefore(wrapper, img);
                        wrapper.appendChild(img);
                    });

                    // Show the article container
                    document.getElementById('newsDetailContainer').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error fetching article:', error);

                    // Display error message
                    document.getElementById('loadingMessage').style.display = 'none';
                    document.getElementById('errorText').textContent = 'Error loading article: Could not fetch data';
                    document.getElementById('errorMessage').style.display = 'block';

                    // Show fallback content if available
                    if (document.getElementById('fallbackContent')) {
                        document.getElementById('fallbackContent').style.display = 'block';
                    }

                    return {
                        item: null,
                        error: 'Failed to fetch article'
                    };
                });
        }

        // Function to update canonical link
        function updateCanonicalLink(slug) {
            const protocol = window.location.protocol;
            const host = window.location.host;
            const baseUrl = `${protocol}//${host}`;
            const canonicalUrl = `${baseUrl}/${slug}.html`;

            // Update canonical link
            const canonicalLink = document.getElementById('canonicalLink');
            if (canonicalLink) {
                canonicalLink.href = canonicalUrl;
            }

            // Update og:url
            const ogUrl = document.getElementById('ogUrl');
            if (ogUrl) {
                ogUrl.content = canonicalUrl;
            }

            // Update structured data
            const structuredData = document.getElementById('structuredData');
            if (structuredData) {
                const data = JSON.parse(structuredData.textContent);
                if (data.mainEntityOfPage && data.mainEntityOfPage['@id']) {
                    data.mainEntityOfPage['@id'] = canonicalUrl;
                    structuredData.textContent = JSON.stringify(data, null, 4);
                }
            }
        }

        // Function to update SEO tags
        function updateSEOTags(article) {
            if (!article) return;

            // Get base URL
            const protocol = window.location.protocol;
            const host = window.location.host;
            const baseUrl = `${protocol}//${host}`;

            // Create a short description from the content
            let description = article.content;
            // Remove HTML tags
            description = description.replace(/<[^>]*>/g, ' ');
            // Trim whitespace
            description = description.replace(/\s+/g, ' ').trim();
            // Limit to 160 characters
            description = description.substring(0, 157) + '...';

            // Update meta description
            const metaDescription = document.getElementById('metaDescription');
            if (metaDescription) {
                metaDescription.content = description;
            }

            // Update meta keywords
            const metaKeywords = document.getElementById('metaKeywords');
            if (metaKeywords) {
                metaKeywords.content = `manage inc, news, ${article.category_name.toLowerCase()}, ${article.title.toLowerCase()}`;
            }

            // Update Open Graph tags
            const ogTitle = document.getElementById('ogTitle');
            if (ogTitle) {
                ogTitle.content = article.title + ' | Manage Inc.';
            }

            const ogDescription = document.getElementById('ogDescription');
            if (ogDescription) {
                ogDescription.content = description;
            }

            const ogImage = document.getElementById('ogImage');
            if (ogImage) {
                ogImage.content = `${baseUrl}/images/news/${article.image}`;
            }

            // Update Twitter Card tags
            const twitterTitle = document.getElementById('twitterTitle');
            if (twitterTitle) {
                twitterTitle.content = article.title + ' | Manage Inc.';
            }

            const twitterDescription = document.getElementById('twitterDescription');
            if (twitterDescription) {
                twitterDescription.content = description;
            }

            const twitterImage = document.getElementById('twitterImage');
            if (twitterImage) {
                twitterImage.content = `${baseUrl}/images/news/${article.image}`;
            }

            // Update structured data
            const structuredData = document.getElementById('structuredData');
            if (structuredData) {
                const data = JSON.parse(structuredData.textContent);

                data.headline = article.title;
                data.description = description;
                data.image = `${baseUrl}/images/news/${article.image}`;

                if (article.created_at) {
                    data.datePublished = article.created_at.split(' ')[0]; // Get just the date part
                }

                if (article.updated_at) {
                    data.dateModified = article.updated_at.split(' ')[0]; // Get just the date part
                }

                structuredData.textContent = JSON.stringify(data, null, 4);
            }
        }
    </script>
</body>
</html>
