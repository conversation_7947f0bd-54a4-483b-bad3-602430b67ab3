/**
 * HTML Editor
 *
 * Main JavaScript file for the HTML editor
 */

// Global variables
let currentFilePath = '';
let editor = null;
let isModified = false;
let isAdmin = false;
let currentUserId = 0;
let currentUsername = '';
let currentMode = 'code'; // Track current editor mode
let visualEditor = null; // Quill editor instance

// Initialize the editor
document.addEventListener('DOMContentLoaded', function() {
    // Wait for admin header to be loaded before initializing editor
    function initializeEditor() {
        // Set global variables
        currentFilePath = document.querySelector('input[name="file_path"]')?.value || '';
        isAdmin = document.body.dataset.isAdmin === 'true';
        currentUserId = parseInt(document.body.dataset.userId || '0');
        currentUsername = document.body.dataset.username || '';

        // Detect site base URL
        detectSiteBaseUrl();

        // Initialize file selector
        initFileSelector();

    // Initialize editor if a file is selected
    if (currentFilePath) {
        initEditor();

        // Initialize toolbar
        initToolbar();

        // Check file lock
        checkLock(currentFilePath);

        // We'll acquire lock on first edit instead of immediately
        // Set flag to track if lock has been acquired
        window.lockAcquired = false;

        // Check if the file is already locked by the current user
        checkLock(currentFilePath).then(lockData => {
            if (lockData && lockData.locked_by_current_user) {
                window.lockAcquired = true;

                // Show the unlock button
                const unlockButton = document.getElementById('unlockFileBtn');
                if (unlockButton) {
                    unlockButton.style.display = 'inline-block';
                }
            }
        });

        // Extend lock every 4 minutes (only if lock has been acquired)
        setInterval(function() {
            if (currentFilePath && window.lockAcquired) {
                extendLock(currentFilePath);
            }
        }, 240000); // 4 minutes

        // Release lock when leaving the page
        window.addEventListener('beforeunload', function(e) {
            if (isModified) {
                // Show confirmation dialog if file is modified
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            }

            // Release lock
            if (currentFilePath) {
                releaseLock(currentFilePath);
            }
        });

        // Initialize version history
        initVersionHistory();

        // Load version history
        loadVersionHistory();

        // Add window resize event to adjust editor height
        window.addEventListener('resize', function() {
            if (editor) {
                setTimeout(function() {
                    editor.refresh();
                    adjustEditorHeight();
                }, 100);
            }
        });
    }

    // Initialize save button
    const saveButton = document.querySelector('button[name="save_file"]');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            saveFile();
        });
    }

    // Initialize unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.addEventListener('click', function(e) {
                // We're using a direct link now, so we just need to confirm
                if (!confirm('Are you sure you want to unlock this file? This will allow other users to edit it.')) {
                    e.preventDefault(); // Prevent the default link action if user cancels
                }
            });
        }

        // Initialize force unlock button
        const forceUnlockButton = document.getElementById('forceUnlockBtn');
        if (forceUnlockButton) {
            forceUnlockButton.addEventListener('click', function() {
                if (confirm('Are you sure you want to force unlock this file? This will override the current user\'s lock.')) {
                    forceReleaseLock(currentFilePath).then(function(success) {
                        if (success) {
                            alert('File force unlocked successfully.');
                            window.location.reload();
                        }
                    });
                }
            });
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);

        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

// Initialize file selector
function initFileSelector() {
    const fileSelector = document.getElementById('fileSelector');
    if (fileSelector) {
        fileSelector.addEventListener('change', function() {
            if (this.value) {
                // Check if there are unsaved changes
                if (isModified) {
                    if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
                        // Release lock on current file
                        if (currentFilePath) {
                            releaseLock(currentFilePath);
                        }

                        // Navigate to selected file
                        window.location.href = this.value;
                    } else {
                        // Reset selector to current file
                        this.value = currentFilePath ? 'html_editor.php?file=' + encodeURIComponent(currentFilePath) : '';
                    }
                } else {
                    // Release lock on current file
                    if (currentFilePath) {
                        releaseLock(currentFilePath);
                    }

                    // Navigate to selected file
                    window.location.href = this.value;
                }
            }
        });
    }
}

// Initialize editor
function initEditor() {
    const textarea = document.getElementById('editor-textarea');
    if (textarea) {
        // Debug: Log the textarea content
        console.log('Textarea content:', textarea.value);

        // Initialize CodeMirror directly
        editor = CodeMirror.fromTextArea(textarea, {
            lineNumbers: true,
            matchBrackets: true,
            autoCloseBrackets: true,
            autoCloseTags: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
            styleActiveLine: true,
            theme: 'material',
            mode: 'htmlmixed',
            indentUnit: 4,
            tabSize: 4,
            indentWithTabs: false,
            extraKeys: {
                "Ctrl-Space": "autocomplete",
                "Tab": function(cm) {
                    if (cm.somethingSelected()) {
                        cm.indentSelection("add");
                    } else {
                        cm.replaceSelection("    ", "end", "+input");
                    }
                }
            }
        });

        // Set height to auto-adjust to content with a large value to ensure it shows all content
        editor.setSize(null, "auto");

        // Force the editor to take full height
        const editorElement = editor.getWrapperElement();
        if (editorElement) {
            editorElement.style.height = 'auto';
            editorElement.style.minHeight = '600px';

            // Apply direct styles to ensure proper height
            editorElement.style.display = 'flex';
            editorElement.style.flexDirection = 'column';
            editorElement.style.flex = '1';
            editorElement.style.maxHeight = 'none';

            // Also apply to the scroll element
            const scrollElement = editorElement.querySelector('.CodeMirror-scroll');
            if (scrollElement) {
                scrollElement.style.height = 'auto';
                scrollElement.style.minHeight = '600px';
                scrollElement.style.maxHeight = 'none';
            }
        }

        // Ensure editor expands with content
        editor.on("change", function() {
            // Update size on change
            editor.setSize(null, "auto");

            // Use our comprehensive height adjustment function
            setTimeout(function() {
                adjustEditorHeight();
            }, 100);
        });

        // Debug: Log the editor content
        console.log('Editor content:', editor.getValue());

        // Force refresh the editor and adjust height
        setTimeout(function() {
            editor.refresh();
            console.log('Editor refreshed');

            // Calculate and set proper height based on content
            adjustEditorHeight();
        }, 100);

        // Add another delayed refresh to ensure proper rendering after page is fully loaded
        setTimeout(function() {
            editor.refresh();
            adjustEditorHeight();
        }, 500);

        // Set read-only sections for header and footer
        setReadOnlySections(editor);

        // Initialize visual editor
        initVisualEditor();

        // Track changes
        editor.on('change', function() {
            // If this is the first edit, acquire lock
            if (!isModified && !window.lockAcquired && currentFilePath) {
                // Set lock acquired flag immediately for UI feedback
                window.lockAcquired = true;

                // Update lock status in UI immediately
                updateLockStatus(true, null);

                // Show the unlock button immediately
                const unlockButton = document.getElementById('unlockFileBtn');
                if (unlockButton) {
                    unlockButton.style.display = 'inline-block';
                }

                // Actually acquire the lock on the server
                acquireLock(currentFilePath).then(success => {
                    if (!success) {
                        // If lock acquisition failed, revert the UI
                        window.lockAcquired = false;
                        console.error('Failed to acquire lock on server');
                    } else {
                        console.log('Lock acquired on first edit');
                    }
                });
            }

            isModified = true;

            // Update textarea value
            editor.save();
        });
    }
}

// Initialize visual editor
function initVisualEditor() {
    const visualEditorElement = document.getElementById('visual-editor');
    if (visualEditorElement && typeof Quill !== 'undefined') {
        // Configure Quill toolbar
        const toolbarOptions = [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['link', 'image', 'video'],
            ['clean']
        ];

        // Initialize Quill
        visualEditor = new Quill(visualEditorElement, {
            theme: 'snow',
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'Start editing your content...'
        });

        // Track changes in visual editor
        visualEditor.on('text-change', function() {
            if (!isModified) {
                // If this is the first edit, acquire lock
                if (!window.lockAcquired && currentFilePath) {
                    window.lockAcquired = true;
                    updateLockStatus(true, null);

                    const unlockButton = document.getElementById('unlockFileBtn');
                    if (unlockButton) {
                        unlockButton.style.display = 'inline-block';
                    }

                    acquireLock(currentFilePath).then(success => {
                        if (!success) {
                            window.lockAcquired = false;
                            console.error('Failed to acquire lock on server');
                        }
                    });
                }
            }
            isModified = true;
        });
    }
}

// Make header and footer sections read-only
function setReadOnlySections(editor) {
    const content = editor.getValue();
    const headerMatch = content.match(/<header[^>]*>[\s\S]*?<\/header>/i);
    const footerMatch = content.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);

    // Clear any existing read-only marks
    editor.getAllMarks().forEach(mark => mark.clear());

    // If header found, make it read-only
    if (headerMatch) {
        const headerStart = getLineNumber(content, headerMatch.index);
        const headerEnd = getLineNumber(content, headerMatch.index + headerMatch[0].length);

        // Mark header as read-only
        editor.markText(
            {line: headerStart, ch: 0},
            {line: headerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }

    // If footer found, make it read-only
    if (footerMatch) {
        const footerStart = getLineNumber(content, footerMatch.index);
        const footerEnd = getLineNumber(content, footerMatch.index + footerMatch[0].length);

        // Mark footer as read-only
        editor.markText(
            {line: footerStart, ch: 0},
            {line: footerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }
}

// Get line number from character index
function getLineNumber(text, index) {
    const lines = text.substr(0, index).split('\n');
    return lines.length - 1;
}

// Save file
function saveFile() {
    // Show loading overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // If we're in visual mode, update the code editor first
    const visualEditorContainer = document.getElementById('visual-editor-container');
    if (visualEditorContainer && visualEditorContainer.style.display === 'block') {
        updateCodeFromVisual();
    }

    // Make sure the editor content is saved to the textarea
    if (editor) {
        editor.save();
    }

    // Get form data
    const form = document.getElementById('fileEditForm');
    const formData = new FormData(form);

    // Add the action parameter for AJAX handling
    formData.append('action', 'save_file');

    // Send request
    fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        if (data.success) {
            // Show success message
            alert('File saved successfully.');

            // Reset modified flag
            isModified = false;

            // Release the lock automatically after successful save
            if (window.lockAcquired && currentFilePath) {
                console.log('Automatically releasing lock after successful save');
                releaseLock(currentFilePath).then(function(success) {
                    if (success) {
                        window.lockAcquired = false;
                        updateLockStatus(false, null);
                        console.log('Lock released automatically after save');
                    }
                });
            }

            // Reload version history
            loadVersionHistory();
        } else {
            // Show error message
            alert('Error: ' + (data.message || 'Failed to save file.'));
        }
    })
    .catch(error => {
        console.error('Error saving file:', error);

        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        // Show detailed error message
        let errorMessage = 'Error saving file: ' + error.message;

        // Add suggestion for JSON parsing errors
        if (error.message.includes('JSON')) {
            errorMessage += '\n\nThis may be due to a server configuration issue. The file might have been saved successfully despite this error. Please check if your changes were applied.';
        }

        alert(errorMessage);

        // If it was a JSON parsing error, try to reload the page after a delay
        // This can help if the file was actually saved but we just couldn't parse the response
        if (error.message.includes('JSON')) {
            setTimeout(() => {
                if (confirm('Would you like to reload the page to see if your changes were saved?')) {
                    window.location.reload();
                }
            }, 1000);
        }
    });
}

// Load version history
function loadVersionHistory() {
    if (currentFilePath) {
        getAllVersions(currentFilePath)
            .then(versions => {
                updateVersionHistory(versions);
            });
    }
}

// Initialize toolbar
function initToolbar() {
    const viewCodeBtn = document.getElementById('viewCodeBtn');
    const viewVisualBtn = document.getElementById('viewVisualBtn');
    const formatCodeBtn = document.getElementById('formatCodeBtn');
    const findReplaceBtn = document.getElementById('findReplaceBtn');
    const unlockFileBtn = document.getElementById('unlockFileBtn');
    const editorContainer = document.getElementById('editor-container');
    const visualEditorContainer = document.getElementById('visual-editor-container');
    const wysiwygToolbar = document.getElementById('wysiwyg-toolbar');

    // Unlock button is now handled globally in the document ready function

    if (viewCodeBtn && viewVisualBtn && editorContainer && visualEditorContainer) {
        // View code button
        viewCodeBtn.addEventListener('click', function() {
            // Update button states
            viewCodeBtn.classList.add('btn-primary');
            viewCodeBtn.classList.remove('btn-outline-primary');
            viewVisualBtn.classList.add('btn-outline-primary');
            viewVisualBtn.classList.remove('btn-primary');

            // If coming from visual mode, update code first
            if (currentMode === 'visual') {
                updateCodeFromVisual();
                console.log('Switching from visual to code mode');
            }

            // Update current mode
            currentMode = 'code';

            // Update container visibility
            editorContainer.style.display = 'block';
            visualEditorContainer.style.display = 'none';
            wysiwygToolbar.style.display = 'none';

            // Remove visual-mode class from body
            document.body.classList.remove('visual-mode');

            // Show Format and Find buttons in code mode
            const formatCodeBtn = document.getElementById('formatCodeBtn');
            const findReplaceBtn = document.getElementById('findReplaceBtn');
            if (formatCodeBtn) formatCodeBtn.style.display = 'inline-block';
            if (findReplaceBtn) findReplaceBtn.style.display = 'inline-block';

            // Refresh editor to fix any display issues
            if (editor) {
                editor.refresh();

                // Adjust the code editor height
                setTimeout(function() {
                    adjustEditorHeight();
                }, 100);
            }
        });

        // View visual editor button
        viewVisualBtn.addEventListener('click', function() {
            // Update button states
            viewVisualBtn.classList.add('btn-primary');
            viewVisualBtn.classList.remove('btn-outline-primary');
            viewCodeBtn.classList.add('btn-outline-primary');
            viewCodeBtn.classList.remove('btn-primary');

            // Update current mode
            currentMode = 'visual';

            // Update container visibility
            editorContainer.style.display = 'none';
            visualEditorContainer.style.display = 'block';
            wysiwygToolbar.style.display = 'flex';

            // Add visual-mode class to body
            document.body.classList.add('visual-mode');

            // Hide Format and Find buttons in visual mode
            const formatCodeBtn = document.getElementById('formatCodeBtn');
            const findReplaceBtn = document.getElementById('findReplaceBtn');
            if (formatCodeBtn) formatCodeBtn.style.display = 'none';
            if (findReplaceBtn) findReplaceBtn.style.display = 'none';

            // Update visual editor content from code
            updateVisualFromCode();
        });

        // Format code button
        if (formatCodeBtn) {
            formatCodeBtn.addEventListener('click', function() {
                if (editor) {
                    formatCode();
                }
            });
        }

        // Find & Replace button
        if (findReplaceBtn) {
            findReplaceBtn.addEventListener('click', function() {
                if (editor) {
                    editor.execCommand('find');
                }
            });
        }

        // Initialize WYSIWYG toolbar buttons
        initWysiwygToolbar();

        // Initialize scroll fix
        initScrollFix();
    }
}

// Update visual editor content from code
function updateVisualFromCode() {
    if (editor && visualEditor) {
        try {
            // Get the content from the code editor
            const content = editor.getValue();
            console.log("Updating visual editor with content:", content.substring(0, 100) + "...");

            // Extract body content or use full content
            let bodyContent = content;
            const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
            if (bodyMatch) {
                bodyContent = bodyMatch[1];
            } else {
                // Remove html, head, and body tags if present
                bodyContent = bodyContent.replace(/<\/?html[^>]*>/gi, '');
                bodyContent = bodyContent.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
                bodyContent = bodyContent.replace(/<\/?body[^>]*>/gi, '');
            }

            // Set the content in Quill editor
            visualEditor.root.innerHTML = bodyContent;

            console.log("Updated visual editor from code content");
        } catch (error) {
            console.error("Error updating visual editor:", error);
        }
    }
}

// Update code editor content from visual editor
function updateCodeFromVisual() {
    if (visualEditor && editor) {
        try {
            // Get HTML content from Quill editor
            const visualContent = visualEditor.root.innerHTML;

            // Get the original content to preserve structure
            const originalContent = editor.getValue();

            // Try to replace just the body content
            let updatedContent = originalContent;
            const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);

            if (bodyMatch) {
                // Replace body content
                updatedContent = originalContent.replace(bodyMatch[1], visualContent);
            } else {
                // If no body tags, replace the entire content
                updatedContent = visualContent;
            }

            // Update the code editor
            editor.setValue(updatedContent);

            console.log("Updated code editor from visual content");
        } catch (error) {
            console.error("Error updating code from visual:", error);
        }
    }
}
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['link', 'image', 'video'],
            ['clean']
        ];

        // Initialize Quill
        visualEditor = new Quill(visualEditorElement, {
            theme: 'snow',
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'Start editing your content...'
        });

        // Track changes in visual editor
        visualEditor.on('text-change', function() {
            if (!isModified) {
                // If this is the first edit, acquire lock
                if (!window.lockAcquired && currentFilePath) {
                    window.lockAcquired = true;
                    updateLockStatus(true, null);

                    const unlockButton = document.getElementById('unlockFileBtn');
                    if (unlockButton) {
                        unlockButton.style.display = 'inline-block';
                    }

                    acquireLock(currentFilePath).then(success => {
                        if (!success) {
                            window.lockAcquired = false;
                            console.error('Failed to acquire lock on server');
                        }
                    });
                }
            }
            isModified = true;
        });
    }
}

// Make header and footer sections read-only
function setReadOnlySections(editor) {
    const content = editor.getValue();
    const headerMatch = content.match(/<header[^>]*>[\s\S]*?<\/header>/i);
    const footerMatch = content.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);

    // Clear any existing read-only marks
    editor.getAllMarks().forEach(mark => mark.clear());

    // If header found, make it read-only
    if (headerMatch) {
        const headerStart = getLineNumber(content, headerMatch.index);
        const headerEnd = getLineNumber(content, headerMatch.index + headerMatch[0].length);

        // Mark header as read-only
        editor.markText(
            {line: headerStart, ch: 0},
            {line: headerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }

    // If footer found, make it read-only
    if (footerMatch) {
        const footerStart = getLineNumber(content, footerMatch.index);
        const footerEnd = getLineNumber(content, footerMatch.index + footerMatch[0].length);

        // Mark footer as read-only
        editor.markText(
            {line: footerStart, ch: 0},
            {line: footerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }
}

// Get line number from character index
function getLineNumber(text, index) {
    const lines = text.substr(0, index).split('\n');
    return lines.length - 1;
}

// Save file
function saveFile() {
    // Show loading overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // If we're in visual mode, update the code editor first
    const visualEditorContainer = document.getElementById('visual-editor-container');
    if (visualEditorContainer && visualEditorContainer.style.display === 'block') {
        updateCodeFromVisual();
    }

    // Make sure the editor content is saved to the textarea
    if (editor) {
        editor.save();
    }

    // Get form data
    const form = document.getElementById('fileEditForm');
    const formData = new FormData(form);

    // Add the action parameter for AJAX handling
    formData.append('action', 'save_file');

    // Send request
    fetch('ajax/html_editor.php', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.text().then(text => {
            try {
                // Check if the response contains HTML and JSON
                if (text.includes('<!DOCTYPE html>') && text.includes('{"success":')) {
                    // Extract the JSON part from the response
                    const jsonStart = text.indexOf('{');
                    if (jsonStart !== -1) {
                        const jsonPart = text.substring(jsonStart);
                        return JSON.parse(jsonPart);
                    }
                }
                // Regular JSON parsing
                return JSON.parse(text);
            } catch (e) {
                console.error('Error parsing JSON response:', text);
                // Try to extract any JSON-like structure from the response
                const jsonMatch = text.match(/\{.*\}/);
                if (jsonMatch) {
                    try {
                        return JSON.parse(jsonMatch[0]);
                    } catch (innerError) {
                        console.error('Failed to extract JSON from response');
                    }
                }
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        if (data.success) {
            // Show success message
            alert('File saved successfully.');

            // Reset modified flag
            isModified = false;

            // Release the lock automatically after successful save
            if (window.lockAcquired && currentFilePath) {
                console.log('Automatically releasing lock after successful save');
                releaseLock(currentFilePath).then(function(success) {
                    if (success) {
                        window.lockAcquired = false;
                        updateLockStatus(false, null);
                        console.log('Lock released automatically after save');
                    }
                });
            }

            // Reload version history
            loadVersionHistory();
        } else {
            // Show error message
            alert('Error: ' + (data.message || 'Failed to save file.'));
        }
    })
    .catch(error => {
        console.error('Error saving file:', error);

        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        // Show detailed error message
        let errorMessage = 'Error saving file: ' + error.message;

        // Add suggestion for JSON parsing errors
        if (error.message.includes('JSON')) {
            errorMessage += '\n\nThis may be due to a server configuration issue. The file might have been saved successfully despite this error. Please check if your changes were applied.';
        }

        alert(errorMessage);

        // If it was a JSON parsing error, try to reload the page after a delay
        // This can help if the file was actually saved but we just couldn't parse the response
        if (error.message.includes('JSON')) {
            setTimeout(() => {
                if (confirm('Would you like to reload the page to see if your changes were saved?')) {
                    window.location.reload();
                }
            }, 1000);
        }
    });
}

// Load version history
function loadVersionHistory() {
    if (currentFilePath) {
        getAllVersions(currentFilePath)
            .then(versions => {
                updateVersionHistory(versions);
            });
    }
}

// Initialize toolbar
function initToolbar() {
    const viewCodeBtn = document.getElementById('viewCodeBtn');
    const viewVisualBtn = document.getElementById('viewVisualBtn');
    const formatCodeBtn = document.getElementById('formatCodeBtn');
    const findReplaceBtn = document.getElementById('findReplaceBtn');
    const unlockFileBtn = document.getElementById('unlockFileBtn');
    const editorContainer = document.getElementById('editor-container');
    const visualEditorContainer = document.getElementById('visual-editor-container');
    const wysiwygToolbar = document.getElementById('wysiwyg-toolbar');

    // Unlock button is now handled globally in the document ready function

    if (viewCodeBtn && viewVisualBtn && editorContainer && visualEditorContainer) {
        // View code button
        viewCodeBtn.addEventListener('click', function() {
            // Update button states
            viewCodeBtn.classList.add('btn-primary');
            viewCodeBtn.classList.remove('btn-outline-primary');
            viewVisualBtn.classList.add('btn-outline-primary');
            viewVisualBtn.classList.remove('btn-primary');

            // If coming from visual mode, update code first
            if (currentMode === 'visual') {
                updateCodeFromVisual();
                console.log('Switching from visual to code mode');
            }

            // Update current mode
            currentMode = 'code';

            // Update container visibility
            editorContainer.style.display = 'block';
            visualEditorContainer.style.display = 'none';
            wysiwygToolbar.style.display = 'none';

            // Remove visual-mode class from body
            document.body.classList.remove('visual-mode');

            // Show Format and Find buttons in code mode
            const formatCodeBtn = document.getElementById('formatCodeBtn');
            const findReplaceBtn = document.getElementById('findReplaceBtn');
            if (formatCodeBtn) formatCodeBtn.style.display = 'inline-block';
            if (findReplaceBtn) findReplaceBtn.style.display = 'inline-block';

            // Refresh editor to fix any display issues
            if (editor) {
                editor.refresh();

                // Adjust the code editor height
                setTimeout(function() {
                    adjustEditorHeight();
                }, 100);
            }
        });

        // View visual editor button
        viewVisualBtn.addEventListener('click', function() {
            // Update button states
            viewVisualBtn.classList.add('btn-primary');
            viewVisualBtn.classList.remove('btn-outline-primary');
            viewCodeBtn.classList.add('btn-outline-primary');
            viewCodeBtn.classList.remove('btn-primary');

            // Update current mode
            currentMode = 'visual';

            // Update container visibility
            editorContainer.style.display = 'none';
            visualEditorContainer.style.display = 'block';
            wysiwygToolbar.style.display = 'flex';

            // Add visual-mode class to body
            document.body.classList.add('visual-mode');

            // Hide Format and Find buttons in visual mode
            const formatCodeBtn = document.getElementById('formatCodeBtn');
            const findReplaceBtn = document.getElementById('findReplaceBtn');
            if (formatCodeBtn) formatCodeBtn.style.display = 'none';
            if (findReplaceBtn) findReplaceBtn.style.display = 'none';

            // Update visual editor content from code
            updateVisualFromCode();
        });

        // Format code button
        if (formatCodeBtn) {
            formatCodeBtn.addEventListener('click', function() {
                if (editor) {
                    formatCode();
                }
            });
        }

        // Find & Replace button
        if (findReplaceBtn) {
            findReplaceBtn.addEventListener('click', function() {
                if (editor) {
                    editor.execCommand('find');
                }
            });
        }

        // Initialize WYSIWYG toolbar buttons
        initWysiwygToolbar();

        // Initialize scroll fix
        initScrollFix();
    }
}

// Fix for toolbar and footer visibility when scrolling
function initScrollFix() {
    const editorContent = document.getElementById('editor-content-container');
    const editorToolbar = document.querySelector('.editor-toolbar');
    const wysiwygToolbar = document.getElementById('wysiwyg-toolbar');
    const editorFooter = document.querySelector('.editor-footer');

    if (editorContent) {
        // Add scroll event listener to ensure toolbars and footer remain visible
        editorContent.addEventListener('scroll', function() {
            // Ensure toolbars stay visible
            if (editorToolbar) {
                editorToolbar.style.position = 'sticky';
                editorToolbar.style.top = '0';
                editorToolbar.style.zIndex = '100';
            }

            if (wysiwygToolbar) {
                wysiwygToolbar.style.position = 'sticky';
                wysiwygToolbar.style.top = '0';
                wysiwygToolbar.style.zIndex = '100';
            }

            // Ensure footer stays visible
            if (editorFooter) {
                editorFooter.style.position = 'sticky';
                editorFooter.style.bottom = '0';
                editorFooter.style.zIndex = '100';
            }
        });

        // Also add window scroll event listener as a backup
        window.addEventListener('scroll', function() {
            if (editorToolbar) {
                editorToolbar.style.position = 'sticky';
                editorToolbar.style.top = '0';
                editorToolbar.style.zIndex = '100';
            }

            if (wysiwygToolbar && wysiwygToolbar.style.display !== 'none') {
                wysiwygToolbar.style.position = 'sticky';
                wysiwygToolbar.style.top = '0';
                wysiwygToolbar.style.zIndex = '100';
            }

            if (editorFooter) {
                editorFooter.style.position = 'sticky';
                editorFooter.style.bottom = '0';
                editorFooter.style.zIndex = '100';
            }
        });
    }
}



// Format HTML code
function formatCode() {
    if (editor) {
        const content = editor.getValue();
        try {
            // Use HTML formatter
            const formattedContent = html_beautify(content, {
                indent_size: 4,
                indent_char: ' ',
                max_preserve_newlines: 2,
                preserve_newlines: true,
                keep_array_indentation: false,
                break_chained_methods: false,
                indent_scripts: 'normal',
                brace_style: 'collapse',
                space_before_conditional: true,
                unescape_strings: false,
                jslint_happy: false,
                end_with_newline: true,
                wrap_line_length: 0,
                indent_inner_html: true,
                comma_first: false,
                e4x: false,
                indent_empty_lines: false
            });

            // Set formatted content
            editor.setValue(formattedContent);

            // Show success message
            alert('Code formatted successfully!');
        } catch (e) {
            console.error('Error formatting code:', e);
            alert('Error formatting code: ' + e.message);
        }
    }
}

// Initialize WYSIWYG toolbar
function initWysiwygToolbar() {
    const wysiwygButtons = document.querySelectorAll('#wysiwyg-toolbar button[data-command]');
    const visualEditor = document.getElementById('visual-editor');

    if (wysiwygButtons.length && visualEditor) {
        // Add event listeners to all toolbar buttons
        wysiwygButtons.forEach(button => {
            button.addEventListener('click', function() {
                const command = this.dataset.command;

                if (command === 'createLink') {
                    const url = prompt('Enter the link URL:', 'https://');
                    if (url) {
                        document.execCommand(command, false, url);
                    }
                } else if (command === 'insertImage') {
                    const url = prompt('Enter the image URL (relative paths like "images/photo.jpg" or absolute URLs):', 'images/');
                    if (url) {
                        // Insert the image with the provided URL
                        document.execCommand(command, false, url);

                        // Fix the image path immediately after insertion
                        setTimeout(() => {
                            const images = visualEditor.querySelectorAll('img[src="' + url + '"]');
                            images.forEach(img => {
                                // Store the original path as a data attribute
                                img.setAttribute('data-original-src', url);

                                // Convert all image paths to ensure they're correct
                                const absolutePath = getAbsoluteImagePath(url);

                                // Update the image src
                                img.src = absolutePath;

                                console.log('Inserted image path updated:', {
                                    original: url,
                                    absolutePath: absolutePath
                                });
                            });
                        }, 100);
                    }
                } else if (command === 'insertTable') {
                    insertTable();
                } else {
                    document.execCommand(command, false, null);
                }

                // Focus back on the editor
                visualEditor.focus();
            });
        });

        // Make sure the editor is focused when clicked
        visualEditor.addEventListener('click', function() {
            this.focus();
        });

        // Ensure visual editor expands with content
        visualEditor.addEventListener('input', function() {
            // If this is the first edit, acquire lock
            if (!isModified && !window.lockAcquired && currentFilePath) {
                // Set lock acquired flag immediately for UI feedback
                window.lockAcquired = true;

                // Update lock status in UI immediately
                updateLockStatus(true, null);

                // Show the unlock button immediately
                const unlockButton = document.getElementById('unlockFileBtn');
                if (unlockButton) {
                    unlockButton.style.display = 'inline-block';
                }

                // Actually acquire the lock on the server
                acquireLock(currentFilePath).then(success => {
                    if (!success) {
                        // If lock acquisition failed, revert the UI
                        window.lockAcquired = false;
                        console.error('Failed to acquire lock on server');
                    } else {
                        console.log('Lock acquired on first edit in visual editor');
                    }
                });
            }

            // Mark as modified
            isModified = true;

            // Adjust container height based on content
            const visualEditorContainer = document.getElementById('visual-editor-container');
            if (visualEditorContainer) {
                visualEditorContainer.style.height = 'auto';
            }
        });
    }
}

// Insert table into WYSIWYG editor
function insertTable() {
    const rows = prompt('Enter number of rows:', '3');
    const cols = prompt('Enter number of columns:', '3');

    if (rows && cols) {
        let tableHTML = '<table border="1" style="width:100%">';

        // Add header row
        tableHTML += '<tr>';
        for (let i = 0; i < cols; i++) {
            tableHTML += '<th>Header ' + (i + 1) + '</th>';
        }
        tableHTML += '</tr>';

        // Add data rows
        for (let i = 0; i < rows - 1; i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < cols; j++) {
                tableHTML += '<td>Cell ' + (i + 1) + ',' + (j + 1) + '</td>';
            }
            tableHTML += '</tr>';
        }

        tableHTML += '</table>';

        document.execCommand('insertHTML', false, tableHTML);
    }
}

// Update visual editor content from code
function updateVisualFromCode() {
    if (editor && visualEditor) {
        try {
            // Get the content from the code editor
            const content = editor.getValue();
            console.log("Updating visual editor with content:", content.substring(0, 100) + "...");

            // Extract body content or use full content
            let bodyContent = content;
            const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
            if (bodyMatch) {
                bodyContent = bodyMatch[1];
            } else {
                // Remove html, head, and body tags if present
                bodyContent = bodyContent.replace(/<\/?html[^>]*>/gi, '');
                bodyContent = bodyContent.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
                bodyContent = bodyContent.replace(/<\/?body[^>]*>/gi, '');
            }

            // Set the content in Quill editor
            visualEditor.root.innerHTML = bodyContent;

            console.log("Updated visual editor from code content");
        } catch (error) {
            console.error("Error updating visual editor:", error);
        }
    }
}

// Update code editor content from visual editor
function updateCodeFromVisual() {
    if (visualEditor && editor) {
        try {
            // Get HTML content from Quill editor
            const visualContent = visualEditor.root.innerHTML;

            // Get the original content to preserve structure
            const originalContent = editor.getValue();

            // Try to replace just the body content
            let updatedContent = originalContent;
            const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);

            if (bodyMatch) {
                // Replace body content
                updatedContent = originalContent.replace(bodyMatch[1], visualContent);
            } else {
                // If no body tags, replace the entire content
                updatedContent = visualContent;
            }

            // Update the code editor
            editor.setValue(updatedContent);

            console.log("Updated code editor from visual content");
        } catch (error) {
            console.error("Error updating code from visual:", error);
        }
    }
        const headElement = tempDiv.querySelector('head');
        if (headElement) {
            headContent = headElement.innerHTML;
            console.log("Found head content from DOM parsing");
        } else {
            // Fallback to regex if DOM parsing fails
            const headMatch = content.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
            if (headMatch) {
                headContent = headMatch[1];
                console.log("Found head content from regex");
            }
        }

        // Extract body content
        let bodyContent = content;
        const bodyElement = tempDiv.querySelector('body');
        if (bodyElement) {
            bodyContent = bodyElement.innerHTML;
            console.log("Found body content from DOM parsing");
        } else {
            // Fallback to regex if DOM parsing fails
            const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
            if (bodyMatch) {
                bodyContent = bodyMatch[1];
                console.log("Found body content from regex");
            } else {
                // If no body tags, try to extract content between header and footer
                let headerContent = '';
                let footerContent = '';

                // Extract header if present
                const headerMatch = content.match(/<header[^>]*>[\s\S]*?<\/header>/i);
                if (headerMatch) {
                    headerContent = headerMatch[0];
                    bodyContent = bodyContent.replace(headerMatch[0], '');
                    console.log("Found header content");
                }

                // Extract footer if present
                const footerMatch = content.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);
                if (footerMatch) {
                    footerContent = footerMatch[0];
                    bodyContent = bodyContent.replace(footerMatch[0], '');
                    console.log("Found footer content");
                }
            }
        }

        // Extract CSS links from the entire document
        const cssLinks = [];

        // First try to extract CSS links using DOM parsing
        const linkElements = tempDiv.querySelectorAll('link[rel="stylesheet"]');
        if (linkElements.length > 0) {
            console.log(`Found ${linkElements.length} CSS links from DOM parsing`);
            linkElements.forEach(link => {
                const href = link.getAttribute('href');
                if (href) {
                    // Skip external CSS (already absolute)
                    if (href.startsWith('http') || href.startsWith('//')) {
                        cssLinks.push(href);
                    } else {
                        // For relative paths, ensure they use the base URL
                        // Remove any leading slash
                        const cleanPath = href.replace(/^\//, '');

                        // Create the absolute path using the site base URL
                        let absoluteCssPath;
                        if (siteBaseUrl) {
                            // Make sure the base URL ends with a slash
                            const baseUrl = siteBaseUrl.endsWith('/') ? siteBaseUrl : siteBaseUrl + '/';
                            absoluteCssPath = baseUrl + cleanPath;
                        } else {
                            // Fallback to origin if no base URL is available
                            absoluteCssPath = window.location.origin + '/' + cleanPath;
                        }

                        console.log('CSS path conversion:', {
                            original: href,
                            absolute: absoluteCssPath
                        });

                        cssLinks.push(absoluteCssPath);
                    }
                }
            });
        } else {
            // Fallback to regex if DOM parsing fails
            const cssRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi;
            let cssMatch;
            let contentCopy = content; // Use a copy to avoid modifying the original
            while ((cssMatch = cssRegex.exec(contentCopy)) !== null) {
                // Get the CSS path
                const cssPath = cssMatch[1];

                // Skip external CSS (already absolute)
                if (cssPath.startsWith('http') || cssPath.startsWith('//')) {
                    cssLinks.push(cssPath);
                    continue;
                }

                // For relative paths, ensure they use the base URL
                // Remove any leading slash
                const cleanPath = cssPath.replace(/^\//, '');

                // Create the absolute path using the site base URL
                let absoluteCssPath;
                if (siteBaseUrl) {
                    // Make sure the base URL ends with a slash
                    const baseUrl = siteBaseUrl.endsWith('/') ? siteBaseUrl : siteBaseUrl + '/';
                    absoluteCssPath = baseUrl + cleanPath;
                } else {
                    // Fallback to origin if no base URL is available
                    absoluteCssPath = window.location.origin + '/' + cleanPath;
                }

                console.log('CSS path conversion (regex):', {
                    original: cssPath,
                    absolute: absoluteCssPath
                });

                cssLinks.push(absoluteCssPath);
            }
        }

        // Extract inline styles from the entire document
        const styleBlocks = [];

        // First try to extract style elements using DOM parsing
        const styleElements = tempDiv.querySelectorAll('style');
        if (styleElements.length > 0) {
            console.log(`Found ${styleElements.length} style blocks from DOM parsing`);
            styleElements.forEach(style => {
                styleBlocks.push(style.textContent);
            });
        } else {
            // Fallback to regex if DOM parsing fails
            const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
            let styleMatch;
            while ((styleMatch = styleRegex.exec(content)) !== null) {
                styleBlocks.push(styleMatch[1]);
            }
        }

        // Create a container for the visual editor with proper styling
        visualEditor.innerHTML = '';
        console.log("Creating iframe for visual editor");

        // Create an iframe to properly render the content with all CSS
        const iframe = document.createElement('iframe');

        // Set fixed dimensions initially
        iframe.style.width = '100%';
        iframe.style.height = '600px'; // Start with a fixed height
        iframe.style.border = 'none';
        iframe.style.display = 'block';

        // Allow scrolling initially to ensure content is accessible
        iframe.style.overflow = 'auto';
        iframe.setAttribute('scrolling', 'yes');
        iframe.setAttribute('frameborder', '0');

        // Add a class for easier styling
        iframe.className = 'visual-editor-iframe';

        // Add a unique ID for easier reference
        iframe.id = 'visual-editor-iframe-' + Date.now();

        // Add sandbox attribute to allow scripts but maintain security
        iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts');

        // Add important attributes for accessibility and SEO
        iframe.setAttribute('title', 'Visual HTML Editor');
        iframe.setAttribute('aria-label', 'Visual HTML Editor');

        // Add data attributes to track state
        iframe.setAttribute('data-editor-mode', 'visual');
        iframe.setAttribute('data-loaded', 'false');

        // Append to the visual editor
        visualEditor.appendChild(iframe);
        console.log("Iframe appended to visual editor");

        // Add a loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'iframe-loading-indicator';
        loadingIndicator.innerHTML = '<div class="spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">Loading visual editor...</div>';
        visualEditor.appendChild(loadingIndicator);

        // Set a timeout to show an error message if the iframe doesn't load within 5 seconds
        const iframeLoadTimeout = setTimeout(() => {
            if (iframe.getAttribute('data-loaded') !== 'true') {
                console.error('Iframe failed to load within timeout period');
                loadingIndicator.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Failed to load visual editor. Please try again or switch to code view.</div>';
            }
        }, 5000);

        // Wait for iframe to load
        iframe.onload = function() {
            console.log("Iframe loaded, initializing content");

            // Clear the timeout and update the data-loaded attribute
            clearTimeout(iframeLoadTimeout);
            iframe.setAttribute('data-loaded', 'true');

            // Remove the loading indicator
            if (loadingIndicator && loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Get the iframe document
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (!iframeDoc) {
                console.error("Could not access iframe document");
                const errorDiv = document.createElement('div');
                errorDiv.className = 'iframe-error';
                errorDiv.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Failed to access iframe document. Please try again or switch to code view.</div>';
                visualEditor.appendChild(errorDiv);
                return;
            }

            try {
                // Create a complete HTML document
                iframeDoc.open();
                iframeDoc.write('<!DOCTYPE html><html><head>');
                console.log("Writing HTML document to iframe");

                // Add meta tags for proper rendering
                iframeDoc.write('<meta charset="UTF-8">');
                iframeDoc.write('<meta name="viewport" content="width=device-width, initial-scale=1.0">');

                // Add base tag to resolve relative URLs
                // Make sure the base URL ends with a slash
                let baseUrl = siteBaseUrl;
                if (!baseUrl.endsWith('/')) {
                    baseUrl += '/';
                }
                console.log('Using base URL for iframe:', baseUrl);
                iframeDoc.write('<base href="' + baseUrl + '">');

                // Add all CSS links with proper error handling
                cssLinks.forEach(href => {
                    console.log('Adding CSS to iframe:', href);
                    // Add onerror handler to log failed CSS loads
                    iframeDoc.write('<link rel="stylesheet" href="' + href + '" onerror="console.error(\'Failed to load CSS: \' + this.href)">');
                });

                // Add all style blocks
                if (styleBlocks.length > 0) {
                    console.log(`Adding ${styleBlocks.length} style blocks to iframe`);
                    iframeDoc.write('<style>' + styleBlocks.join('\n') + '</style>');
                }

                // Add additional CSS to ensure all content is visible
                iframeDoc.write(`
                    <style>
                        /* Force all images to be visible */
                        img {
                            display: inline-block !important;
                            visibility: visible !important;
                            opacity: 1 !important;
                        }

                        /* Ensure all elements have proper box model */
                        * {
                            box-sizing: border-box;
                        }
                    </style>
                `);
            } catch (error) {
                console.error("Error writing to iframe document:", error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'iframe-error';
                errorDiv.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Error initializing visual editor: ' + error.message + '</div>';
                visualEditor.appendChild(errorDiv);
                return;
            }

            // Add custom styles for read-only sections and fallback styles
            iframeDoc.write(`
                <style>
                    /* Fallback styles to ensure content is visible even if external CSS fails to load */
                    body {
                        font-family: Arial, sans-serif;
                        font-size: 14px;
                        line-height: 1.5;
                        color: #333;
                        background-color: #fff;
                    }
                    h1, h2, h3, h4, h5, h6 {
                        margin-top: 1em;
                        margin-bottom: 0.5em;
                        font-weight: bold;
                        color: #000;
                    }
                    p {
                        margin-bottom: 1em;
                    }
                    a {
                        color: #0066cc;
                        text-decoration: underline;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                        border: 0;
                    }
                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 1em;
                    }
                    table, th, td {
                        border: 1px solid #ddd;
                    }
                    th, td {
                        padding: 8px;
                        text-align: left;
                    }

                    /* Read-only section styles */
                    .read-only-section {
                        position: relative;
                        opacity: 0.8;
                        background-color: #f8f9fa;
                        pointer-events: none;
                        user-select: none;
                        border: 1px dashed #ccc;
                        padding: 10px;
                        margin: 10px 0;
                    }
                    .read-only-section::before {
                        content: "Read-only section";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        background-color: rgba(0,0,0,0.1);
                        color: #495057;
                        font-size: 12px;
                        padding: 2px 8px;
                        z-index: 100;
                    }

                    /* Basic layout */
                    html, body {
                        margin: 0;
                        padding: 15px;
                        height: auto;
                        overflow: auto; /* Allow scrolling */
                    }

                    /* Helper element for height calculation */
                    #content-height-calculator {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        visibility: hidden;
                        pointer-events: none;
                    }
                </style>
            `);

            // Add a script to help with height calculation, but defer execution until body is available
            iframeDoc.write(`
                <script>
                    // Function to calculate and report content height
                    function reportContentHeight() {
                        if (!document.body) {
                            console.warn('Document body not available yet');
                            return { maxHeight: 600 };
                        }

                        const body = document.body;
                        const html = document.documentElement;

                        // Get various height measurements
                        const scrollHeight = Math.max(
                            body.scrollHeight || 0,
                            html.scrollHeight || 0
                        );

                        const offsetHeight = Math.max(
                            body.offsetHeight || 0,
                            html.offsetHeight || 0
                        );

                        const clientHeight = Math.max(
                            body.clientHeight || 0,
                            html.clientHeight || 0
                        );

                        // Calculate total height of all elements
                        let totalHeight = 0;
                        if (body.children) {
                            const elements = body.children;
                            for (let i = 0; i < elements.length; i++) {
                                if (elements[i].id !== 'content-height-calculator') {
                                    const rect = elements[i].getBoundingClientRect();
                                    totalHeight += rect.height;
                                }
                            }
                        }

                        // Get the maximum of all measurements
                        const maxHeight = Math.max(
                            scrollHeight,
                            offsetHeight,
                            clientHeight,
                            totalHeight,
                            600 // Minimum height
                        );

                        // Return the calculated height
                        return {
                            scrollHeight,
                            offsetHeight,
                            clientHeight,
                            totalHeight,
                            maxHeight
                        };
                    }

                    // Wait for document to be ready before adding the helper element
                    document.addEventListener('DOMContentLoaded', function() {
                        if (document.body) {
                            const heightCalculator = document.createElement('div');
                            heightCalculator.id = 'content-height-calculator';
                            document.body.appendChild(heightCalculator);
                        }
                    });
                </script>
            `);

            // Add any head content
            if (headContent) {
                iframeDoc.write(headContent);
            }

            // No auto-resizing script needed with fixed height approach

            try {
                iframeDoc.write('</head><body>');
                console.log("Writing body content to iframe");

                // Process body content to make header and footer read-only
                // First process all resource paths in the body content
                let processedContent = processHtmlResources(bodyContent);

                // Create a temporary div to parse the body content
                const bodyTempDiv = document.createElement('div');
                bodyTempDiv.innerHTML = processedContent;

                // Extract header if present using DOM parsing
                const headerElement = bodyTempDiv.querySelector('header');
                if (headerElement) {
                    const headerContent = headerElement.outerHTML;
                    // Replace header with a read-only version
                    const readOnlyHeader = '<div class="read-only-section">' + headerContent + '</div>';
                    // Use DOM replacement to ensure proper HTML structure
                    headerElement.outerHTML = readOnlyHeader;
                    console.log("Processed header content using DOM parsing");
                    processedContent = bodyTempDiv.innerHTML;
                } else {
                    // Fallback to regex if DOM parsing fails
                    const headerMatch = bodyContent.match(/<header[^>]*>[\s\S]*?<\/header>/i);
                    if (headerMatch) {
                        const headerContent = headerMatch[0];
                        // Replace header with a read-only version
                        const readOnlyHeader = '<div class="read-only-section">' + headerContent + '</div>';
                        processedContent = processedContent.replace(headerContent, readOnlyHeader);
                        console.log("Processed header content using regex");
                    }
                }

                // Reset the temp div if we used regex for header
                if (!headerElement) {
                    bodyTempDiv.innerHTML = processedContent;
                }

                // Extract footer if present using DOM parsing
                const footerElement = bodyTempDiv.querySelector('footer');
                if (footerElement) {
                    const footerContent = footerElement.outerHTML;
                    // Replace footer with a read-only version
                    const readOnlyFooter = '<div class="read-only-section">' + footerContent + '</div>';
                    // Use DOM replacement to ensure proper HTML structure
                    footerElement.outerHTML = readOnlyFooter;
                    console.log("Processed footer content using DOM parsing");
                    processedContent = bodyTempDiv.innerHTML;
                } else {
                    // Fallback to regex if DOM parsing fails
                    const footerMatch = processedContent.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);
                    if (footerMatch) {
                        const footerContent = footerMatch[0];
                        // Replace footer with a read-only version
                        const readOnlyFooter = '<div class="read-only-section">' + footerContent + '</div>';
                        processedContent = processedContent.replace(footerContent, readOnlyFooter);
                        console.log("Processed footer content using regex");
                    }
                }

                console.log("Writing processed content to iframe:", processedContent.substring(0, 100) + "...");
                iframeDoc.write(processedContent);
                iframeDoc.write('</body></html>');
                iframeDoc.close();
                console.log("Iframe document closed, content should be visible now");
            } catch (error) {
                console.error("Error writing body content to iframe:", error);
                if (iframeDoc) {
                    try {
                        // Try to close the document even if there was an error
                        iframeDoc.write('</body></html>');
                        iframeDoc.close();
                    } catch (closeError) {
                        console.error("Error closing iframe document:", closeError);
                    }
                }

                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'iframe-error';
                errorDiv.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Error writing content to visual editor: ' + error.message + '</div>';
                visualEditor.appendChild(errorDiv);
                return;
            }

            // Make sure the iframe document and body are available before proceeding
            if (iframeDoc && iframeDoc.body) {
                try {
                    // Make the iframe body editable
                    iframeDoc.body.contentEditable = true;
                    console.log("Made iframe body editable");

                    // Add a class to the body for styling
                    iframeDoc.body.classList.add('visual-editor-body');

                    // Add event listeners for content changes
                    iframeDoc.body.addEventListener('input', function() {
                        console.log("Content changed in visual editor");
                        isModified = true;

                        // If this is the first edit, acquire lock
                        if (!window.lockAcquired && currentFilePath) {
                            acquireLockOnFirstEdit();
                        }
                    });

                    // Add event listener for keydown to handle tab key
                    iframeDoc.body.addEventListener('keydown', function(e) {
                        if (e.key === 'Tab') {
                            e.preventDefault();
                            document.execCommand('insertHTML', false, '&nbsp;&nbsp;&nbsp;&nbsp;');
                        }
                    });

                    // Force a repaint to ensure content is visible
                    setTimeout(() => {
                        iframe.style.height = iframe.style.height;
                        console.log("Forced iframe repaint");

                        // Add another repaint after a longer delay
                        setTimeout(() => {
                            iframe.style.height = iframe.style.height;
                            console.log("Second forced iframe repaint");

                            // Check if content is visible
                            const bodyHeight = iframeDoc.body.scrollHeight;
                            console.log("Body height:", bodyHeight);

                            if (bodyHeight < 100) {
                                console.warn("Body height is suspiciously small, content may not be visible");
                                // Try to force visibility
                                iframeDoc.body.style.display = 'block';
                                iframeDoc.body.style.visibility = 'visible';
                                iframeDoc.body.style.opacity = '1';
                            }
                        }, 500);
                    }, 100);
                } catch (error) {
                    console.error('Error making iframe body editable:', error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'iframe-error';
                    errorDiv.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Error initializing visual editor: ' + error.message + '</div>';
                    visualEditor.appendChild(errorDiv);
                }
            } else {
                console.error('Iframe document or body not available');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'iframe-error';
                errorDiv.innerHTML = '<div class="error-icon"><i class="fas fa-exclamation-circle"></i></div><div class="error-text">Iframe document or body not available. Please try again or switch to code view.</div>';
                visualEditor.appendChild(errorDiv);
                return; // Exit early if body is not available
            }

            // Helper function to acquire lock on first edit
            function acquireLockOnFirstEdit() {
                // Set lock acquired flag immediately for UI feedback
                window.lockAcquired = true;

                // Update lock status in UI immediately
                updateLockStatus(true, null);

                // Show the unlock button immediately
                const unlockButton = document.getElementById('unlockFileBtn');
                if (unlockButton) {
                    unlockButton.style.display = 'inline-block';
                }

                // Actually acquire the lock on the server
                acquireLock(currentFilePath).then(success => {
                    if (!success) {
                        // If lock acquisition failed, revert the UI
                        window.lockAcquired = false;
                        console.error('Failed to acquire lock on server');
                    } else {
                        console.log('Lock acquired on first edit in visual editor iframe');
                    }
                });
            }

            // Function to adjust iframe height - simplified to use fixed height
            function adjustIframeHeight() {
                // We're using a fixed height approach to avoid flickering
                // No dynamic height calculation needed
                console.log('Using fixed height for visual editor');
            }

            // Initial height adjustment
            adjustIframeHeight();

            // We're using a fixed height approach, so we don't need complex observers
            // Just add a simple event listener for window resize
            window.addEventListener('resize', function() {
                // No action needed for fixed height
            });

            // Add event listener to capture changes
            iframeDoc.body.addEventListener('input', function() {
                // Adjust height on input
                adjustIframeHeight();
                // If this is the first edit, acquire lock
                if (!isModified && !window.lockAcquired && currentFilePath) {
                    // Set lock acquired flag immediately for UI feedback
                    window.lockAcquired = true;

                    // Update lock status in UI immediately
                    updateLockStatus(true, null);

                    // Show the unlock button immediately
                    const unlockButton = document.getElementById('unlockFileBtn');
                    if (unlockButton) {
                        unlockButton.style.display = 'inline-block';
                    }

                    // Actually acquire the lock on the server
                    acquireLock(currentFilePath).then(success => {
                        if (!success) {
                            // If lock acquisition failed, revert the UI
                            window.lockAcquired = false;
                            console.error('Failed to acquire lock on server');
                        } else {
                            console.log('Lock acquired on first edit in visual editor iframe');
                        }
                    });
                }

                // Mark as modified
                isModified = true;
            });

            // Store reference to iframe for later use
            window.visualEditorIframe = iframe;
        };

        // Fix relative image paths
        const images = visualEditor.querySelectorAll('img');
        images.forEach(img => {
            // Get the original src attribute
            const srcAttr = img.getAttribute('src');

            // Store the original path as a data attribute for later use
            if (srcAttr) {
                img.setAttribute('data-original-src', srcAttr);

                // Convert all image paths to ensure they're correct
                const absolutePath = getAbsoluteImagePath(srcAttr);

                // Update the image src
                img.src = absolutePath;

                // Log for debugging
                console.log('Image path updated:', {
                    original: srcAttr,
                    absolutePath: absolutePath
                });
            }
        });
    }
}

// Update code from visual editor
function updateCodeFromVisual() {
    const visualEditor = document.getElementById('visual-editor');

    if (editor && visualEditor) {
        const content = editor.getValue();

        // Get the content directly from the visual editor (simplified approach)
        let visualContent = visualEditor.innerHTML;

        // Process the content to restore original image paths
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = visualContent;

        // Restore original image paths from data attributes
        const images = tempDiv.querySelectorAll('img');
        images.forEach(img => {
            const originalSrc = img.getAttribute('data-original-src');
            if (originalSrc) {
                img.setAttribute('src', originalSrc);
                img.removeAttribute('data-original-src');
                console.log('Restored original image path:', originalSrc);
            } else {
                // Try to convert absolute URLs back to relative if they match our site base URL
                const currentSrc = img.getAttribute('src');
                if (currentSrc && currentSrc.includes(window.location.origin)) {
                    // Extract the path after the origin
                    const urlObj = new URL(currentSrc);
                    let path = urlObj.pathname;

                    // If the path includes the site path (e.g., /manageinc/), extract just the relative part
                    const sitePath = new URL(siteBaseUrl).pathname;
                    if (sitePath && sitePath !== '/' && path.startsWith(sitePath)) {
                        path = path.substring(sitePath.length);
                        if (path.startsWith('/')) path = path.substring(1);
                    }

                    console.log('Converting absolute URL back to relative:', {
                        original: currentSrc,
                        relative: path
                    });

                    img.setAttribute('src', path);
                }
            }
        });

        // Get the processed content
        visualContent = tempDiv.innerHTML;

        // Determine where to insert the content
        let newContent = content;

        // Check if the document has body tags
        const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        if (bodyMatch) {
            // Replace content between body tags
            const bodyStart = bodyMatch.index + bodyMatch[0].match(/<body[^>]*>/i)[0].length;
            const bodyEnd = bodyMatch.index + bodyMatch[0].lastIndexOf('</body>');

            newContent = content.substring(0, bodyStart) +
                        visualContent +
                        content.substring(bodyEnd);
        } else {
            // If no body tags, look for header and footer
            let bodyStart = 0;
            let bodyEnd = content.length;

            // Find header if present
            const headerMatch = content.match(/<header[^>]*>[\s\S]*?<\/header>/i);
            if (headerMatch) {
                bodyStart = headerMatch.index + headerMatch[0].length;
            }

            // Find footer if present
            const footerMatch = content.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);
            if (footerMatch) {
                bodyEnd = footerMatch.index;
            }

            // Replace content between header and footer
            newContent = content.substring(0, bodyStart) +
                        visualContent +
                        content.substring(bodyEnd);
        }

        // Update code editor
        editor.setValue(newContent);

        // Mark as modified
        isModified = true;
    } else {
        console.error('Editor or iframe not initialized');
    }
}

// Detect the base URL of the site
let siteBaseUrl = '';

// Initialize site base URL detection
function detectSiteBaseUrl() {
    // Try to get the base URL from a meta tag if available
    const baseUrlMeta = document.querySelector('meta[name="site-base-url"]');
    const adminBaseUrlMeta = document.querySelector('meta[name="admin-base-url"]');

    if (baseUrlMeta && baseUrlMeta.getAttribute('content')) {
        siteBaseUrl = baseUrlMeta.getAttribute('content');
        console.log('Base URL detected from meta tag:', siteBaseUrl);

        // Also store admin base URL if available
        if (adminBaseUrlMeta && adminBaseUrlMeta.getAttribute('content')) {
            window.adminBaseUrl = adminBaseUrlMeta.getAttribute('content');
            console.log('Admin base URL detected from meta tag:', window.adminBaseUrl);
        }

        // Convert relative image paths to absolute URLs
        convertRelativeImagePaths();
        return;
    }

    // Extract site path from current URL
    const currentPath = window.location.pathname;
    const adminIndex = currentPath.indexOf('/admin/');
    if (adminIndex !== -1) {
        // Get the site path (e.g., /manageinc)
        const sitePath = currentPath.substring(0, adminIndex);
        siteBaseUrl = window.location.origin + sitePath;
        console.log('Base URL detected from current path:', siteBaseUrl);

        // Convert relative image paths to absolute URLs
        convertRelativeImagePaths();
        return;
    }

    // Simple fallback - just use the origin
    siteBaseUrl = window.location.origin;
    console.log('Using origin as base URL:', siteBaseUrl);

    // Try to determine from the current file path
    if (currentFilePath) {
        // Make an AJAX request to get the site root
        fetch('ajax/html_editor.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            },
            body: 'action=get_site_root'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.text().then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON response:', text);
                    throw new Error('Invalid JSON response from server');
                }
            });
        })
        .then(data => {
            if (data.success && data.site_root) {
                siteBaseUrl = data.site_root;
                console.log('Base URL detected from server:', siteBaseUrl);

                // Convert relative image paths to absolute URLs
                convertRelativeImagePaths();
            }
        })
        .catch(error => {
            console.error('Error detecting site base URL:', error);
            // We already set a fallback above, so no need to do it again
        });
    }

    // Convert relative image paths to absolute URLs with the fallback URL
    convertRelativeImagePaths();
}

// Convert relative paths to absolute URLs in the editor content
function convertRelativeImagePaths() {
    if (!editor) return;

    console.log('Converting relative paths to absolute URLs...');

    // Get the current content
    const content = editor.getValue();

    // Create a temporary DOM element to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Find all images
    const images = tempDiv.querySelectorAll('img');
    let hasChanges = false;

    // Process each image
    images.forEach(img => {
        const src = img.getAttribute('src');
        if (src && !src.match(/^(https?:\/\/|data:|blob:)/i)) {
            // This is a relative path
            console.log('Converting relative image path:', src);

            // Use our helper function to get the absolute path
            const absoluteUrl = getAbsoluteImagePath(src);

            // Update the src attribute
            img.setAttribute('src', absoluteUrl);
            hasChanges = true;
        }
    });

    // Find all CSS links
    const links = tempDiv.querySelectorAll('link[rel="stylesheet"]');

    // Process each CSS link
    links.forEach(link => {
        const href = link.getAttribute('href');
        if (href && !href.match(/^(https?:\/\/|\/\/)/i)) {
            // This is a relative path
            console.log('Converting relative CSS path:', href);

            // Use our helper function to get the absolute path
            const absoluteUrl = getAbsoluteImagePath(href);

            // Update the href attribute
            link.setAttribute('href', absoluteUrl);
            hasChanges = true;
        }
    });

    // If changes were made, update the editor content
    if (hasChanges) {
        console.log('Updating editor content with absolute URLs');
        editor.setValue(tempDiv.innerHTML);

        // Reset the modified flag since this is just initialization
        isModified = false;
    }
}

// Helper function to convert relative paths to absolute
function getAbsoluteImagePath(relativePath) {
    if (!relativePath) {
        return relativePath;
    }

    // If it's already an absolute URL or data URL, return as is
    if (relativePath.startsWith('http') || relativePath.startsWith('//') || relativePath.startsWith('data:') || relativePath.startsWith('blob:')) {
        return relativePath; // Already absolute or data URL
    }

    try {
        // Make sure we have a base URL
        if (!siteBaseUrl) {
            // If no base URL is set, use the origin
            siteBaseUrl = window.location.origin;
        }

        // Make sure the base URL ends with a slash
        const baseUrl = siteBaseUrl.endsWith('/') ? siteBaseUrl : siteBaseUrl + '/';

        // Check if the path starts with "admin/" and remove it
        let cleanPath = relativePath;
        if (cleanPath.startsWith('admin/')) {
            cleanPath = cleanPath.substring(6); // Remove "admin/"
            console.log('Removed "admin/" prefix from path:', cleanPath);
        }

        // Remove any leading slash from the path
        cleanPath = cleanPath.replace(/^\//, '');

        // Check if the path is a document root path (e.g., images/...)
        // We don't want to add "admin/" to these paths
        const isDocumentRootPath = cleanPath.startsWith('images/') ||
                                  cleanPath.startsWith('css/') ||
                                  cleanPath.startsWith('js/') ||
                                  cleanPath.startsWith('fonts/') ||
                                  cleanPath.startsWith('assets/') ||
                                  cleanPath.startsWith('uploads/') ||
                                  cleanPath.startsWith('documents/') ||
                                  cleanPath.startsWith('manageinc/images/');

        // For JS files that are in the site root (like carousel.js), ensure they don't get admin prefix
        if (cleanPath.startsWith('js/') && !cleanPath.startsWith('js/admin/')) {
            // This is a site root JS file, use it as-is
            console.log('Site root JS file detected:', cleanPath);
        }

        // Create the absolute path
        const absolutePath = baseUrl + cleanPath;

        console.log('Path conversion:', {
            original: relativePath,
            baseUrl: baseUrl,
            cleanPath: cleanPath,
            isDocumentRootPath: isDocumentRootPath,
            absolute: absolutePath
        });

        return absolutePath;
    } catch (e) {
        console.error('Error creating absolute path:', e);
        // Fallback to a simple concatenation
        const origin = window.location.origin;

        // Check if the path starts with "admin/" and remove it
        let cleanPath = relativePath;
        if (cleanPath.startsWith('admin/')) {
            cleanPath = cleanPath.substring(6); // Remove "admin/"
        }

        // Remove any leading slash from the path
        cleanPath = cleanPath.replace(/^\//, '');

        return origin + '/' + cleanPath;
    }
}

// Process all resource paths in HTML content
function processHtmlResources(htmlContent) {
    if (!htmlContent) return htmlContent;

    console.log("Processing HTML resources");

    // Create a temporary div to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // Process all image sources
    const images = tempDiv.querySelectorAll('img');
    images.forEach(img => {
        const src = img.getAttribute('src');
        if (src && !src.startsWith('http') && !src.startsWith('//') && !src.startsWith('data:')) {
            // Check if the path starts with "admin/" and remove it
            let cleanSrc = src;
            if (cleanSrc.startsWith('admin/')) {
                cleanSrc = cleanSrc.substring(6); // Remove "admin/"
                console.log('Removed "admin/" prefix from image path:', cleanSrc);
            }

            // Store the original path as a data attribute
            img.setAttribute('data-original-src', src);

            // Convert to absolute path
            const absolutePath = getAbsoluteImagePath(cleanSrc);

            // Update the image src
            img.setAttribute('src', absolutePath);

            console.log('Image path processed:', {
                original: src,
                cleaned: cleanSrc,
                absolute: absolutePath
            });
        }
    });

    // Return the processed HTML
    return tempDiv.innerHTML;
}

// Function to adjust editor height based on content
function adjustEditorHeight() {
    if (!editor) return;

    try {
        // Get the editor wrapper element
        const wrapper = editor.getWrapperElement();
        if (!wrapper) return;

        // Get the content height
        const contentHeight = editor.doc.height;
        console.log('Content height:', contentHeight);

        // Set minimum height with padding - ensure it's large enough
        const minHeight = Math.max(600, contentHeight + 200);

        // Apply height to various elements
        wrapper.style.height = 'auto';
        wrapper.style.minHeight = minHeight + 'px';
        wrapper.style.maxHeight = 'none';
        wrapper.style.display = 'flex';
        wrapper.style.flexDirection = 'column';
        wrapper.style.flex = '1';

        // Get the scroll element
        const scroll = wrapper.querySelector('.CodeMirror-scroll');
        if (scroll) {
            scroll.style.height = 'auto';
            scroll.style.minHeight = minHeight + 'px';
            scroll.style.maxHeight = 'none';
            scroll.style.overflowY = 'hidden';
            scroll.style.display = 'flex';
            scroll.style.flexDirection = 'column';
            scroll.style.flex = '1';
        }

        // Get the sizer element
        const sizer = wrapper.querySelector('.CodeMirror-sizer');
        if (sizer) {
            sizer.style.minHeight = contentHeight + 'px';
            sizer.style.flex = '1';
        }

        // Also adjust the container
        const container = document.getElementById('editor-container');
        if (container) {
            container.style.height = 'auto';
            container.style.minHeight = minHeight + 'px';
            container.style.maxHeight = 'none';
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.flex = '1';
        }

        // Adjust the editor content container
        const editorContent = document.getElementById('editor-content-container');
        if (editorContent) {
            editorContent.style.height = 'auto';
            editorContent.style.minHeight = minHeight + 'px';
            editorContent.style.maxHeight = 'none';
            editorContent.style.display = 'flex';
            editorContent.style.flexDirection = 'column';
            editorContent.style.flex = '1';
        }

        // Adjust the textarea
        const textarea = document.getElementById('editor-textarea');
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.minHeight = minHeight + 'px';
        }

        // Force a refresh
        editor.refresh();

        // Set the editor size again
        editor.setSize(null, minHeight);

        console.log('Editor height adjusted to:', minHeight);
    } catch (e) {
        console.error('Error adjusting editor height:', e);
    }
}

// Check if a file contains dynamic content
function containsDynamicContent(content) {
    // Check for PHP tags
    if (content.includes('<?php') || content.includes('<?=') || content.includes('<%')) {
        return true;
    }

    // Check for server-side includes
    if (content.includes('<!--#include')) {
        return true;
    }

    // Check for template syntax
    if (content.includes('{{') || content.includes('}}') ||
        content.includes('{%') || content.includes('%}') ||
        content.includes('@if') || content.includes('@foreach')) {
        return true;
    }

    return false;
}

// Function to update code editor from visual editor
function updateCodeFromVisual() {
    if (!window.visualEditorIframe || !editor) return;

    try {
        const iframeDoc = window.visualEditorIframe.contentDocument || window.visualEditorIframe.contentWindow.document;
        const bodyContent = iframeDoc.body.innerHTML;

        // Get the original content structure
        const originalContent = editor.getValue();

        // Replace body content while preserving head and structure
        let updatedContent = originalContent;
        const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);

        if (bodyMatch) {
            updatedContent = originalContent.replace(bodyMatch[1], bodyContent);
        } else {
            // If no body tags, replace the main content area
            updatedContent = bodyContent;
        }

        editor.setValue(updatedContent);
        console.log("Code editor updated from visual editor");
    } catch (error) {
        console.error("Error updating code from visual:", error);
    }
}

// Add this function to the global scope for external access
window.updateCodeFromVisual = updateCodeFromVisual;
