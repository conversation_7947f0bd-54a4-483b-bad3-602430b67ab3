/**
 * HTML Editor
 *
 * Main JavaScript file for the HTML editor
 */

// Global variables
let currentFilePath = '';
let editor = null;
let isModified = false;
let isAdmin = false;
let currentUserId = 0;
let currentUsername = '';
let currentMode = 'code'; // Track current editor mode
let visualEditor = null; // Quill editor instance

// Initialize the editor
document.addEventListener('DOMContentLoaded', function() {
    // Wait for admin header to be loaded before initializing editor
    function initializeEditor() {
        // Set global variables
        currentFilePath = document.querySelector('input[name="file_path"]')?.value || '';
        isAdmin = document.body.dataset.isAdmin === 'true';
        currentUserId = parseInt(document.body.dataset.userId || '0');
        currentUsername = document.body.dataset.username || '';

        // Detect site base URL
        detectSiteBaseUrl();

        // Initialize file selector
        initFileSelector();

        // Initialize editor if a file is selected
        if (currentFilePath) {
            initEditor();

            // Initialize toolbar
            initToolbar();

            // Check file lock
            checkLock(currentFilePath);

            // We'll acquire lock on first edit instead of immediately
            // Set flag to track if lock has been acquired
            window.lockAcquired = false;

            // Check if the file is already locked by the current user
            checkLock(currentFilePath).then(lockData => {
                if (lockData && lockData.locked_by_current_user) {
                    window.lockAcquired = true;

                    // Show the unlock button
                    const unlockButton = document.getElementById('unlockFileBtn');
                    if (unlockButton) {
                        unlockButton.style.display = 'inline-block';
                    }
                }
            });

            // Extend lock every 4 minutes (only if lock has been acquired)
            setInterval(function() {
                if (currentFilePath && window.lockAcquired) {
                    extendLock(currentFilePath);
                }
            }, 240000); // 4 minutes

            // Release lock when leaving the page
            window.addEventListener('beforeunload', function(e) {
                if (isModified) {
                    // Show confirmation dialog if file is modified
                    e.preventDefault();
                    e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                }

                // Release lock
                if (currentFilePath) {
                    releaseLock(currentFilePath);
                }
            });

            // Initialize version history
            initVersionHistory();

            // Load version history
            loadVersionHistory();

            // Add window resize event to adjust editor height
            window.addEventListener('resize', function() {
                if (editor) {
                    setTimeout(function() {
                        editor.refresh();
                        adjustEditorHeight();
                    }, 100);
                }
            });
        }

        // Initialize save button
        const saveButton = document.querySelector('button[name="save_file"]');
        if (saveButton) {
            saveButton.addEventListener('click', function(e) {
                e.preventDefault();
                saveFile();
            });
        }

        // Initialize unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.addEventListener('click', function(e) {
                // We're using a direct link now, so we just need to confirm
                if (!confirm('Are you sure you want to unlock this file? This will allow other users to edit it.')) {
                    e.preventDefault(); // Prevent the default link action if user cancels
                }
            });
        }

        // Initialize force unlock button
        const forceUnlockButton = document.getElementById('forceUnlockBtn');
        if (forceUnlockButton) {
            forceUnlockButton.addEventListener('click', function() {
                if (confirm('Are you sure you want to force unlock this file? This will override the current user\'s lock.')) {
                    forceReleaseLock(currentFilePath).then(function(success) {
                        if (success) {
                            alert('File force unlocked successfully.');
                            window.location.reload();
                        }
                    });
                }
            });
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);

        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

// Initialize file selector
function initFileSelector() {
    const fileSelector = document.getElementById('fileSelector');
    if (fileSelector) {
        fileSelector.addEventListener('change', function() {
            if (this.value) {
                // Check if there are unsaved changes
                if (isModified) {
                    if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
                        // Release lock on current file
                        if (currentFilePath) {
                            releaseLock(currentFilePath);
                        }

                        // Navigate to selected file
                        window.location.href = this.value;
                    } else {
                        // Reset selector to current file
                        this.value = currentFilePath ? 'html_editor.php?file=' + encodeURIComponent(currentFilePath) : '';
                    }
                } else {
                    // Release lock on current file
                    if (currentFilePath) {
                        releaseLock(currentFilePath);
                    }

                    // Navigate to selected file
                    window.location.href = this.value;
                }
            }
        });
    }
}

// Initialize editor
function initEditor() {
    const textarea = document.getElementById('editor-textarea');
    if (textarea) {
        // Debug: Log the textarea content
        console.log('Textarea content:', textarea.value);

        // Initialize CodeMirror directly
        editor = CodeMirror.fromTextArea(textarea, {
            lineNumbers: true,
            matchBrackets: true,
            autoCloseBrackets: true,
            autoCloseTags: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
            styleActiveLine: true,
            theme: 'material',
            mode: 'htmlmixed',
            indentUnit: 4,
            tabSize: 4,
            indentWithTabs: false,
            extraKeys: {
                "Ctrl-Space": "autocomplete",
                "Tab": function(cm) {
                    if (cm.somethingSelected()) {
                        cm.indentSelection("add");
                    } else {
                        cm.replaceSelection("    ", "end", "+input");
                    }
                }
            }
        });

        // Set height to auto-adjust to content with a large value to ensure it shows all content
        editor.setSize(null, "auto");

        // Force the editor to take full height
        const editorElement = editor.getWrapperElement();
        if (editorElement) {
            editorElement.style.height = 'auto';
            editorElement.style.minHeight = '600px';

            // Apply direct styles to ensure proper height
            editorElement.style.display = 'flex';
            editorElement.style.flexDirection = 'column';
            editorElement.style.flex = '1';
            editorElement.style.maxHeight = 'none';

            // Also apply to the scroll element
            const scrollElement = editorElement.querySelector('.CodeMirror-scroll');
            if (scrollElement) {
                scrollElement.style.height = 'auto';
                scrollElement.style.minHeight = '600px';
                scrollElement.style.maxHeight = 'none';
            }
        }

        // Ensure editor expands with content
        editor.on("change", function() {
            // Update size on change
            editor.setSize(null, "auto");

            // Use our comprehensive height adjustment function
            setTimeout(function() {
                adjustEditorHeight();
            }, 100);
        });

        // Debug: Log the editor content
        console.log('Editor content:', editor.getValue());

        // Force refresh the editor and adjust height
        setTimeout(function() {
            editor.refresh();
            console.log('Editor refreshed');

            // Calculate and set proper height based on content
            adjustEditorHeight();
        }, 100);

        // Add another delayed refresh to ensure proper rendering after page is fully loaded
        setTimeout(function() {
            editor.refresh();
            adjustEditorHeight();
        }, 500);

        // Set read-only sections for header and footer
        setReadOnlySections(editor);

        // Initialize visual editor
        initVisualEditor();

        // Track changes
        editor.on('change', function() {
            // If this is the first edit, acquire lock
            if (!isModified && !window.lockAcquired && currentFilePath) {
                // Set lock acquired flag immediately for UI feedback
                window.lockAcquired = true;

                // Update lock status in UI immediately
                updateLockStatus(true, null);

                // Show the unlock button immediately
                const unlockButton = document.getElementById('unlockFileBtn');
                if (unlockButton) {
                    unlockButton.style.display = 'inline-block';
                }

                // Actually acquire the lock on the server
                acquireLock(currentFilePath).then(success => {
                    if (!success) {
                        // If lock acquisition failed, revert the UI
                        window.lockAcquired = false;
                        console.error('Failed to acquire lock on server');
                    } else {
                        console.log('Lock acquired on first edit');
                    }
                });
            }

            isModified = true;

            // Update textarea value
            editor.save();
        });
    }
}

// Initialize visual editor
function initVisualEditor() {
    const visualEditorElement = document.getElementById('visual-editor');
    if (visualEditorElement && typeof Quill !== 'undefined') {
        // Configure Quill toolbar
        const toolbarOptions = [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['link', 'image', 'video'],
            ['clean']
        ];

        // Initialize Quill
        visualEditor = new Quill(visualEditorElement, {
            theme: 'snow',
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'Start editing your content...'
        });

        // Track changes in visual editor
        visualEditor.on('text-change', function() {
            if (!isModified) {
                // If this is the first edit, acquire lock
                if (!window.lockAcquired && currentFilePath) {
                    window.lockAcquired = true;
                    updateLockStatus(true, null);

                    const unlockButton = document.getElementById('unlockFileBtn');
                    if (unlockButton) {
                        unlockButton.style.display = 'inline-block';
                    }

                    acquireLock(currentFilePath).then(success => {
                        if (!success) {
                            window.lockAcquired = false;
                            console.error('Failed to acquire lock on server');
                        }
                    });
                }
            }
            isModified = true;
        });
    }
}

// Make header and footer sections read-only
function setReadOnlySections(editor) {
    const content = editor.getValue();
    const headerMatch = content.match(/<header[^>]*>[\s\S]*?<\/header>/i);
    const footerMatch = content.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);

    // Clear any existing read-only marks
    editor.getAllMarks().forEach(mark => mark.clear());

    // If header found, make it read-only
    if (headerMatch) {
        const headerStart = getLineNumber(content, headerMatch.index);
        const headerEnd = getLineNumber(content, headerMatch.index + headerMatch[0].length);

        // Mark header as read-only
        editor.markText(
            {line: headerStart, ch: 0},
            {line: headerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }

    // If footer found, make it read-only
    if (footerMatch) {
        const footerStart = getLineNumber(content, footerMatch.index);
        const footerEnd = getLineNumber(content, footerMatch.index + footerMatch[0].length);

        // Mark footer as read-only
        editor.markText(
            {line: footerStart, ch: 0},
            {line: footerEnd, ch: 0},
            {readOnly: true, className: 'read-only-section', inclusiveLeft: true, inclusiveRight: true}
        );
    }
}

// Get line number from character index
function getLineNumber(text, index) {
    const lines = text.substr(0, index).split('\n');
    return lines.length - 1;
}

// Update visual editor content from code
function updateVisualFromCode() {
    if (editor && visualEditor) {
        try {
            // Get the content from the code editor
            const content = editor.getValue();
            console.log("Updating visual editor with content:", content.substring(0, 100) + "...");

            // Extract body content or use full content
            let bodyContent = content;
            const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
            if (bodyMatch) {
                bodyContent = bodyMatch[1];
            } else {
                // Remove html, head, and body tags if present
                bodyContent = bodyContent.replace(/<\/?html[^>]*>/gi, '');
                bodyContent = bodyContent.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
                bodyContent = bodyContent.replace(/<\/?body[^>]*>/gi, '');
            }

            // Set the content in Quill editor
            visualEditor.root.innerHTML = bodyContent;

            console.log("Updated visual editor from code content");
        } catch (error) {
            console.error("Error updating visual editor:", error);
        }
    }
}

// Update code editor content from visual editor
function updateCodeFromVisual() {
    if (visualEditor && editor) {
        try {
            // Get HTML content from Quill editor
            const visualContent = visualEditor.root.innerHTML;

            // Get the original content to preserve structure
            const originalContent = editor.getValue();

            // Try to replace just the body content
            let updatedContent = originalContent;
            const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);

            if (bodyMatch) {
                // Replace body content
                updatedContent = originalContent.replace(bodyMatch[1], visualContent);
            } else {
                // If no body tags, replace the entire content
                updatedContent = visualContent;
            }

            // Update the code editor
            editor.setValue(updatedContent);

            console.log("Updated code editor from visual content");
        } catch (error) {
            console.error("Error updating code from visual:", error);
        }
    }
}

// Initialize toolbar
function initToolbar() {
    const viewCodeBtn = document.getElementById('viewCodeBtn');
    const viewVisualBtn = document.getElementById('viewVisualBtn');
    const formatCodeBtn = document.getElementById('formatCodeBtn');
    const findReplaceBtn = document.getElementById('findReplaceBtn');
    const editorContainer = document.getElementById('editor-container');
    const visualEditorContainer = document.getElementById('visual-editor-container');
    const wysiwygToolbar = document.getElementById('wysiwyg-toolbar');

    if (viewCodeBtn && viewVisualBtn && editorContainer && visualEditorContainer) {
        // View code button
        viewCodeBtn.addEventListener('click', function() {
            // Update button states
            viewCodeBtn.classList.add('btn-primary');
            viewCodeBtn.classList.remove('btn-outline-primary');
            viewVisualBtn.classList.add('btn-outline-primary');
            viewVisualBtn.classList.remove('btn-primary');

            // If coming from visual mode, update code first
            if (currentMode === 'visual') {
                updateCodeFromVisual();
                console.log('Switching from visual to code mode');
            }

            // Update current mode
            currentMode = 'code';

            // Update container visibility
            editorContainer.style.display = 'block';
            visualEditorContainer.style.display = 'none';
            if (wysiwygToolbar) wysiwygToolbar.style.display = 'none';

            // Remove visual-mode class from body
            document.body.classList.remove('visual-mode');

            // Show Format and Find buttons in code mode
            if (formatCodeBtn) formatCodeBtn.style.display = 'inline-block';
            if (findReplaceBtn) findReplaceBtn.style.display = 'inline-block';

            // Refresh editor to fix any display issues
            if (editor) {
                editor.refresh();
                setTimeout(function() {
                    adjustEditorHeight();
                }, 100);
            }
        });

        // View visual editor button
        viewVisualBtn.addEventListener('click', function() {
            // Update button states
            viewVisualBtn.classList.add('btn-primary');
            viewVisualBtn.classList.remove('btn-outline-primary');
            viewCodeBtn.classList.add('btn-outline-primary');
            viewCodeBtn.classList.remove('btn-primary');

            // Update current mode
            currentMode = 'visual';

            // Update container visibility
            editorContainer.style.display = 'none';
            visualEditorContainer.style.display = 'block';
            if (wysiwygToolbar) wysiwygToolbar.style.display = 'flex';

            // Add visual-mode class to body
            document.body.classList.add('visual-mode');

            // Hide Format and Find buttons in visual mode
            if (formatCodeBtn) formatCodeBtn.style.display = 'none';
            if (findReplaceBtn) findReplaceBtn.style.display = 'none';

            // Update visual editor content from code
            updateVisualFromCode();
        });

        // Format code button
        if (formatCodeBtn) {
            formatCodeBtn.addEventListener('click', function() {
                if (editor) {
                    formatCode();
                }
            });
        }

        // Find & Replace button
        if (findReplaceBtn) {
            findReplaceBtn.addEventListener('click', function() {
                if (editor) {
                    editor.execCommand('find');
                }
            });
        }
    }
}

// Format HTML code
function formatCode() {
    if (editor) {
        const content = editor.getValue();
        try {
            // Use HTML formatter
            const formattedContent = html_beautify(content, {
                indent_size: 4,
                indent_char: ' ',
                max_preserve_newlines: 2,
                preserve_newlines: true,
                keep_array_indentation: false,
                break_chained_methods: false,
                indent_scripts: 'normal',
                brace_style: 'collapse',
                space_before_conditional: true,
                unescape_strings: false,
                jslint_happy: false,
                end_with_newline: true,
                wrap_line_length: 0,
                indent_inner_html: true,
                comma_first: false,
                e4x: false,
                indent_empty_lines: false
            });

            // Set formatted content
            editor.setValue(formattedContent);

            // Show success message
            alert('Code formatted successfully!');
        } catch (e) {
            console.error('Error formatting code:', e);
            alert('Error formatting code: ' + e.message);
        }
    }
}

// Function to adjust editor height based on content
function adjustEditorHeight() {
    if (!editor) return;

    try {
        // Get the editor wrapper element
        const wrapper = editor.getWrapperElement();
        if (!wrapper) return;

        // Get the content height
        const contentHeight = editor.doc.height;
        console.log('Content height:', contentHeight);

        // Set minimum height with padding - ensure it's large enough
        const minHeight = Math.max(600, contentHeight + 200);

        // Apply height to various elements
        wrapper.style.height = 'auto';
        wrapper.style.minHeight = minHeight + 'px';
        wrapper.style.maxHeight = 'none';
        wrapper.style.display = 'flex';
        wrapper.style.flexDirection = 'column';
        wrapper.style.flex = '1';

        // Get the scroll element
        const scroll = wrapper.querySelector('.CodeMirror-scroll');
        if (scroll) {
            scroll.style.height = 'auto';
            scroll.style.minHeight = minHeight + 'px';
            scroll.style.maxHeight = 'none';
            scroll.style.overflowY = 'hidden';
            scroll.style.display = 'flex';
            scroll.style.flexDirection = 'column';
            scroll.style.flex = '1';
        }

        // Get the sizer element
        const sizer = wrapper.querySelector('.CodeMirror-sizer');
        if (sizer) {
            sizer.style.minHeight = contentHeight + 'px';
            sizer.style.flex = '1';
        }

        // Also adjust the container
        const container = document.getElementById('editor-container');
        if (container) {
            container.style.height = 'auto';
            container.style.minHeight = minHeight + 'px';
            container.style.maxHeight = 'none';
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.flex = '1';
        }

        // Force a refresh
        editor.refresh();

        // Set the editor size again
        editor.setSize(null, minHeight);

        console.log('Editor height adjusted to:', minHeight);
    } catch (e) {
        console.error('Error adjusting editor height:', e);
    }
}

// Detect the base URL of the site
let siteBaseUrl = '';

// Initialize site base URL detection
function detectSiteBaseUrl() {
    // Try to get the base URL from a meta tag if available
    const baseUrlMeta = document.querySelector('meta[name="site-base-url"]');

    if (baseUrlMeta && baseUrlMeta.getAttribute('content')) {
        siteBaseUrl = baseUrlMeta.getAttribute('content');
        console.log('Base URL detected from meta tag:', siteBaseUrl);
        return;
    }

    // Extract site path from current URL
    const currentPath = window.location.pathname;
    const adminIndex = currentPath.indexOf('/admin/');
    if (adminIndex !== -1) {
        // Get the site path (e.g., /manageinc)
        const sitePath = currentPath.substring(0, adminIndex);
        siteBaseUrl = window.location.origin + sitePath;
        console.log('Base URL detected from current path:', siteBaseUrl);
        return;
    }

    // Simple fallback - just use the origin
    siteBaseUrl = window.location.origin;
    console.log('Using origin as base URL:', siteBaseUrl);
}