<?php
/**
 * SMTP Setup Guide
 *
 * This page provides guidance on setting up SMTP for email delivery.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Get current email settings
$settings = [];
$sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'email'";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}

// Include header
include 'includes/header.php';
?>

<div class="admin-content">
    <div class="admin-content-inner">
        <div class="admin-card">
            <div class="admin-card-header">
                <h2><i class="fas fa-envelope"></i> SMTP Setup Guide</h2>
                <div class="admin-card-actions">
                    <a href="settings.php" class="admin-btn secondary"><i class="fas fa-arrow-left"></i> Back to Settings</a>
                </div>
            </div>
            <div class="admin-card-body">
                <div class="alert alert-info">
                    <p><strong>Note:</strong> You're seeing this guide because your server is configured to prevent sending emails to non-local domains using PHP's mail() function. This is a common security measure to prevent spam.</p>
                </div>

                <h3>Current Email Configuration</h3>
                <div class="settings-info">
                    <p><strong>SMTP Enabled:</strong> <?php echo isset($settings['use_smtp']) && $settings['use_smtp'] == '1' ? 'Yes' : 'No'; ?></p>
                    <p><strong>From Email:</strong> <?php echo isset($settings['from_email']) ? $settings['from_email'] : 'Not set'; ?></p>
                    <p><strong>From Name:</strong> <?php echo isset($settings['from_name']) ? $settings['from_name'] : 'Not set'; ?></p>
                    <p><strong>Reply To:</strong> <?php echo isset($settings['reply_to']) ? $settings['reply_to'] : 'Not set'; ?></p>
                    <?php if (isset($settings['use_smtp']) && $settings['use_smtp'] == '1'): ?>
                    <p><strong>SMTP Host:</strong> <?php echo isset($settings['smtp_host']) ? $settings['smtp_host'] : 'Not set'; ?></p>
                    <p><strong>SMTP Port:</strong> <?php echo isset($settings['smtp_port']) ? $settings['smtp_port'] : 'Not set'; ?></p>
                    <p><strong>SMTP Security:</strong> <?php echo isset($settings['smtp_security']) ? $settings['smtp_security'] : 'Not set'; ?></p>
                    <p><strong>SMTP Username:</strong> <?php echo isset($settings['smtp_username']) ? $settings['smtp_username'] : 'Not set'; ?></p>
                    <?php endif; ?>
                </div>

                <h3>How to Fix Email Delivery Issues</h3>
                <p>To resolve the "We do not relay non-local mail" error, you need to configure your application to use an external SMTP server instead of the local mail server.</p>

                <div class="setup-steps">
                    <h4>Option 1: Use Gmail SMTP (Recommended for Testing)</h4>
                    <ol>
                        <li>Go to <a href="settings.php" target="_blank">Email Settings</a></li>
                        <li>Enable the "Use SMTP" option</li>
                        <li>Set the following SMTP settings:
                            <ul>
                                <li><strong>SMTP Host:</strong> smtp.gmail.com</li>
                                <li><strong>SMTP Port:</strong> 587</li>
                                <li><strong>SMTP Security:</strong> TLS</li>
                                <li><strong>SMTP Username:</strong> <EMAIL> (use your actual Gmail address)</li>
                                <li><strong>SMTP Password:</strong> your Gmail password or app password</li>
                            </ul>
                        </li>
                        <li>Important: For Gmail, you may need to:
                            <ul>
                                <li>Enable "Less secure app access" in your Google account settings, OR</li>
                                <li>Create an "App Password" if you have 2-factor authentication enabled</li>
                                <li>Visit <a href="https://myaccount.google.com/security" target="_blank">Google Account Security</a> to manage these settings</li>
                            </ul>
                        </li>
                    </ol>

                    <h4>Option 2: Use Your Web Host's SMTP Server</h4>
                    <ol>
                        <li>Contact your web hosting provider for their SMTP server details</li>
                        <li>Go to <a href="settings.php" target="_blank">Email Settings</a></li>
                        <li>Enable the "Use SMTP" option</li>
                        <li>Enter the SMTP details provided by your hosting company</li>
                        <li>Common hosting SMTP settings:
                            <ul>
                                <li><strong>SMTP Host:</strong> mail.yourdomain.com or smtp.yourdomain.com</li>
                                <li><strong>SMTP Port:</strong> 587 or 465</li>
                                <li><strong>SMTP Security:</strong> TLS or SSL (use TLS for port 587, SSL for port 465)</li>
                                <li><strong>SMTP Username:</strong> Often your email address or username</li>
                                <li><strong>SMTP Password:</strong> Your email account password</li>
                            </ul>
                        </li>
                    </ol>

                    <h4>Option 3: Use a Transactional Email Service</h4>
                    <p>For production environments, consider using a dedicated email service:</p>
                    <ul>
                        <li><a href="https://sendgrid.com/" target="_blank">SendGrid</a> - Offers 100 free emails per day</li>
                        <li><a href="https://www.mailgun.com/" target="_blank">Mailgun</a> - Offers 5,000 free emails per month for 3 months</li>
                        <li><a href="https://www.sendinblue.com/" target="_blank">Sendinblue</a> - Offers 300 free emails per day</li>
                        <li><a href="https://postmarkapp.com/" target="_blank">Postmark</a> - Known for excellent deliverability</li>
                    </ul>
                    <p>These services provide SMTP credentials you can use in your settings.</p>
                </div>

                <h3>Testing Your SMTP Configuration</h3>
                <p>After configuring your SMTP settings:</p>
                <ol>
                    <li>Save your settings</li>
                    <li>Click the "Test Email" button in the Email Settings section</li>
                    <li>Check if you receive the test email</li>
                    <li>If you still encounter issues, check the error message and adjust your settings accordingly</li>
                </ol>

                <div class="form-actions">
                    <a href="settings.php" class="admin-btn primary"><i class="fas fa-cog"></i> Go to Email Settings</a>
                    <a href="test_phpmailer.php" class="admin-btn highlight"><i class="fas fa-paper-plane"></i> Test Email Configuration</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.setup-steps {
    margin: 20px 0;
}
.setup-steps h4 {
    margin-top: 25px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}
.setup-steps ol, .setup-steps ul {
    margin-left: 20px;
}
.setup-steps li {
    margin-bottom: 10px;
}
.setup-steps ul li {
    margin-bottom: 5px;
}
.settings-info {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}
.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}
</style>

<?php include 'includes/footer.php'; ?>
