<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Response</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .response-box {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        button {
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            background: #e0b929;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🔍 Response Debugger</h1>
    <p>This tool shows exactly what the server returns, including any non-JSON content.</p>

    <form id="debug-form">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="Debug User">
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message">Debug test message</textarea>
        </div>
        <input type="hidden" name="source" value="Response Debugger">
        
        <button type="submit">🚀 Send Request</button>
        <button type="button" onclick="clearResults()">🗑️ Clear Results</button>
    </form>

    <h2>📊 Response Analysis</h2>
    
    <h3>Raw Response:</h3>
    <div id="raw-response" class="response-box">No response yet...</div>
    
    <h3>Response Headers:</h3>
    <div id="response-headers" class="response-box">No headers yet...</div>
    
    <h3>Response Status:</h3>
    <div id="response-status" class="response-box">No status yet...</div>
    
    <h3>Character Analysis:</h3>
    <div id="char-analysis" class="response-box">No analysis yet...</div>
    
    <h3>JSON Parse Attempt:</h3>
    <div id="json-result" class="response-box">No JSON attempt yet...</div>

    <script>
        document.getElementById('debug-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            console.log('Starting debug request...');
            
            const formData = new FormData(this);
            
            fetch('process-contact.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response received:', response);
                
                // Show status
                document.getElementById('response-status').textContent = 
                    `Status: ${response.status} ${response.statusText}\n` +
                    `OK: ${response.ok}\n` +
                    `Type: ${response.type}\n` +
                    `URL: ${response.url}`;
                
                // Show headers
                let headersText = '';
                for (let [key, value] of response.headers.entries()) {
                    headersText += `${key}: ${value}\n`;
                }
                document.getElementById('response-headers').textContent = headersText || 'No headers';
                
                // Get response text
                return response.text();
            })
            .then(text => {
                console.log('Raw response text:', text);
                
                // Show raw response
                document.getElementById('raw-response').textContent = text;
                
                // Character analysis
                let analysis = `Length: ${text.length} characters\n`;
                analysis += `First 10 chars: ${JSON.stringify(text.substring(0, 10))}\n`;
                analysis += `Last 10 chars: ${JSON.stringify(text.substring(text.length - 10))}\n`;
                
                // Check for common issues
                if (text.startsWith('<')) {
                    analysis += `⚠️ Starts with HTML tag\n`;
                }
                if (text.includes('<!DOCTYPE')) {
                    analysis += `⚠️ Contains DOCTYPE declaration\n`;
                }
                if (text.includes('Warning:') || text.includes('Notice:')) {
                    analysis += `⚠️ Contains PHP warnings/notices\n`;
                }
                if (text.includes('Fatal error:')) {
                    analysis += `⚠️ Contains PHP fatal error\n`;
                }
                
                // Check for whitespace issues
                if (text.startsWith(' ') || text.startsWith('\n') || text.startsWith('\t')) {
                    analysis += `⚠️ Starts with whitespace\n`;
                }
                if (text.endsWith(' ') || text.endsWith('\n') || text.endsWith('\t')) {
                    analysis += `⚠️ Ends with whitespace\n`;
                }
                
                document.getElementById('char-analysis').textContent = analysis;
                
                // Try to parse JSON
                const jsonResult = document.getElementById('json-result');
                try {
                    const parsed = JSON.parse(text);
                    jsonResult.textContent = `✅ Valid JSON:\n${JSON.stringify(parsed, null, 2)}`;
                    jsonResult.className = 'response-box success';
                } catch (error) {
                    jsonResult.textContent = `❌ JSON Parse Error:\n${error.message}\n\nFirst 200 chars of response:\n${JSON.stringify(text.substring(0, 200))}`;
                    jsonResult.className = 'response-box error';
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('json-result').textContent = `❌ Fetch Error:\n${error.message}`;
                document.getElementById('json-result').className = 'response-box error';
            });
        });
        
        function clearResults() {
            document.getElementById('raw-response').textContent = 'No response yet...';
            document.getElementById('response-headers').textContent = 'No headers yet...';
            document.getElementById('response-status').textContent = 'No status yet...';
            document.getElementById('char-analysis').textContent = 'No analysis yet...';
            document.getElementById('json-result').textContent = 'No JSON attempt yet...';
            document.getElementById('json-result').className = 'response-box';
        }
    </script>
</body>
</html>
