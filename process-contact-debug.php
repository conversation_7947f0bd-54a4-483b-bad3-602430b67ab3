<?php
/**
 * Debug Version of Contact Form Handler
 * This version adds extensive debugging to identify the exact failure point
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set up error handler for fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        $error_msg = "FATAL ERROR in process-contact-debug.php: " . print_r($error, true);
        error_log($error_msg);

        // If this is an AJAX request, return JSON error
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if (!headers_sent()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Debug: Fatal error at line ' . $error['line'] . ' in ' . basename($error['file']) . ': ' . $error['message']
                ]);
            }
        }
    }
});

// Log the start
error_log("=== DEBUG CONTACT FORM PROCESSING STARTED ===");

try {
    error_log("DEBUG STEP 1: Basic setup");

    // Default response
    $response = [
        'success' => false,
        'message' => 'An error occurred while processing your request.'
    ];

    error_log("DEBUG STEP 2: Starting session");
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    error_log("DEBUG STEP 3: Checking if config file exists");
    if (!file_exists('admin/config.php')) {
        throw new Exception('Config file not found');
    }

    error_log("DEBUG STEP 4: Including config");
    require_once 'admin/config.php';

    error_log("DEBUG STEP 5: Checking database connection");
    if (!isset($conn) || !$conn) {
        throw new Exception('Database connection not established');
    }

    error_log("DEBUG STEP 6: Checking if email functions file exists");
    if (!file_exists('admin/includes/email-functions.php')) {
        throw new Exception('Email functions file not found');
    }

    error_log("DEBUG STEP 7: Including email functions");
    require_once 'admin/includes/email-functions.php';

    error_log("DEBUG STEP 8: Checking if send_email function exists");
    if (!function_exists('send_email')) {
        throw new Exception('send_email function not found');
    }

    error_log("DEBUG STEP 9: Processing POST request");
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        error_log("DEBUG STEP 10: Getting form data");

        // Use a simple sanitize function to avoid conflicts
        function debug_sanitize($input) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }

        $name = isset($_POST['name']) ? debug_sanitize($_POST['name']) : '';
        $email = isset($_POST['email']) ? debug_sanitize($_POST['email']) : '';
        $phone = isset($_POST['phone']) ? debug_sanitize($_POST['phone']) : '';
        $message = isset($_POST['message']) ? debug_sanitize($_POST['message']) : '';
        $source = isset($_POST['source']) ? debug_sanitize($_POST['source']) : 'Debug Test';

        error_log("DEBUG STEP 11: Form data - Name: $name, Email: $email");

        // Basic validation
        if (empty($name) || empty($email) || empty($message)) {
            $response['message'] = 'Please fill in all required fields.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $response['message'] = 'Please enter a valid email address.';
        } else {
            error_log("DEBUG STEP 12: Validation passed, getting email settings");

            try {
                $settings = get_email_settings();
                error_log("DEBUG STEP 13: Email settings retrieved");

                // Determine recipient
                $recipient = get_setting('admin_email', '');
                if (empty($recipient)) {
                    $recipient = $settings['from_email'];
                }
                if (empty($recipient)) {
                    $recipient = '<EMAIL>';
                }

                error_log("DEBUG STEP 14: Recipient determined: $recipient");

                // Create email content
                $subject = "Debug Contact Form Submission from " . $name;
                $email_content = "Debug Contact Form Submission\n\n";
                $email_content .= "Name: " . $name . "\n";
                $email_content .= "Email: " . $email . "\n";
                $email_content .= "Phone: " . $phone . "\n";
                $email_content .= "Message: " . $message . "\n";
                $email_content .= "Source: " . $source . "\n";
                $email_content .= "Date: " . date('Y-m-d H:i:s') . "\n";

                error_log("DEBUG STEP 15: Attempting to send email");

                $email_result = send_email($recipient, $subject, $email_content);

                error_log("DEBUG STEP 16: Email result: " . ($email_result ? 'SUCCESS' : 'FAILED'));

                if ($email_result) {
                    $response = [
                        'success' => true,
                        'message' => 'Debug: Email sent successfully!'
                    ];
                } else {
                    $response['message'] = 'Debug: Failed to send email';
                }

            } catch (Exception $e) {
                error_log("DEBUG ERROR in email processing: " . $e->getMessage());
                $response['message'] = 'Debug: Email processing error - ' . $e->getMessage();
            }
        }
    } else {
        $response['message'] = 'Debug: Not a POST request';
    }

    error_log("DEBUG STEP 17: Preparing response");

} catch (Exception $e) {
    error_log("DEBUG EXCEPTION: " . $e->getMessage());
    $response = [
        'success' => false,
        'message' => 'Debug Exception: ' . $e->getMessage()
    ];
} catch (Error $e) {
    error_log("DEBUG FATAL ERROR: " . $e->getMessage());
    $response = [
        'success' => false,
        'message' => 'Debug Fatal Error: ' . $e->getMessage()
    ];
}

// Return JSON response for AJAX requests
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    error_log("DEBUG STEP 18: Returning AJAX response");

    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    error_log("DEBUG FINAL RESPONSE: " . json_encode($response));
    echo json_encode($response);
    exit;
}

// For non-AJAX requests
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Contact Form</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .message { padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        form { margin: 20px 0; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; }
        button { background: #f1ca2f; color: #333; padding: 10px 20px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🐛 Debug Contact Form</h1>

    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="message <?php echo $response['success'] ? 'success' : 'error'; ?>">
            <?php echo htmlspecialchars($response['message']); ?>
        </div>
    <?php endif; ?>

    <form method="post">
        <p>
            <label>Name *</label>
            <input type="text" name="name" value="Debug User" required>
        </p>
        <p>
            <label>Email *</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </p>
        <p>
            <label>Phone</label>
            <input type="tel" name="phone" value="************">
        </p>
        <p>
            <label>Message *</label>
            <textarea name="message" required>This is a debug test message.</textarea>
        </p>
        <input type="hidden" name="source" value="Debug Form">
        <p>
            <button type="submit">Send Debug Message</button>
        </p>
    </form>

    <h2>🧪 AJAX Test</h2>
    <button onclick="testAjax()">Test AJAX Submission</button>
    <div id="ajax-result"></div>

    <script>
    function testAjax() {
        const formData = new FormData();
        formData.append('name', 'AJAX Debug User');
        formData.append('email', '<EMAIL>');
        formData.append('phone', '************');
        formData.append('message', 'This is an AJAX debug test message');
        formData.append('source', 'AJAX Debug Test');

        fetch('process-contact-debug.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.text().then(text => {
                console.log('Raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error('Invalid JSON: ' + text);
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);
            document.getElementById('ajax-result').innerHTML =
                '<div class="message ' + (data.success ? 'success' : 'error') + '">' +
                'AJAX Result: ' + data.message + '</div>';
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('ajax-result').innerHTML =
                '<div class="message error">AJAX Error: ' + error.message + '</div>';
        });
    }
    </script>
</body>
</html>
