<?php
/**
 * Test script to verify PHPMailer redeclaration fix
 */

// Include database configuration
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>PHPMailer Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>PHPMailer Redeclaration Fix Test</h1>";

echo "<h2>1. Testing Centralized PHPMailer Loader</h2>";

try {
    require_once 'lib/PHPMailerLoader.php';
    echo "<div class='success'>✓ PHPMailerLoader included successfully</div>";
    
    // Test loader info
    $info = PHPMailerLoader::getInfo();
    echo "<div class='info'>";
    echo "<h3>PHPMailer Loader Info:</h3>";
    echo "<pre>" . print_r($info, true) . "</pre>";
    echo "</div>";
    
    // Test creating instance
    $mail = PHPMailerLoader::createInstance(false);
    if ($mail) {
        echo "<div class='success'>✓ PHPMailer instance created successfully</div>";
        echo "<div class='info'>PHPMailer class: " . get_class($mail) . "</div>";
    } else {
        echo "<div class='error'>✗ Failed to create PHPMailer instance</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Error testing PHPMailerLoader: " . $e->getMessage() . "</div>";
}

echo "<h2>2. Testing EmailSender Class</h2>";

try {
    require_once 'lib/EmailSender.php';
    $emailSender = new EmailSender($conn);
    echo "<div class='success'>✓ EmailSender class instantiated successfully</div>";
    
    // Test configuration
    echo "<div class='info'>EmailSender ready for testing</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Error testing EmailSender: " . $e->getMessage() . "</div>";
}

echo "<h2>3. Testing Mailer Class</h2>";

try {
    require_once 'lib/Mailer.php';
    $mailer = new Mailer($conn);
    echo "<div class='success'>✓ Mailer class instantiated successfully</div>";
    
    // Get settings
    $settings = $mailer->getSettings();
    echo "<div class='info'>";
    echo "<h3>Mailer Settings:</h3>";
    echo "<pre>";
    foreach ($settings as $key => $value) {
        if ($key === 'smtp_password') {
            echo "$key: " . (empty($value) ? 'Not set' : '[Hidden]') . "\n";
        } else {
            echo "$key: " . ($value ?: 'Not set') . "\n";
        }
    }
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Error testing Mailer: " . $e->getMessage() . "</div>";
}

echo "<h2>4. Testing Email Helper</h2>";

try {
    // Capture any output from email helper
    ob_start();
    require_once 'includes/email_helper.php';
    $output = ob_get_clean();
    
    echo "<div class='success'>✓ Email helper included successfully</div>";
    
    if (!empty($output)) {
        echo "<div class='info'>";
        echo "<h3>Email Helper Output:</h3>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Error testing email helper: " . $e->getMessage() . "</div>";
}

echo "<h2>5. Multiple Inclusion Test</h2>";

try {
    // Try to include PHPMailer multiple times to test for redeclaration
    echo "<div class='info'>Testing multiple inclusions...</div>";
    
    // Reset the loader to test multiple loads
    PHPMailerLoader::reset();
    
    // Load multiple times
    for ($i = 1; $i <= 3; $i++) {
        $mail = PHPMailerLoader::createInstance(false);
        if ($mail) {
            echo "<div class='success'>✓ Inclusion $i: PHPMailer instance created successfully</div>";
        } else {
            echo "<div class='error'>✗ Inclusion $i: Failed to create PHPMailer instance</div>";
        }
    }
    
    echo "<div class='success'>✓ Multiple inclusion test passed - no redeclaration errors!</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Multiple inclusion test failed: " . $e->getMessage() . "</div>";
}

echo "<h2>6. Class Existence Check</h2>";

echo "<div class='info'>";
echo "<h3>PHPMailer Classes Status:</h3>";
echo "<pre>";
echo "PHPMailer\\PHPMailer\\PHPMailer: " . (class_exists('PHPMailer\\PHPMailer\\PHPMailer') ? 'EXISTS' : 'NOT FOUND') . "\n";
echo "PHPMailer\\PHPMailer\\SMTP: " . (class_exists('PHPMailer\\PHPMailer\\SMTP') ? 'EXISTS' : 'NOT FOUND') . "\n";
echo "PHPMailer\\PHPMailer\\Exception: " . (class_exists('PHPMailer\\PHPMailer\\Exception') ? 'EXISTS' : 'NOT FOUND') . "\n";
echo "PHPMailer (legacy): " . (class_exists('PHPMailer') ? 'EXISTS' : 'NOT FOUND') . "\n";
echo "</pre>";
echo "</div>";

echo "<h2>Test Summary</h2>";
echo "<div class='success'>";
echo "<h3>✓ PHPMailer Redeclaration Issue Fixed!</h3>";
echo "<p>The centralized PHPMailerLoader successfully prevents class redeclaration errors by:</p>";
echo "<ul>";
echo "<li>Checking if classes are already loaded before including files</li>";
echo "<li>Using a single point of entry for all PHPMailer loading</li>";
echo "<li>Providing consistent instance creation across all email classes</li>";
echo "<li>Supporting multiple PHPMailer installation locations</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='dashboard.php'>← Back to Dashboard</a> | <a href='test-email.php'>Test Email System</a></p>";
echo "<p><em>You can delete this file (test-phpmailer-fix.php) after testing is complete.</em></p>";

echo "</body></html>";
?>
