/**
 * Global Alert Auto-Dismissal Script
 * 
 * This script provides automatic dismissal functionality for all alert messages
 * across the admin panel, ensuring consistent behavior.
 */

(function() {
    'use strict';

    /**
     * Initialize alert auto-dismissal functionality
     */
    function initAlertAutoDismiss() {
        // Find all alert elements
        const alerts = document.querySelectorAll('.alert, .admin-alert');
        
        alerts.forEach(function(alert) {
            // Skip alerts that already have dismissal set up
            if (alert.hasAttribute('data-auto-dismiss-initialized')) {
                return;
            }
            
            // Mark as initialized
            alert.setAttribute('data-auto-dismiss-initialized', 'true');
            
            // Add auto-dismiss class for animation
            alert.classList.add('auto-dismiss');
            
            // Set up auto-dismissal after 5 seconds
            setTimeout(function() {
                dismissAlert(alert);
            }, 5000);
            
            // Add manual dismiss functionality if not already present
            addManualDismiss(alert);
        });
    }

    /**
     * Dismiss an alert with animation
     * @param {Element} alert - The alert element to dismiss
     */
    function dismissAlert(alert) {
        if (!alert || alert.style.display === 'none') {
            return;
        }

        // Start fade out animation
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        alert.style.transition = 'all 0.5s ease-out';
        
        // Remove from DOM after animation
        setTimeout(function() {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 500);
    }

    /**
     * Add manual dismiss functionality to an alert
     * @param {Element} alert - The alert element
     */
    function addManualDismiss(alert) {
        // Check if alert already has a dismiss button
        if (alert.querySelector('.alert-dismiss, .close')) {
            return;
        }

        // Create dismiss button
        const dismissBtn = document.createElement('button');
        dismissBtn.type = 'button';
        dismissBtn.className = 'alert-dismiss';
        dismissBtn.innerHTML = '<i class="fas fa-times"></i>';
        dismissBtn.setAttribute('aria-label', 'Close');
        
        // Add click handler
        dismissBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dismissAlert(alert);
        });
        
        // Add to alert
        alert.style.position = 'relative';
        alert.appendChild(dismissBtn);
        alert.classList.add('alert-dismissible');
    }

    /**
     * Create and show a new alert
     * @param {string} message - The alert message
     * @param {string} type - The alert type (success, danger, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds (default: 5000)
     */
    function showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = getOrCreateAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} auto-dismiss`;
        alert.innerHTML = `
            <i class="fas fa-${getIconForType(type)}"></i>
            ${message}
        `;
        
        alertContainer.appendChild(alert);
        
        // Initialize auto-dismiss for the new alert
        alert.setAttribute('data-auto-dismiss-initialized', 'true');
        addManualDismiss(alert);
        
        // Auto-dismiss after specified duration
        setTimeout(function() {
            dismissAlert(alert);
        }, duration);
        
        return alert;
    }

    /**
     * Get or create alert container
     * @returns {Element} The alert container element
     */
    function getOrCreateAlertContainer() {
        let container = document.querySelector('.alert-container');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'alert-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
        
        return container;
    }

    /**
     * Get icon class for alert type
     * @param {string} type - The alert type
     * @returns {string} The icon class
     */
    function getIconForType(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle',
            primary: 'info-circle'
        };
        
        return icons[type] || 'info-circle';
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAlertAutoDismiss);
    } else {
        initAlertAutoDismiss();
    }

    // Re-initialize when new content is added (for dynamic content)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node is an alert
                        if (node.classList && (node.classList.contains('alert') || node.classList.contains('admin-alert'))) {
                            if (!node.hasAttribute('data-auto-dismiss-initialized')) {
                                node.setAttribute('data-auto-dismiss-initialized', 'true');
                                node.classList.add('auto-dismiss');
                                addManualDismiss(node);
                                setTimeout(function() {
                                    dismissAlert(node);
                                }, 5000);
                            }
                        }
                        
                        // Check for alerts within the added node
                        const alerts = node.querySelectorAll && node.querySelectorAll('.alert, .admin-alert');
                        if (alerts) {
                            alerts.forEach(function(alert) {
                                if (!alert.hasAttribute('data-auto-dismiss-initialized')) {
                                    alert.setAttribute('data-auto-dismiss-initialized', 'true');
                                    alert.classList.add('auto-dismiss');
                                    addManualDismiss(alert);
                                    setTimeout(function() {
                                        dismissAlert(alert);
                                    }, 5000);
                                }
                            });
                        }
                    }
                });
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Expose global functions
    window.AlertManager = {
        show: showAlert,
        dismiss: dismissAlert,
        init: initAlertAutoDismiss
    };

})();
