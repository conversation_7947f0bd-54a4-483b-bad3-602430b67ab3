<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

$error = '';
$success = '';

// Set page title
$page_title = "Create News";

// Add CSS for the news page
$extra_css = '<link rel="stylesheet" href="assets/css/pages/news_editor.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/pages/create_news.css?v=' . time() . '">';

// Check if categories table exists
$categories_table_check = $conn->query("SHOW TABLES LIKE 'categories'");
if ($categories_table_check->num_rows == 0) {
    // Create categories table
    $create_categories_table = "CREATE TABLE categories (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_categories_table);

    // Add some default categories
    $default_categories = [
        ['name' => 'Technology', 'slug' => 'technology'],
        ['name' => 'Business', 'slug' => 'business'],
        ['name' => 'Cloud', 'slug' => 'cloud'],
        ['name' => 'Security', 'slug' => 'security']
    ];

    foreach ($default_categories as $category) {
        $name = $category['name'];
        $slug = $category['slug'];
        $insert_category = "INSERT INTO categories (name, slug) VALUES ('$name', '$slug')";
        $conn->query($insert_category);
    }
}

// Check if news table exists
$news_table_check = $conn->query("SHOW TABLES LIKE 'news'");
if ($news_table_check->num_rows == 0) {
    // Create news table
    $create_news_table = "CREATE TABLE news (
        id INT(11) NOT NULL AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        slug VARCHAR(255) DEFAULT NULL,
        featured_image VARCHAR(255) DEFAULT NULL,
        category_id INT(11) DEFAULT NULL,
        author_id INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug),
        KEY idx_author_id (author_id),
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_news_table);
}

// Get categories
$categories_sql = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_sql);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get CSRF token if available
    $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    // Verify CSRF token if provided, otherwise proceed (for backward compatibility)
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error = "Security validation failed. Please try again.";
        error_log("CSRF token validation failed during news creation");
    } else {
        $title = sanitize($_POST['title']);
        $content = $_POST['content']; // Don't sanitize HTML content
        $slug = sanitize($_POST['slug']);
        $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;

        // If slug is empty, generate it from the title
        if (empty($slug)) {
            $slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9-]+/', '-', $title)));
        }

        // Validate input
        if (empty($title) || empty($content)) {
            $error = "Please fill in all required fields";
        } else {
            // Handle image upload
            $target_dir = "../images/news/";

            // Check if directory exists, if not create it
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }

            // Make sure the directory is writable
            if (!is_writable($target_dir)) {
                chmod($target_dir, 0777);
            }

            $file_name = basename($_FILES["image"]["name"]);
            $target_file = $target_dir . $file_name;
            $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
            $uploadOk = 1;

            // Detailed error checking
            if(!isset($_FILES["image"]) || !isset($_FILES["image"]["name"]) || empty($_FILES["image"]["name"])) {
                $error = "Please select an image file.";
                $uploadOk = 0;
            }
            elseif($_FILES["image"]["error"] > 0) {
                $error = "File upload error: " . $_FILES["image"]["error"];
                $uploadOk = 0;
            }
            // Check if image file is a actual image
            elseif ($_FILES["image"]["tmp_name"] == "" || !file_exists($_FILES["image"]["tmp_name"])) {
                $error = "No file was uploaded or temporary file doesn't exist.";
                $uploadOk = 0;
            }
            else {
                $check = getimagesize($_FILES["image"]["tmp_name"]);
                if ($check === false) {
                    $error = "File is not an image.";
                    $uploadOk = 0;
                }
                // Check file size (limit to 5MB)
                elseif ($_FILES["image"]["size"] > 5000000) {
                    $error = "Sorry, your file is too large.";
                    $uploadOk = 0;
                }
                // Allow certain file formats
                elseif ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif") {
                    $error = "Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
                    $uploadOk = 0;
                }
            }

            // If everything is ok, try to upload file
            if ($uploadOk == 1) {
                // Generate unique filename to prevent overwriting
                $new_filename = uniqid() . '.' . $imageFileType;
                $target_file = $target_dir . $new_filename;

                if (move_uploaded_file($_FILES["image"]["tmp_name"], $target_file)) {
                    // File uploaded successfully, now save to database using prepared statement
                    $author_id = $_SESSION['user_id']; // Get the current logged-in user ID
                    $sql = "INSERT INTO news (title, content, featured_image, slug, category_id, author_id) VALUES (?, ?, ?, ?, ?, ?)";

                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        $error = "Failed to prepare statement: " . $conn->error;
                    } else {
                        $stmt->bind_param("ssssii", $title, $content, $new_filename, $slug, $category_id, $author_id);

                        if ($stmt->execute()) {
                            $success = "News post created successfully!";

                            // Update sitemap
                            if (function_exists('afterNewsOperation')) {
                                afterNewsOperation();
                            }

                            // Clear form data
                            $title = '';
                            $content = '';
                            $slug = '';
                            $category_id = 0;
                        } else {
                            $error = "Database error: " . $stmt->error;
                        }
                    }
                } else {
                    $error = "Sorry, there was an error uploading your file. Error code: " . $_FILES["image"]["error"] .
                             ". Target path: " . $target_file .
                             ". Is writable: " . (is_writable($target_dir) ? 'Yes' : 'No');
                }
            }
        }
    }
}
?>
<?php
// Add page-specific class to body
$body_class = 'page-create_news';
include 'includes/header.php';
?>

    <div class="admin-container">
        <?php
        // Set up variables for enhanced header
        $page_icon = 'fas fa-plus-circle';
        $page_subtitle = 'Create a new article for your website';
        $back_link = ['url' => 'all_news.php', 'text' => 'Back to News'];

        // Include the content header
        include 'includes/content-header.php';
        ?>

        <?php if (!empty($error) || !empty($success)): ?>
            <div class="admin-alerts-container">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <form class="admin-form news-editor-form" method="post" action="" enctype="multipart/form-data">
            <!-- CSRF Protection -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

            <div class="admin-content-grid">
                <!-- Main Content Area -->
                <div class="admin-content-main">
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-heading"></i> Post Details</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="title" class="form-label">Title <span class="required">*</span></label>
                                <input type="text" id="title" name="title" class="form-control" value="<?php echo isset($title) ? htmlspecialchars($title) : ''; ?>" placeholder="Enter news title" required>
                            </div>

                            <div class="form-group">
                                <label for="slug" class="form-label">Slug (for SEO-friendly URLs)</label>
                                <div class="input-group">
                                    <span class="input-group-text">/news/</span>
                                    <input type="text" id="slug" name="slug" class="form-control" value="<?php echo isset($slug) ? htmlspecialchars($slug) : ''; ?>" placeholder="my-news-title">
                                </div>
                                <div class="form-text">Use only letters, numbers, and hyphens. Leave empty to generate from title.</div>
                            </div>

                            <div class="form-group">
                                <label for="content" class="form-label">Content <span class="required">*</span></label>
                                <textarea id="content" name="content" class="wysiwyg-editor" placeholder="Enter news content" required><?php echo isset($content) ? htmlspecialchars($content) : ''; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="admin-content-sidebar">
                    <!-- Publish Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-paper-plane"></i> Publish</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-actions">
                                <button type="submit" class="admin-btn primary btn-lg btn-block">
                                    <i class="fas fa-save"></i> Create Post
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Category Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-folder"></i> Category</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="category_id" class="form-label">Select Category</label>
                                <select id="category_id" name="category_id" class="form-select">
                                    <option value="0">-- Select Category --</option>
                                    <?php if ($categories_result && $categories_result->num_rows > 0): ?>
                                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo (isset($category_id) && $category_id == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-image"></i> Featured Image</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="image" class="form-label">Select Image <span class="required">*</span></label>
                                <div class="image-preview-container">
                                    <div class="image-preview">
                                        <div class="image-preview-empty">
                                            <i class="fas fa-image"></i>
                                            <p>No image selected</p>
                                        </div>
                                    </div>
                                    <div class="custom-file-upload">
                                        <input type="file" id="image" name="image" class="custom-file-input" required>
                                        <label for="image" class="custom-file-label">Choose file</label>
                                    </div>
                                </div>
                                <div class="form-text">Recommended size: 800x450px. Max file size: 5MB.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Include our custom WYSIWYG editor -->
    <script src="js/editor.js?v=<?php echo time(); ?>"></script>
    <script src="js/editor-mobile.js?v=<?php echo time(); ?>"></script>

    <!-- Include image upload and form validation -->
    <script src="js/image-upload.js?v=<?php echo time(); ?>"></script>
    <script src="js/news-form-validation.js?v=<?php echo time(); ?>"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the editor
            new SimpleEditor('.wysiwyg-editor', {
                height: 400,
                placeholder: 'Enter news content here...'
            });

            // Auto-generate slug from title
            const titleInput = document.getElementById('title');
            const slugInput = document.getElementById('slug');

            // Function to generate slug
            function generateSlug(text) {
                return text
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-')      // Replace multiple hyphens with single hyphen
                    .trim();                  // Trim whitespace
            }

            // Generate slug on input (while typing)
            titleInput.addEventListener('input', function() {
                // Only generate slug if the slug field is empty or hasn't been manually edited
                if (slugInput.dataset.userEdited !== 'true') {
                    slugInput.value = generateSlug(this.value);
                }
            });

            // Also handle blur event for compatibility
            titleInput.addEventListener('blur', function() {
                // Only generate slug if the slug field is empty
                if (slugInput.value === '') {
                    slugInput.value = generateSlug(this.value);
                }
            });

            // Mark slug as user-edited when user types in it
            slugInput.addEventListener('input', function() {
                this.dataset.userEdited = 'true';
            });

            // If slug is cleared, reset the user-edited flag
            slugInput.addEventListener('blur', function() {
                if (this.value === '') {
                    this.dataset.userEdited = 'false';
                    // Generate from current title
                    if (titleInput.value !== '') {
                        this.value = generateSlug(titleInput.value);
                    }
                }
            });

            // Handle alert close buttons
            const alertCloseButtons = document.querySelectorAll('.admin-alert-close');
            alertCloseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const alert = this.closest('.admin-alert');
                    if (alert) {
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            alert.style.display = 'none';
                        }, 300);
                    }
                });
            });

            // Handle file input change to show file name
            const fileInput = document.getElementById('image');
            const fileLabel = document.querySelector('.custom-file-label');

            if (fileInput && fileLabel) {
                fileInput.addEventListener('change', function() {
                    if (this.files && this.files.length > 0) {
                        fileLabel.textContent = this.files[0].name;
                    } else {
                        fileLabel.textContent = 'Choose file';
                    }
                });
            }
        });
    </script>

<?php include 'includes/footer.php'; ?>
