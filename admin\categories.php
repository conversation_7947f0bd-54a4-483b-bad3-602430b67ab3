<?php
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage categories
if (!$permissions->hasPermission('manage_categories')) {
    $_SESSION['error_message'] = "You do not have permission to manage categories.";
    redirect('dashboard.php');
}

$error = '';
$success = '';

// Set page title
$page_title = "Category Management";

// Process form submission for adding a new category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $name = sanitize($_POST['name']);
    $description = isset($_POST['description']) ? sanitize($_POST['description']) : '';

    // Validate input
    if (empty($name)) {
        $error = "Category name is required";
    } else {
        // Check if category already exists
        $stmt = $conn->prepare("SELECT id FROM categories WHERE name = ?");
        $stmt->bind_param("s", $name);
        $stmt->execute();
        $check_result = $stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Category already exists";
        } else {
            // Generate slug from name
            $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $name));
            $slug = trim($slug, '-');

            // Insert new category
            $stmt = $conn->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
            $stmt->bind_param("sss", $name, $slug, $description);

            if ($stmt->execute()) {
                $success = "Category added successfully!";
            } else {
                $error = "Database error: " . $conn->error;
            }
        }
    }
}

// Process category deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $category_id = (int)$_GET['delete'];

    // Check if category is in use
    $stmt = $conn->prepare("SELECT id FROM news WHERE category_id = ?");
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $check_result = $stmt->get_result();

    if ($check_result->num_rows > 0) {
        $error = "Cannot delete category because it is assigned to one or more news posts";
    } else {
        // Delete category
        $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->bind_param("i", $category_id);

        if ($stmt->execute()) {
            $success = "Category deleted successfully!";
        } else {
            $error = "Database error: " . $conn->error;
        }
    }
}

// Process category edit
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit') {
    $category_id = (int)$_POST['category_id'];
    $name = sanitize($_POST['name']);
    $description = isset($_POST['description']) ? sanitize($_POST['description']) : '';

    // Validate input
    if (empty($name)) {
        $error = "Category name is required";
    } else {
        // Check if category already exists with this name (excluding current category)
        $stmt = $conn->prepare("SELECT id FROM categories WHERE name = ? AND id != ?");
        $stmt->bind_param("si", $name, $category_id);
        $stmt->execute();
        $check_result = $stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Another category with this name already exists";
        } else {
            // Generate slug from name
            $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $name));
            $slug = trim($slug, '-');

            // Update category
            $stmt = $conn->prepare("UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?");
            $stmt->bind_param("sssi", $name, $slug, $description, $category_id);

            if ($stmt->execute()) {
                $success = "Category updated successfully!";
            } else {
                $error = "Database error: " . $conn->error;
            }
        }
    }
}

// Check if categories table exists and has description column
$categories_table_check = $conn->query("SHOW TABLES LIKE 'categories'");
if ($categories_table_check->num_rows == 0) {
    // Create categories table
    $create_categories_table = "CREATE TABLE categories (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_categories_table);

    // Add some default categories
    $default_categories = [
        ['name' => 'Technology', 'slug' => 'technology', 'description' => 'Latest technology news and updates'],
        ['name' => 'Business', 'slug' => 'business', 'description' => 'Business insights and market trends'],
        ['name' => 'Cloud', 'slug' => 'cloud', 'description' => 'Cloud computing and services'],
        ['name' => 'Security', 'slug' => 'security', 'description' => 'Cybersecurity news and best practices']
    ];

    foreach ($default_categories as $category) {
        $name = $category['name'];
        $slug = $category['slug'];
        $description = $category['description'];
        $stmt = $conn->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $name, $slug, $description);
        $stmt->execute();
    }
} else {
    // Check if description column exists
    $description_column_check = $conn->query("SHOW COLUMNS FROM categories LIKE 'description'");
    if ($description_column_check->num_rows == 0) {
        // Add description column
        $conn->query("ALTER TABLE categories ADD COLUMN description TEXT AFTER slug");
    }
}

// Check if news table exists
$news_table_check = $conn->query("SHOW TABLES LIKE 'news'");
if ($news_table_check->num_rows == 0) {
    // Create news table
    $create_news_table = "CREATE TABLE news (
        id INT(11) NOT NULL AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        image VARCHAR(255) DEFAULT NULL,
        category_id INT(11) DEFAULT NULL,
        slug VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_news_table);
}

// Get all categories with post counts
$sql = "SELECT c.*, COUNT(n.id) as post_count
        FROM categories c
        LEFT JOIN news n ON c.id = n.category_id
        GROUP BY c.id
        ORDER BY c.name ASC";
$result = $conn->query($sql);

// Extra CSS for categories page
$extra_css = '<link rel="stylesheet" href="css/categories.css?v=' . time() . '">';
?>
<?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php
        // Count total categories
        $total_categories = $result->num_rows;

        // Count total posts
        $total_posts = 0;
        $result->data_seek(0); // Reset result pointer
        while ($row = $result->fetch_assoc()) {
            $total_posts += $row['post_count'];
        }
        $result->data_seek(0); // Reset result pointer again

        // Set up variables for content header
        $page_icon = 'fas fa-folder';
        $page_title = 'Categories';
        $page_subtitle = 'Manage content categories for your website';
        $stats_data = [
            ['icon' => 'fas fa-folder', 'text' => "Total Categories: $total_categories", 'highlight' => true],
            ['icon' => 'fas fa-newspaper', 'text' => "Total Posts: $total_posts", 'highlight' => false]
        ];

        // Remove primary action button from header

        // Include the content header
        include 'includes/content-header.php';
        ?>

        <?php if (!empty($error)): ?>
            <div class="admin-alert error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="admin-alert success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="categories-spacer"></div>

        <div class="admin-content-body">
            <?php if ($result->num_rows > 0): ?>
                <div class="admin-table-actions">
                    <div class="admin-table-filters">
                        <div class="admin-search-box">
                            <input type="text" id="categorySearch" placeholder="Search categories..." onkeyup="filterCategories()">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    <a href="category.php" class="admin-btn">
                        <i class="fas fa-plus-circle"></i> Add New Category
                    </a>
                </div>

                <div class="admin-table-responsive">
                    <table class="admin-table wp-style" id="categoriesTable">
                        <thead>
                            <tr>
                                <th class="name-column">Name</th>
                                <th class="slug-column">Slug</th>
                                <th class="description-column">Description</th>
                                <th class="count-column">Posts</th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch_assoc()): ?>
                                <tr data-name="<?php echo htmlspecialchars(strtolower($row['name'])); ?>">
                                    <td class="name-column" data-label="Name">
                                        <div class="category-name">
                                            <i class="fas fa-folder"></i>
                                            <strong><?php echo htmlspecialchars($row['name']); ?></strong>
                                        </div>
                                    </td>
                                    <td class="slug-column" data-label="Slug">
                                        <code><?php echo htmlspecialchars($row['slug']); ?></code>
                                    </td>
                                    <td class="description-column" data-label="Description">
                                        <?php echo !empty($row['description']) ? htmlspecialchars($row['description']) : '<em>No description</em>'; ?>
                                    </td>
                                    <td class="count-column" data-label="Posts">
                                        <span class="count-badge <?php echo $row['post_count'] == 0 ? 'empty' : ''; ?>">
                                            <?php echo $row['post_count']; ?>
                                        </span>
                                    </td>
                                    <td class="actions-column" data-label="Actions">
                                        <div class="row-actions">
                                            <a href="all_news.php?category=<?php echo $row['id']; ?>" class="admin-btn view-btn" title="View Posts">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="category.php?id=<?php echo $row['id']; ?>" class="admin-btn edit-btn">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($row['post_count'] == 0): ?>
                                                <a href="categories.php?delete=<?php echo $row['id']; ?>" class="admin-btn delete-btn" onclick="return confirm('Are you sure you want to delete this category?')">
                                                    <i class="fas fa-trash-alt"></i>
                                                </a>
                                            <?php else: ?>
                                                <button type="button" class="admin-btn delete-btn disabled" disabled title="Cannot delete category with posts">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>

                    <!-- No search results message -->
                    <div id="noSearchResults" class="no-search-results" style="display: none;">
                        <div class="empty-state-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="empty-state-title">No Categories Found</h3>
                        <p class="empty-state-text">No categories match your search criteria. Try a different search term.</p>
                    </div>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="empty-state-title">No Categories Found</h3>
                    <p class="empty-state-text">You haven't created any categories yet. Categories help organize your content.</p>
                    <a href="category.php" class="admin-btn">
                        <i class="fas fa-plus-circle"></i> Create Your First Category
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="admin-modal" id="addCategoryModal">
        <div class="admin-modal-dialog">
            <div class="admin-modal-content">
                <div class="admin-modal-header">
                    <h3><i class="fas fa-plus-circle"></i> Add New Category</h3>
                    <button type="button" class="admin-modal-close" data-dismiss="admin-modal">&times;</button>
                </div>
                <div class="admin-modal-body">
                    <form class="admin-modal-form" method="post" action="" id="addCategoryForm">
                        <input type="hidden" name="action" value="add">

                        <div class="form-group">
                            <label for="name">Category Name</label>
                            <input type="text" id="name" name="name" placeholder="Enter category name" required>
                            <p class="form-hint">The name is how it appears on your site.</p>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" placeholder="Enter category description" rows="4"></textarea>
                            <p class="form-hint">The description is not prominent by default; however, some themes may show it.</p>
                        </div>

                        <div class="form-group">
                            <label for="slug-preview">Slug Preview</label>
                            <div class="slug-preview" id="slug-preview">category-name</div>
                            <p class="form-hint">The "slug" is the URL-friendly version of the name. It is usually all lowercase and contains only letters, numbers, and hyphens.</p>
                        </div>
                    </form>
                </div>
                <div class="admin-modal-footer">
                    <button type="button" class="admin-modal-btn admin-modal-btn-secondary" data-dismiss="admin-modal">Cancel</button>
                    <button type="button" class="admin-modal-btn admin-modal-btn-primary" onclick="document.getElementById('addCategoryForm').submit();">
                        <i class="fas fa-plus-circle"></i> Add Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="admin-modal" id="editCategoryModal">
        <div class="admin-modal-dialog">
            <div class="admin-modal-content">
                <div class="admin-modal-header">
                    <h3><i class="fas fa-edit"></i> Edit Category</h3>
                    <button type="button" class="admin-modal-close" data-dismiss="admin-modal">&times;</button>
                </div>
                <div class="admin-modal-body">
                    <form class="admin-modal-form" method="post" action="" id="editCategoryForm">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="category_id" id="edit_category_id">

                        <div class="form-group">
                            <label for="edit_name">Category Name</label>
                            <input type="text" id="edit_name" name="name" required>
                            <p class="form-hint">The name is how it appears on your site.</p>
                        </div>

                        <div class="form-group">
                            <label for="edit_description">Description</label>
                            <textarea id="edit_description" name="description" rows="4"></textarea>
                            <p class="form-hint">The description is not prominent by default; however, some themes may show it.</p>
                        </div>

                        <div class="form-group">
                            <label for="edit-slug-preview">Slug Preview</label>
                            <div class="slug-preview" id="edit-slug-preview">category-name</div>
                            <p class="form-hint">The "slug" is the URL-friendly version of the name. It is usually all lowercase and contains only letters, numbers, and hyphens.</p>
                        </div>
                    </form>
                </div>
                <div class="admin-modal-footer">
                    <button type="button" class="admin-modal-btn admin-modal-btn-secondary" data-dismiss="admin-modal">Cancel</button>
                    <button type="button" class="admin-modal-btn admin-modal-btn-primary" onclick="document.getElementById('editCategoryForm').submit();">
                        <i class="fas fa-save"></i> Update Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
    /* Slug Preview Styling */
    .slug-preview {
        padding: 8px 12px;
        background-color: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-family: monospace;
        color: #555555;
        margin-bottom: 5px;
    }

    /* Table Styling */
    .admin-table .name-column {
        width: 20%;
    }

    .admin-table .slug-column {
        width: 15%;
    }

    .admin-table .description-column {
        width: 45%;
    }

    .admin-table .count-column {
        width: 10%;
        text-align: center;
    }

    .admin-table .actions-column {
        width: 10%;
        text-align: right;
    }

    .category-name {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .category-name i {
        color: #f1ca2f;
    }

    .count-badge {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #f1ca2f;
        color: #333;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
    }

    .count-badge.empty {
        background-color: #e0e0e0;
        color: #777;
    }

    .row-actions {
        display: flex;
        gap: 5px;
        justify-content: flex-end;
    }

    /* Search Box Styling */
    .admin-search-box {
        position: relative;
        max-width: 300px;
    }

    .admin-search-box input {
        width: 100%;
        padding: 8px 12px 8px 36px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .admin-search-box i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #777;
    }

    /* No Search Results Styling */
    .no-search-results {
        padding: 30px;
        text-align: center;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin-top: 20px;
    }

    .no-search-results .empty-state-icon {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
    }

    .no-search-results .empty-state-title {
        font-size: 18px;
        margin-bottom: 10px;
        color: #555;
    }

    .no-search-results .empty-state-text {
        color: #777;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Table Actions Styling */
    .admin-table-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    /* Modal loader */
    .modal-loader {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10;
    }

    .modal-loader i {
        font-size: 2rem;
        color: #f1ca2f;
    }

    /* Loading state for modal */
    .admin-modal.loading .admin-modal-content {
        position: relative;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
        .admin-table .slug-column,
        .admin-table .description-column {
            display: none;
        }

        .admin-table-actions {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .admin-search-box {
            max-width: 100%;
        }
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize modals
        initModals();

        // Initialize slug preview
        initSlugPreview();

        // Auto-dismiss alerts
        initAlertDismissal();

        // Handle data-action attributes
        initDataActionHandlers();
    });

    // Initialize data-action handlers
    function initDataActionHandlers() {
        const actionButtons = document.querySelectorAll('[data-action]');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const action = this.getAttribute('data-action');

                if (action === 'open-modal-addCategoryModal') {
                    openModal('addCategoryModal');
                }
            });
        });
    }

    // Initialize modal functionality
    function initModals() {
        // Open Add Category Modal from other buttons
        const addCategoryBtns = document.querySelectorAll('[data-toggle="admin-modal"][data-target="addCategoryModal"]');
        addCategoryBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                openModal('addCategoryModal');
            });
        });

        // Edit Category Buttons
        const editButtons = document.querySelectorAll('[data-target="editCategoryModal"]');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-id');

                // Show loading state
                const editModal = document.getElementById('editCategoryModal');
                if (editModal) {
                    editModal.classList.add('loading');

                    // Show the modal first, then load data
                    openModal('editCategoryModal');

                    // Add loading indicator
                    const modalContent = editModal.querySelector('.admin-modal-content');
                    if (modalContent) {
                        if (!document.getElementById('modal-loader')) {
                            const loader = document.createElement('div');
                            loader.id = 'modal-loader';
                            loader.className = 'modal-loader';
                            loader.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                            modalContent.appendChild(loader);
                        } else {
                            document.getElementById('modal-loader').style.display = 'flex';
                        }
                    }
                }

                // Fetch category data via AJAX
                fetch('ajax/get_category.php?id=' + categoryId)
                    .then(response => {
                        console.log('Response status:', response.status);
                        if (!response.ok) {
                            throw new Error('Network response was not ok: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Received data:', data);

                        if (data.success) {
                            // Populate form fields
                            document.getElementById('edit_category_id').value = data.category.id;
                            document.getElementById('edit_name').value = data.category.name;
                            document.getElementById('edit_description').value = data.category.description || '';
                            document.getElementById('edit-slug-preview').textContent = data.category.slug || generateSlug(data.category.name);
                        } else {
                            console.error('Error in data:', data.message);
                            alert('Error loading category data: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching category data:', error);
                        alert('Error loading category data. Please try again.');
                    })
                    .finally(() => {
                        // Remove loading state
                        if (editModal) {
                            editModal.classList.remove('loading');

                            // Hide loader
                            const loader = document.getElementById('modal-loader');
                            if (loader) {
                                loader.style.display = 'none';
                            }
                        }
                    });
            });
        });

        // Close modal buttons
        const closeButtons = document.querySelectorAll('[data-dismiss="admin-modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const modal = this.closest('.admin-modal');
                if (modal) {
                    modal.classList.remove('show');
                }
            });
        });

        // Close modal when clicking outside
        document.querySelectorAll('.admin-modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('show');
                }
            });
        });
    }

    // Open a modal by ID
    function openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
        }
    }

    // Initialize slug preview functionality
    function initSlugPreview() {
        const nameInput = document.getElementById('name');
        const slugPreview = document.getElementById('slug-preview');

        if (nameInput && slugPreview) {
            nameInput.addEventListener('input', function() {
                slugPreview.textContent = generateSlug(this.value);
            });
        }

        const editNameInput = document.getElementById('edit_name');
        const editSlugPreview = document.getElementById('edit-slug-preview');

        if (editNameInput && editSlugPreview) {
            editNameInput.addEventListener('input', function() {
                editSlugPreview.textContent = generateSlug(this.value);
            });
        }
    }

    // Initialize alert auto-dismissal
    function initAlertDismissal() {
        const alerts = document.querySelectorAll('.admin-alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });
    }

    // Function to generate slug from text
    function generateSlug(text) {
        if (!text) return 'category-name';

        return text
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }

    // Function to filter categories based on search input
    function filterCategories() {
        const searchInput = document.getElementById('categorySearch');
        const filter = searchInput.value.toLowerCase();
        const table = document.getElementById('categoriesTable');
        const rows = table.getElementsByTagName('tr');

        let hasVisibleRows = false;

        // Loop through all table rows except the header
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const nameData = row.getAttribute('data-name');

            if (nameData && nameData.includes(filter)) {
                row.style.display = '';
                hasVisibleRows = true;
            } else {
                row.style.display = 'none';
            }
        }

        // Show or hide "no results" message
        const noResults = document.getElementById('noSearchResults');
        if (noResults) {
            noResults.style.display = hasVisibleRows ? 'none' : 'block';
        }
    }
    </script>

<?php include 'includes/footer.php'; ?>
