<?php
session_start();
require_once 'config.php';
require_once 'includes/security.php';
require_once 'includes/helpers.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Initialize variables
$success_message = '';
$error_message = '';
$user_id = $_SESSION['user_id'];

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get user settings
$user_settings = [];
$settings_stmt = $conn->prepare("SELECT * FROM user_settings WHERE user_id = ?");
$settings_stmt->bind_param("i", $user_id);
$settings_stmt->execute();
$settings_result = $settings_stmt->get_result();

if ($settings_result->num_rows > 0) {
    $row = $settings_result->fetch_assoc();
    // Map database columns to settings array
    $user_settings = [
        'timezone' => $row['timezone'] ?? 'UTC',
        'language' => $row['language'] ?? 'en',
        'notifications_enabled' => $row['notifications_enabled'] ?? 1,
        'email_notifications' => $row['email_notifications'] ?? 1,
        'browser_notifications' => $row['browser_notifications'] ?? 1,
        'notification_sound' => $row['notification_sound'] ?? 1,
        'theme' => $row['theme'] ?? 'light',
        'items_per_page' => $row['items_per_page'] ?? 10,
        'date_format' => $row['date_format'] ?? 'Y-m-d',
        'time_format' => $row['time_format'] ?? 'H:i'
    ];
}

// Default settings
$default_settings = [
    'timezone' => 'UTC',
    'language' => 'en',
    'notifications_enabled' => 1,
    'email_notifications' => 1,
    'browser_notifications' => 1,
    'notification_sound' => 1,
    'theme' => 'light',
    'items_per_page' => 10,
    'date_format' => 'Y-m-d',
    'time_format' => 'H:i'
];

// Merge with user settings
$settings = array_merge($default_settings, $user_settings);

// Helper functions
function updateProfile($conn, $user_id, $post_data, $files) {
    $username = sanitize($post_data['username'] ?? '');
    $email = sanitize($post_data['email'] ?? '');

    if (empty($username) || empty($email)) {
        return "Username and email are required.";
    }

    // Check if username is already taken
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->bind_param("si", $username, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return "Username is already taken.";
    }

    // Check if email is already taken
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
    $stmt->bind_param("si", $email, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return "Email is already taken.";
    }

    // Handle profile image upload
    $profile_image = null;
    if (isset($files['profile_image']) && $files['profile_image']['error'] === UPLOAD_ERR_OK) {
        $upload_result = handleProfileImageUpload($files['profile_image']);
        if ($upload_result['success']) {
            $profile_image = $upload_result['filename'];
        } else {
            return $upload_result['error'];
        }
    }

    // Update user profile
    if ($profile_image) {
        $stmt = $conn->prepare("UPDATE users SET username = ?, email = ?, profile_image = ? WHERE id = ?");
        $stmt->bind_param("sssi", $username, $email, $profile_image, $user_id);
    } else {
        $stmt = $conn->prepare("UPDATE users SET username = ?, email = ? WHERE id = ?");
        $stmt->bind_param("ssi", $username, $email, $user_id);
    }

    if ($stmt->execute()) {
        $_SESSION['username'] = $username;
        return '';
    } else {
        return "Error updating profile: " . $conn->error;
    }
}

function changePassword($conn, $user_id, $post_data) {
    $current_password = $post_data['current_password'] ?? '';
    $new_password = $post_data['new_password'] ?? '';
    $confirm_password = $post_data['confirm_password'] ?? '';

    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        return "All password fields are required.";
    }

    if ($new_password !== $confirm_password) {
        return "New passwords do not match.";
    }

    if (strlen($new_password) < 8) {
        return "Password must be at least 8 characters long.";
    }

    // Verify current password
    $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if (!password_verify($current_password, $user['password'])) {
        return "Current password is incorrect.";
    }

    // Update password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $hashed_password, $user_id);

    if ($stmt->execute()) {
        return '';
    } else {
        return "Error updating password: " . $conn->error;
    }
}

function updatePreferences($conn, $user_id, $post_data) {
    $timezone = sanitize($post_data['timezone'] ?? 'UTC');
    $language = sanitize($post_data['language'] ?? 'en');
    $notifications_enabled = isset($post_data['notifications_enabled']) ? 1 : 0;
    $email_notifications = isset($post_data['email_notifications']) ? 1 : 0;
    $browser_notifications = isset($post_data['browser_notifications']) ? 1 : 0;
    $notification_sound = isset($post_data['notification_sound']) ? 1 : 0;
    $theme = sanitize($post_data['theme'] ?? 'light');
    $items_per_page = (int)($post_data['items_per_page'] ?? 10);
    $date_format = sanitize($post_data['date_format'] ?? 'Y-m-d');
    $time_format = sanitize($post_data['time_format'] ?? 'H:i');

    // Check if user settings record exists
    $check_stmt = $conn->prepare("SELECT id FROM user_settings WHERE user_id = ?");
    $check_stmt->bind_param("i", $user_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing record
        $stmt = $conn->prepare("UPDATE user_settings SET timezone = ?, language = ?, notifications_enabled = ?, email_notifications = ?, browser_notifications = ?, notification_sound = ?, theme = ?, items_per_page = ?, date_format = ?, time_format = ? WHERE user_id = ?");
        $stmt->bind_param("ssiiiissssi", $timezone, $language, $notifications_enabled, $email_notifications, $browser_notifications, $notification_sound, $theme, $items_per_page, $date_format, $time_format, $user_id);
    } else {
        // Insert new record
        $stmt = $conn->prepare("INSERT INTO user_settings (user_id, timezone, language, notifications_enabled, email_notifications, browser_notifications, notification_sound, theme, items_per_page, date_format, time_format) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issiiiisiss", $user_id, $timezone, $language, $notifications_enabled, $email_notifications, $browser_notifications, $notification_sound, $theme, $items_per_page, $date_format, $time_format);
    }

    if (!$stmt->execute()) {
        return "Error updating preferences: " . $conn->error;
    }

    return '';
}

function handleProfileImageUpload($file) {
    $upload_dir = '../uploads/profiles/';

    // Create directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Validate file
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'error' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.'];
    }

    if ($file['size'] > 2097152) { // 2MB
        return ['success' => false, 'error' => 'File size too large. Maximum size is 2MB.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('profile_') . '.' . $extension;
    $filepath = $upload_dir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'error' => 'Failed to upload file.'];
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_profile':
                $error_message = updateProfile($conn, $user_id, $_POST, $_FILES);
                if (empty($error_message)) {
                    $success_message = "Profile updated successfully!";
                    // Refresh user data
                    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->bind_param("i", $user_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $user = $result->fetch_assoc();
                }
                break;

            case 'change_password':
                $error_message = changePassword($conn, $user_id, $_POST);
                if (empty($error_message)) {
                    $success_message = "Password changed successfully!";
                }
                break;

            case 'update_preferences':
                $error_message = updatePreferences($conn, $user_id, $_POST);
                if (empty($error_message)) {
                    $success_message = "Preferences updated successfully!";
                    // Refresh settings
                    $settings_stmt = $conn->prepare("SELECT * FROM user_settings WHERE user_id = ?");
                    $settings_stmt->bind_param("i", $user_id);
                    $settings_stmt->execute();
                    $settings_result = $settings_stmt->get_result();
                    $user_settings = [];
                    if ($settings_result->num_rows > 0) {
                        $row = $settings_result->fetch_assoc();
                        $user_settings = [
                            'timezone' => $row['timezone'] ?? 'UTC',
                            'language' => $row['language'] ?? 'en',
                            'notifications_enabled' => $row['notifications_enabled'] ?? 1,
                            'email_notifications' => $row['email_notifications'] ?? 1,
                            'browser_notifications' => $row['browser_notifications'] ?? 1,
                            'notification_sound' => $row['notification_sound'] ?? 1,
                            'theme' => $row['theme'] ?? 'light',
                            'items_per_page' => $row['items_per_page'] ?? 10,
                            'date_format' => $row['date_format'] ?? 'Y-m-d',
                            'time_format' => $row['time_format'] ?? 'H:i'
                        ];
                    }
                    $settings = array_merge($default_settings, $user_settings);
                }
                break;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Admin Panel</title>
    <link rel="stylesheet" href="styles/admin_main.css">
    <link rel="stylesheet" href="styles/admin_profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-content-header">
                <div class="admin-content-title-group">
                    <h2 class="admin-content-title">
                        <i class="fas fa-user-circle"></i>
                        Profile Settings
                    </h2>
                    <p class="admin-content-subtitle">Manage your account information and preferences</p>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="profile-container">
                <!-- Profile Overview Card -->
                <div class="profile-overview-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <?php if (!empty($user['profile_image'])): ?>
                                <img src="../uploads/profiles/<?php echo htmlspecialchars($user['profile_image']); ?>" alt="Profile Picture" class="avatar-img">
                            <?php else: ?>
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="profile-info">
                            <h2 class="profile-name"><?php echo htmlspecialchars($user['username']); ?></h2>
                            <p class="profile-email"><?php echo htmlspecialchars($user['email']); ?></p>
                            <div class="profile-meta">
                                <span class="profile-role">
                                    <i class="fas fa-shield-alt"></i>
                                    Administrator
                                </span>
                                <span class="profile-joined">
                                    <i class="fas fa-calendar"></i>
                                    Joined <?php echo date('M Y', strtotime($user['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Tabs -->
                <div class="profile-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" data-tab="profile">
                            <i class="fas fa-user"></i>
                            Profile Information
                        </button>
                        <button class="tab-btn" data-tab="security">
                            <i class="fas fa-lock"></i>
                            Security
                        </button>
                        <button class="tab-btn" data-tab="preferences">
                            <i class="fas fa-cog"></i>
                            Preferences
                        </button>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Profile Information Tab -->
                        <div class="tab-pane active" id="profile">
                            <form method="post" enctype="multipart/form-data" class="profile-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="update_profile">

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-user"></i>
                                        Basic Information
                                    </h3>

                                    <div class="form-grid two-column">
                                        <div class="form-group">
                                            <label for="username">Username</label>
                                            <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="email">Email Address</label>
                                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-image"></i>
                                        Profile Picture
                                    </h3>

                                    <div class="form-group">
                                        <label for="profile_image">Upload New Picture</label>
                                        <input type="file" id="profile_image" name="profile_image" accept="image/*">
                                        <div class="form-help">JPG, PNG, or GIF. Maximum size: 2MB</div>
                                        <div id="image-preview" style="display: none; margin-top: 10px;">
                                            <img id="preview-img" src="" alt="Preview" style="max-width: 150px; max-height: 150px; border-radius: 8px;">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane" id="security">
                            <form method="post" class="profile-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="change_password">

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-lock"></i>
                                        Change Password
                                    </h3>

                                    <div class="form-group">
                                        <label for="current_password">Current Password</label>
                                        <input type="password" id="current_password" name="current_password" required>
                                    </div>

                                    <div class="form-grid two-column">
                                        <div class="form-group">
                                            <label for="new_password">New Password</label>
                                            <input type="password" id="new_password" name="new_password" required minlength="8">
                                            <div class="form-help">Minimum 8 characters</div>
                                        </div>

                                        <div class="form-group">
                                            <label for="confirm_password">Confirm New Password</label>
                                            <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-key"></i>
                                        Change Password
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Preferences Tab -->
                        <div class="tab-pane" id="preferences">
                            <form method="post" class="profile-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="update_preferences">

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-globe"></i>
                                        Regional Settings
                                    </h3>

                                    <div class="form-grid three-column">
                                        <div class="form-group">
                                            <label for="timezone">Timezone</label>
                                            <select id="timezone" name="timezone">
                                                <option value="UTC" <?php echo $settings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                                <option value="America/New_York" <?php echo $settings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                                <option value="America/Chicago" <?php echo $settings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                                <option value="America/Denver" <?php echo $settings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                                <option value="America/Los_Angeles" <?php echo $settings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                                <option value="Europe/London" <?php echo $settings['timezone'] === 'Europe/London' ? 'selected' : ''; ?>>London</option>
                                                <option value="Europe/Paris" <?php echo $settings['timezone'] === 'Europe/Paris' ? 'selected' : ''; ?>>Paris</option>
                                                <option value="Asia/Tokyo" <?php echo $settings['timezone'] === 'Asia/Tokyo' ? 'selected' : ''; ?>>Tokyo</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="language">Language</label>
                                            <select id="language" name="language">
                                                <option value="en" <?php echo $settings['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                                <option value="es" <?php echo $settings['language'] === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                                <option value="fr" <?php echo $settings['language'] === 'fr' ? 'selected' : ''; ?>>French</option>
                                                <option value="de" <?php echo $settings['language'] === 'de' ? 'selected' : ''; ?>>German</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="date_format">Date Format</label>
                                            <select id="date_format" name="date_format">
                                                <option value="Y-m-d" <?php echo $settings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                                <option value="m/d/Y" <?php echo $settings['date_format'] === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                                <option value="d/m/Y" <?php echo $settings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                                <option value="F j, Y" <?php echo $settings['date_format'] === 'F j, Y' ? 'selected' : ''; ?>>Month DD, YYYY</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-grid two-column">
                                        <div class="form-group">
                                            <label for="time_format">Time Format</label>
                                            <select id="time_format" name="time_format">
                                                <option value="H:i" <?php echo $settings['time_format'] === 'H:i' ? 'selected' : ''; ?>>24 Hour (HH:MM)</option>
                                                <option value="g:i A" <?php echo $settings['time_format'] === 'g:i A' ? 'selected' : ''; ?>>12 Hour (H:MM AM/PM)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-bell"></i>
                                        Notification Settings
                                    </h3>

                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="notifications_enabled" value="1" <?php echo $settings['notifications_enabled'] ? 'checked' : ''; ?>>
                                                <span class="checkmark"></span>
                                                Enable Notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="email_notifications" value="1" <?php echo $settings['email_notifications'] ? 'checked' : ''; ?>>
                                                <span class="checkmark"></span>
                                                Email Notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="browser_notifications" value="1" <?php echo $settings['browser_notifications'] ? 'checked' : ''; ?>>
                                                <span class="checkmark"></span>
                                                Browser Notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="notification_sound" value="1" <?php echo $settings['notification_sound'] ? 'checked' : ''; ?>>
                                                <span class="checkmark"></span>
                                                Notification Sound
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h3 class="section-title">
                                        <i class="fas fa-palette"></i>
                                        Interface Settings
                                    </h3>

                                    <div class="form-grid two-column">
                                        <div class="form-group">
                                            <label for="theme">Theme</label>
                                            <select id="theme" name="theme">
                                                <option value="light" <?php echo $settings['theme'] === 'light' ? 'selected' : ''; ?>>Light</option>
                                                <option value="dark" <?php echo $settings['theme'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                                <option value="auto" <?php echo $settings['theme'] === 'auto' ? 'selected' : ''; ?>>Auto (System)</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="items_per_page">Items Per Page</label>
                                            <select id="items_per_page" name="items_per_page">
                                                <option value="10" <?php echo $settings['items_per_page'] == 10 ? 'selected' : ''; ?>>10</option>
                                                <option value="25" <?php echo $settings['items_per_page'] == 25 ? 'selected' : ''; ?>>25</option>
                                                <option value="50" <?php echo $settings['items_per_page'] == 50 ? 'selected' : ''; ?>>50</option>
                                                <option value="100" <?php echo $settings['items_per_page'] == 100 ? 'selected' : ''; ?>>100</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Save Preferences
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all tabs and panes
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // Add active class to clicked tab and corresponding pane
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });

            // Profile image preview
            const profileImageInput = document.getElementById('profile_image');
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');

            if (profileImageInput) {
                profileImageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];

                    if (file) {
                        // Validate file type
                        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                        if (!allowedTypes.includes(file.type)) {
                            alert('Please select a valid image file (JPG, PNG, or GIF)');
                            e.target.value = '';
                            imagePreview.style.display = 'none';
                            return;
                        }

                        // Validate file size (2MB)
                        if (file.size > 2097152) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            imagePreview.style.display = 'none';
                            return;
                        }

                        // Show preview
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImg.src = e.target.result;
                            imagePreview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    } else {
                        imagePreview.style.display = 'none';
                    }
                });
            }

            // Password confirmation validation
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');

            if (newPassword && confirmPassword) {
                function validatePasswords() {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                }

                newPassword.addEventListener('input', validatePasswords);
                confirmPassword.addEventListener('input', validatePasswords);
            }
        });
    </script>
</body>
</html>