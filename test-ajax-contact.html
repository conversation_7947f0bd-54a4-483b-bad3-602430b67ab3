<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Contact Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #e0b929;
        }
        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 3px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 20px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 AJAX Contact Form Test</h1>
    <p>This page tests the AJAX contact form functionality directly.</p>

    <div id="success-message" class="message success"></div>
    <div id="error-message" class="message error"></div>
    <div id="info-message" class="message info"></div>

    <form id="test-contact-form">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" value="Test User" required>
        </div>

        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>

        <div class="form-group">
            <label for="phone">Phone</label>
            <input type="tel" id="phone" name="phone" value="************">
        </div>

        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" required>This is a test message from the AJAX test page.</textarea>
        </div>

        <input type="hidden" name="source" value="AJAX Test Page">

        <button type="submit">🚀 Send Test Message</button>
    </form>

    <div id="debug"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('test-contact-form');
            const debugDiv = document.getElementById('debug');
            
            function log(message) {
                const timestamp = new Date().toLocaleTimeString();
                debugDiv.textContent += `[${timestamp}] ${message}\n`;
                console.log(message);
            }
            
            function showMessage(type, text) {
                // Hide all messages
                document.getElementById('success-message').style.display = 'none';
                document.getElementById('error-message').style.display = 'none';
                document.getElementById('info-message').style.display = 'none';
                
                // Show the appropriate message
                const messageElement = document.getElementById(type + '-message');
                if (messageElement) {
                    messageElement.textContent = text;
                    messageElement.style.display = 'block';
                }
            }
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                log('Form submission started');
                showMessage('info', 'Sending your message...');
                
                // Create form data
                const formData = new FormData(form);
                
                // Log form data
                log('Form data:');
                for (let [key, value] of formData.entries()) {
                    log(`  ${key}: ${value}`);
                }
                
                // Send AJAX request
                fetch('process-contact.php', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    log(`Response status: ${response.status}`);
                    log(`Response ok: ${response.ok}`);
                    log(`Response headers: ${JSON.stringify([...response.headers])}`);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    return response.text().then(text => {
                        log(`Raw response: ${text}`);
                        try {
                            const data = JSON.parse(text);
                            log(`Parsed JSON: ${JSON.stringify(data)}`);
                            return data;
                        } catch (e) {
                            log(`JSON parse error: ${e.message}`);
                            throw new Error('Invalid JSON response');
                        }
                    });
                })
                .then(data => {
                    log(`Final data: ${JSON.stringify(data)}`);
                    if (data.success) {
                        showMessage('success', data.message);
                        form.reset();
                        log('Form reset after success');
                    } else {
                        showMessage('error', data.message);
                        log('Error message shown');
                    }
                })
                .catch(error => {
                    log(`Fetch error: ${error.message}`);
                    showMessage('error', 'An error occurred while sending your message. Please try again later.');
                });
            });
            
            log('AJAX Contact Form Test initialized');
        });
    </script>
</body>
</html>
