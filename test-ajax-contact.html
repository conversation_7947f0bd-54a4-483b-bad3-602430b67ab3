<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Contact Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #f1ca2f;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #e0b929;
        }
        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 3px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 20px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body><div class="content-wrapper"><h1>🧪 AJAX Contact Form</h1>
<p>This page tests the AJAX contact form functionality directly.</p>
<div id="success-message" class="message success"></div>
<div id="error-message" class="message error"></div>
<div id="info-message" class="message info"></div>
<form id="test-contact-form">
<div class="form-group"><label for="name">Name *</label> <input required="" type="text" value="Test User"></div>
<div class="form-group"><label for="email">Email *</label> <input id="email" name="email" required="" type="email" value="<EMAIL>"></div>
<div class="form-group"><label for="phone">Phone</label> <input id="phone" name="phone" type="tel" value="************"></div>
<div class="form-group"><label for="message">Message *</label> <textarea id="message" name="message" required="">This is a test message from the AJAX test page.</textarea></div>
<input name="source" type="hidden" value="AJAX Test Page"> <button type="submit">🚀 Send Test Message</button></form>
<div id="debug"></div></div></body>
</html>
