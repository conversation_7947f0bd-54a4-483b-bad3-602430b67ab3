<?php
/**
 * Database Configuration
 *
 * This file contains the database connection settings and security functions.
 *
 * Updated with security enhancements on: <?php echo date('Y-m-d H:i:s'); ?>
 */

// Database connection settings
define('DB_HOST', 'localhost');
define('DB_USER', 'u171951215_news'); // Use your actual database username with prefix
define('DB_PASS', '#jQdYdr6'); // Add your actual password here
define('DB_NAME', 'u171951215_news'); // Use your actual database name with prefix
define('DB_INSTALLED', false);
define('DB_PREFIX', ''); // Will be dynamically detected from the hosting provider
define('INSTALLATION_DATE', date('Y-m-d H:i:s'));

// Security settings
define('CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('PASSWORD_REQUIRES_MIXED_CASE', true);
define('PASSWORD_REQUIRES_NUMBERS', true);
define('PASSWORD_REQUIRES_SYMBOLS', true);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_DURATION', 900); // 15 minutes
define('XSS_PROTECTION', true);
define('CONTENT_SECURITY_POLICY', true);

// Debug settings00000000000000000000000000000000000000000000000000000000000000000000000000000000000
define('DEBUG_MODE', false); // Set to false in production

// Error reporting
ini_set('display_errors', 0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

// Database connection with error handling
$conn = null;
$max_retries = 3;
$retry_count = 0;
$connection_error = '';

// Skip database connection if we're in the installation process
if (!defined('INSTALLING') || !INSTALLING) {
    while ($retry_count < $max_retries && $conn === null) {
        try {
            // Create connection - with error suppression to handle it gracefully
            $conn = @new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

            // Check connection
            if ($conn->connect_error) {
                throw new Exception('Connection failed: ' . $conn->connect_error);
            }

            // Set charset
            $conn->set_charset('utf8mb4');

        } catch (Exception $e) {
            $connection_error = $e->getMessage();
            $retry_count++;

            if ($retry_count >= $max_retries) {
                error_log('Database connection failed after ' . $max_retries . ' attempts: ' . $connection_error);

                // Don't exit immediately, allow the page to load with an error message
                // This prevents blank pages when database connection fails
                $conn = null;
                $db_error = true;
                $db_error_message = $connection_error;

                // Only show error page if not on index.php or login.php
                $current_script = basename($_SERVER['SCRIPT_NAME']);
                if ($current_script !== 'index.php' && $current_script !== 'login.php') {
                    // Show error page
                    $error_file = __DIR__ . '/includes/db-error.php';
                    if (file_exists($error_file)) {
                        include_once $error_file;
                    } else {
                        echo "Database connection failed: " . $connection_error;
                    }
                    exit;
                }
            }

            // Wait before retrying
            sleep(1);
        }
    }
} else {
    // We're in the installation process, log this
    error_log('Skipping database connection during installation');
}

// Helper functions

/**
 * Include helper functions
 */
/**
 * Include helpers.php if it exists and is not already included
 */
$helpers_file = __DIR__ . '/includes/helpers.php';
if (file_exists($helpers_file)) {
    require_once $helpers_file;
}

// redirect() function is now defined in helpers.php

/**
 * Sanitize input data to prevent SQL injection
 *
 * @param mixed $input Input to sanitize
 * @return mixed Sanitized input
 */
function sanitize($input) {
    global $conn;

    // Handle arrays recursively
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
        return $input;
    }

    // Handle strings
    if (is_string($input)) {
        if ($conn) {
            return $conn->real_escape_string(trim($input));
        }
        return trim($input);
    }

    // Return as is for other types
    return $input;
}

/**
 * Sanitize output to prevent XSS
 *
 * @param string $output String to sanitize
 * @return string Sanitized output
 */
function html_escape($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

// CSRF functions are now defined in security.php
// We'll include that file if the functions don't exist

if (!function_exists('generate_csrf_token') || !function_exists('validate_csrf_token')) {
    // Check if security.php exists
    $security_file = __DIR__ . '/includes/security.php';
    if (file_exists($security_file)) {
        // Include security.php which defines these functions
        require_once $security_file;
    } else {
        // Define the functions here as a fallback

        /**
         * Generate CSRF token
         *
         * @return string CSRF token
         */
        if (!function_exists('generate_csrf_token')) {
            function generate_csrf_token() {
                // Make sure session is started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                if (empty($_SESSION['csrf_token'])) {
                    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                }
                return $_SESSION['csrf_token'];
            }
        }

        /**
         * Validate CSRF token
         *
         * @param string $token Token to validate
         * @return bool True if valid, false otherwise
         */
        if (!function_exists('validate_csrf_token')) {
            function validate_csrf_token($token) {
                // Make sure session is started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                // If CSRF protection is disabled, always return true
                if (!defined('CSRF_PROTECTION') || !CSRF_PROTECTION) {
                    return true;
                }

                // If token is empty, return false
                if (empty($token)) {
                    error_log("CSRF validation failed: Empty token provided");
                    return false;
                }

                // If session token is not set, return false
                if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
                    error_log("CSRF validation failed: No token in session");
                    return false;
                }

                // Compare tokens
                return hash_equals($_SESSION['csrf_token'], $token);
            }
        }
    }
}

/**
 * Log database errors
 *
 * @param string $message Error message
 * @return string Generic error message for display
 */
function db_error($message) {
    error_log('[DB Error] ' . $message);
    return 'Database error occurred. Please check the error log for details.';
}

/**
 * Check if user is logged in
 *
 * @return bool True if logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Validate password strength
 *
 * @param string $password Password to validate
 * @return array Result with status and message
 */
function validate_password($password) {
    $result = [
        'valid' => true,
        'message' => ''
    ];

    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $result['valid'] = false;
        $result['message'] = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        return $result;
    }

    if (PASSWORD_REQUIRES_MIXED_CASE && (!preg_match('/[a-z]/', $password) || !preg_match('/[A-Z]/', $password))) {
        $result['valid'] = false;
        $result['message'] = 'Password must include both uppercase and lowercase letters.';
        return $result;
    }

    if (PASSWORD_REQUIRES_NUMBERS && !preg_match('/[0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = 'Password must include at least one number.';
        return $result;
    }

    if (PASSWORD_REQUIRES_SYMBOLS && !preg_match('/[^A-Za-z0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = 'Password must include at least one special character.';
        return $result;
    }

    return $result;
}

/**
 * Prepare and execute a SQL query safely
 *
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters to bind
 * @param string $types Types of parameters (s: string, i: integer, d: double, b: blob)
 * @return mixed Result object or false on failure
 */
function db_query($sql, $params = [], $types = '') {
    global $conn;

    if (!$conn) {
        error_log('Database connection not available');
        return false;
    }

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log('Failed to prepare statement: ' . $conn->error);
        return false;
    }

    if (!empty($params)) {
        if (empty($types)) {
            // Auto-detect types if not provided
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } elseif (is_string($param)) {
                    $types .= 's';
                } else {
                    $types .= 'b';
                }
            }
        }

        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result && $stmt->errno) {
        error_log('Query execution failed: ' . $stmt->error);
    }

    return $result;
}

/**
 * Initialize secure session settings if not already set
 */
function init_secure_session() {
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
        session_start();
    }

    // Regenerate session ID periodically for security
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } else if (time() - $_SESSION['last_regeneration'] > 300) { // Every 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

/**
 * Set security headers to prevent common attacks
 */
function set_security_headers() {
    // X-XSS-Protection
    if (defined('XSS_PROTECTION') && XSS_PROTECTION) {
        header('X-XSS-Protection: 1; mode=block');
    }

    // Content-Security-Policy
    if (defined('CONTENT_SECURITY_POLICY') && CONTENT_SECURITY_POLICY) {
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self'");
    }

    // X-Content-Type-Options
    header('X-Content-Type-Options: nosniff');

    // X-Frame-Options
    header('X-Frame-Options: SAMEORIGIN');

    // Referrer-Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
}

// Initialize security headers
set_security_headers();

// Initialize secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    init_secure_session();
}

// Create db-error.php file if it doesn't exist
$error_file_path = __DIR__ . '/includes/db-error.php';
if (!file_exists($error_file_path)) {
    $error_page = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .error-container {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #721c24;
        }
        p {
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            background-color: #f1ca2f;
            color: #333;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>Database Connection Error</h1>
        <p>We're having trouble connecting to the database. This could be due to:</p>
        <ul style="text-align: left; display: inline-block;">
            <li>Database server is down</li>
            <li>Database credentials are incorrect</li>
            <li>Database name doesn't exist</li>
        </ul>
        <p>Please try again later or contact the administrator.</p>
    </div>
    <a href="../index.php" class="btn">Go to Homepage</a>
    <a href="install.php?run_installation=1" class="btn">Run Installation</a>
</body>
</html>
HTML;

    $includes_dir = __DIR__ . '/includes';
    if (!is_dir($includes_dir)) {
        mkdir($includes_dir, 0755, true);
    }

    file_put_contents($error_file_path, $error_page);
}
?>
