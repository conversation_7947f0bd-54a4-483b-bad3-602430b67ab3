
<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Contact Us | Manage Inc.</title>
    <link rel="stylesheet" href="css/style.css?v=20250541">
    <link rel="stylesheet" href="css/carousel.css?v=20250541">
    <link rel="stylesheet" href="css/submenu.css?v=20250541">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        main {
            flex: 1;
            padding-bottom: 30px;
        }

        /* Ensure mobile menu toggle is visible */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block !important;
            }

            .main-nav {
                display: none !important;
            }

            .main-nav.active {
                display: block !important;
            }

            .logo {
                text-align: left !important;
            }

            .main-header .container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }
        }
        .contact-us-page-banner {
            background-color: #f1ca2f;
            background-image: url('images/banners/contact-us-heading-bg.jpg');
            margin-top: 90px;
            min-height: 120px;
            padding: 40px 0;
            text-align: center;
        }
        .contact-us-page-banner::before {
            background-color: rgba(241, 202, 47, 0.7);
        }
        .contact-us-page-banner h1 {
            font-size: 36px;
            font-weight: 600;
            color: #333;
            position: relative;
            z-index: 5;
            text-align: center;
            width: 100%;
        }
        .contact-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0 60px 0;
        }

        /* Responsive styles for contact page */
        @media (max-width: 768px) {
            .contact-layout {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .contact-form-side {
                order: 2;
            }

            .contact-info-side {
                order: 1;
            }

            .contact-form input,
            .contact-form textarea {
                width: 100%;
            }

            .contact-us-page-banner {
                margin-top: 70px;
                padding: 30px 0;
            }

            .contact-us-page-banner h1 {
                font-size: 28px;
            }
        }
        .contact-form-side {
            padding: 0;
            box-shadow: none;
            background-color: transparent;
        }
        .contact-form-side h2 {
            font-size: 24px;
            margin-bottom: 25px;
            font-weight: 600;
        }
        .contact-form input,
        .contact-form textarea {
            border: 1px solid #ddd;
            border-radius: 0;
            padding: 12px 15px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .contact-form textarea {
            height: 120px;
        }
        .contact-form .submit-btn {
            background-color: #f1ca2f;
            color: #333;
            font-weight: 600;
            padding: 10px 25px;
            border: none;
            border-radius: 0;
            cursor: pointer;
            float: left;
            transition: all 0.3s;
        }

        .contact-form .submit-btn:hover {
            background-color: #e0bc20;
        }
        .contact-info-side {
            background-color: #3c3c45;
            padding: 30px;
            color: #fff;
            border-radius: 0;
        }
        .contact-info-side h3 {
            font-size: 18px;
            margin-bottom: 25px;
            font-weight: 600;
            color: #fff;
        }
        .contact-info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        .contact-icon {
            margin-right: 15px;
            min-width: 20px;
        }
        .contact-icon img {
            width: 16px;
            height: 16px;
            filter: brightness(0) saturate(100%) invert(80%) sepia(54%) saturate(1071%) hue-rotate(359deg) brightness(103%) contrast(101%);
        }
        .contact-info-item p {
            margin: 0;
            line-height: 1.5;
            color: #fff;
            font-size: 14px;
        }
        .contact-info-item a {
            color: #fff;
            text-decoration: none;
        }
        .contact-info-item a:hover {
            color: #f1ca2f;
        }
        /* Footer styling for contact page */
        .contact-page .footer {
            margin-top: 0;
            border-top: 1px solid #eee;
        }
        .page-content {
            padding-bottom: 20px;
        }

        /* Contact form message styles */
        .contact-success-message,
        .contact-error-message,
        .contact-info-message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }

        .contact-success-message {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .contact-error-message {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }

        .contact-info-message {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body class="contact-page">
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="Manage Incorporated - Established in 1995">
                </a>
            </div>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="index.html">HOME</a></li>
                    <li><a href="cloud.html">CLOUD</a></li>
                    <li><a href="managed-services.html">MANAGED SERVICES</a></li>
                    <li><a href="infrastructure.html">INFRASTRUCTURE</a></li>
                    <li><a href="services.html">SERVICES</a></li>
                    <li><a href="news.html">NEWS</a></li>
                    <li class="active"><a href="contact-us.html">CONTACT US</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="contact-us-page-banner">
            <div class="container">
                <h1>Contact Us</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <!-- Success message will appear here -->
                <div class="contact-success-message" style="display: none;">
                    <i class="fas fa-check-circle"></i> Thank you for your message. We'll get back to you soon!
                </div>

                <!-- Error message will appear here -->
                <div class="contact-error-message" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> Please fill in all required fields.
                </div>

                <!-- Info message will appear here -->
                <div class="contact-info-message" style="display: none;">
                    <i class="fas fa-info-circle"></i> Sending your message...
                </div>

                <div class="contact-layout">
                    <div class="contact-form-side">
                        <h2>Get in Touch</h2>
                        <form class="contact-form" method="post" action="process-contact-clean.php" id="main-contact-form">
                            <input type="text" id="name" name="name" placeholder="Name" required>

                            <input type="email" id="email" name="email" placeholder="Email Address" required>

                            <input type="tel" id="phone" name="phone" placeholder="Phone">

                            <textarea id="message" name="message" placeholder="Message" required></textarea>

                            <input type="hidden" name="source" value="Contact Page">

                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>

                    <div class="contact-info-side">
                        <h3>Seattle, WA Office</h3>
                        <div class="contact-info-item">
                            <div class="contact-icon">
                                <img src="images/footer/location-icon.svg" alt="Location">
                            </div>
                            <p>600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-icon">
                                <img src="images/footer/phone-icon.svg" alt="Phone">
                            </div>
                            <p>(*************</p>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-icon">
                                <img src="images/footer/fax-icon.svg" alt="Fax">
                            </div>
                            <p>(*************</p>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-icon">
                                <img src="images/footer/email-icon.svg" alt="Email">
                            </div>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <!-- Footer Top Section -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-top-content">
                    <div class="footer-info">
                        <h2>BUILD A FUTURE-READY IT INFRASTRUCTURE</h2>
                        <p>Leverage cutting-edge technology to optimize performance, security, and scalability.</p>
                    </div>
                    <!-- Footer contact form hidden on contact page -->
                    <div class="footer-form" style="display: none;">
                        <form method="post" action="process-contact-clean.php" id="footer-contact-form">
                            <input type="hidden" name="source" value="Footer Form">
                            <div class="form-row">
                                <input type="text" name="name" placeholder="Name" required>
                                <input type="email" name="email" placeholder="Email Address" required>
                            </div>
                            <textarea name="message" placeholder="Message" required></textarea>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section -->
        <div class="footer-middle">
            <div class="container">
                <div class="footer-middle-content">
                    <div class="footer-logo">
                        <img src="images/footer/manage-inc-logo.png" alt="Manage Incorporated">
                    </div>
                    <div class="footer-services">
                        <h4>SERVICES</h4>
                        <ul>
                            <li><a href="cloud.html">Cloud</a></li>
                            <li><a href="managed-services.html">Managed Services</a></li>
                            <li><a href="infrastructure.html">Infrastructure</a></li>
                            <li><a href="services.html">Services</a></li>
                        </ul>
                    </div>
                    <div class="footer-contact">
                        <h4>Seattle, WA Office</h4>
                        <p><img src="images/footer/phone-icon.svg" alt="Phone"> (*************</p>
                        <p><img src="images/footer/fax-icon.svg" alt="Fax"> (*************</p>
                        <p><img src="images/footer/email-icon.svg" alt="Email"> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><img src="images/footer/location-icon.svg" alt="Location"> 600 Stewart Street, Suite 400, Seattle, WA 98101</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <p>© Copyright 2025. Manage Inc. all Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/carousel.js?v=20250541"></script>
    <script src="js/mobile-menu.js?v=20250541"></script>
    <script src="js/submenu-mobile.js?v=20250541"></script>
    <script src="js/footer-form.js?v=20250546"></script>
    <script src="js/contact-form.js?v=20250546"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        // Ensure mobile menu is working
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.toggle('active');
                    document.getElementById('mainNav').classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
