<?php
/**
 * Auth Wrapper
 * 
 * This file serves as a wrapper for the Auth class to maintain compatibility
 * with both old and new code.
 */

// Include the Auth class
require_once 'lib/Auth.php';

// If this file is included directly, make sure we have a database connection
if (!isset($conn) && file_exists('config.php')) {
    require_once 'config.php';
}

// Create Auth instance if not already created
if (!isset($auth) && isset($conn)) {
    $auth = new Auth($conn);
}
?>
