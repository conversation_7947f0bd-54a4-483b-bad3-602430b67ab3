<?php
session_start();
require_once 'config.php';
require_once 'lib/EmailSender.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    redirect('index.php');
}

$message = '';
$status = '';

// Check if user_id is provided
if (isset($_GET['user_id']) && !empty($_GET['user_id'])) {
    $user_id = (int)$_GET['user_id'];

    // Get user information
    $sql = "SELECT id, username, email, is_verified FROM users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Check if user is already verified
        if (isset($user['is_verified']) && $user['is_verified'] == 1) {
            $message = "This user is already verified.";
            $status = "info";
        } else {
            // Generate verification token
            $token = bin2hex(random_bytes(32)); // More secure token generation
            $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

            // Check if tokens table exists
            $check_table = $conn->query("SHOW TABLES LIKE 'verification_tokens'");
            if ($check_table->num_rows == 0) {
                // Create tokens table
                $create_table = "CREATE TABLE verification_tokens (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    user_id INT(11) NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expiry DATETIME NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY token (token)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                $conn->query($create_table);
            }

            // Start transaction
            $conn->begin_transaction();

            try {
                // Delete any existing tokens for this user
                $delete_tokens = "DELETE FROM verification_tokens WHERE user_id = ?";
                $delete_stmt = $conn->prepare($delete_tokens);
                $delete_stmt->bind_param("i", $user_id);
                $delete_stmt->execute();

                // Save new token
                $insert_token = "INSERT INTO verification_tokens (user_id, token, expiry) VALUES (?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_token);
                $insert_stmt->bind_param("iss", $user_id, $token, $expiry);
                $insert_stmt->execute();

                // Initialize email sender
                $emailSender = new EmailSender($conn);

                // Get site URL from settings
                $site_url = '';
                $site_name = '';
                $site_url_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_url'";
                $site_name_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_name'";

                $site_url_result = $conn->query($site_url_query);
                if ($site_url_result && $site_url_result->num_rows > 0) {
                    $site_url = $site_url_result->fetch_assoc()['setting_value'];
                } else {
                    $site_url = 'https://manageinc.redconic.com';
                }

                $site_name_result = $conn->query($site_name_query);
                if ($site_name_result && $site_name_result->num_rows > 0) {
                    $site_name = $site_name_result->fetch_assoc()['setting_value'];
                } else {
                    $site_name = 'Manage Inc.';
                }

                // Create verification URL with absolute path
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];

                // Get the document root path
                $doc_root = $_SERVER['DOCUMENT_ROOT'];

                // Get the current script's directory path (admin)
                $admin_path = dirname(__FILE__);

                // Get the relative path from document root to the current directory
                $relative_path = str_replace('\\', '/', str_replace($doc_root, '', $admin_path));

                // Build the verification URL with the correct path structure
                // This will create a URL like: https://redconic.com/manageinc-html/admin/verify.php?token=xyz
                $verification_url = $protocol . $host . $relative_path . '/verify.php?token=' . $token;

                // Clean up the URL (remove double slashes, etc.)
                $verification_url = preg_replace('#([^:])//+#', '$1/', $verification_url);

                // Log the verification URL for debugging
                error_log("Verification URL: " . $verification_url);

                // Email subject and content
                $subject = "Verify Your Account - " . $site_name;
                $message = "
                <html>
                <head>
                    <title>Verify Your Account</title>
                </head>
                <body>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
                        <div style='text-align: center; margin-bottom: 20px;'>
                            <img src='{$site_url}/images/login-logo.jpg' alt='{$site_name}' style='max-width: 200px;'>
                        </div>
                        <div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>
                            <h2 style='color: #333; margin-top: 0;'>Welcome to {$site_name}!</h2>
                            <p>Hello {$user['username']},</p>
                            <p>Thank you for creating an account with us. Please click the button below to verify your email address:</p>
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='{$verification_url}' style='background-color: #f1ca2f; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Verify Your Email</a>
                            </div>
                            <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                            <p style='word-break: break-all;'><a href='{$verification_url}'>{$verification_url}</a></p>
                            <p>This link will expire in 24 hours.</p>
                            <p>If you did not create an account, please ignore this email.</p>
                        </div>
                        <div style='margin-top: 20px; text-align: center; color: #777; font-size: 12px;'>
                            <p>&copy; " . date('Y') . " {$site_name}. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>
                ";

                // Send the email
                $result = $emailSender->sendEmail($user['email'], $subject, $message);

                // Log the email sending result
                error_log("Verification email sending result: " . ($result ? "Success" : "Failed") . " to " . $user['email']);

                if ($result) {
                    $message = "Verification email has been sent to " . $user['email'] . ".";
                    $status = "success";

                    // Commit transaction
                    $conn->commit();

                    // Log successful email sending
                    error_log("Admin resent verification email to: " . $user['email'] . " by admin ID: " . $_SESSION['user_id']);

                    // Clear expired token session variables
                    unset($_SESSION['expired_token_email']);
                    unset($_SESSION['expired_token_user_id']);
                } else {
                    // Rollback transaction
                    $conn->rollback();

                    $message = "Failed to send verification email. Please check the error logs for details.";
                    $status = "error";

                    // Log error
                    error_log("Failed to send verification email to: " . $user['email']);
                }
            } catch (Exception $e) {
                // Rollback transaction
                $conn->rollback();

                $message = "An error occurred. Please try again later.";
                $status = "error";

                // Log error
                error_log("Error in admin-resend-verification.php: " . $e->getMessage());
            }
        }
    } else {
        $message = "User not found.";
        $status = "error";
    }
} else {
    $message = "User ID is required.";
    $status = "error";
}

// Set page title
$page_title = "Resend Verification Email";

// Include header
include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h2><i class="fas fa-paper-plane"></i> Resend Verification Email</h2>
        <div class="admin-actions">
            <a href="users.php" class="admin-btn outline">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <?php if (!empty($message)): ?>
        <div class="admin-alert <?php echo $status; ?>">
            <?php if ($status == 'error'): ?>
                <i class="fas fa-exclamation-circle"></i>
            <?php elseif ($status == 'success'): ?>
                <i class="fas fa-check-circle"></i>
            <?php else: ?>
                <i class="fas fa-info-circle"></i>
            <?php endif; ?>
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <div class="admin-card">
        <div class="admin-card-body">
            <p>From this page, administrators can resend verification emails to users whose verification tokens have expired.</p>

            <?php if ($status == 'success'): ?>
                <div class="admin-actions" style="margin-top: 20px;">
                    <a href="users.php" class="admin-btn">
                        <i class="fas fa-users"></i> Return to User Management
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
