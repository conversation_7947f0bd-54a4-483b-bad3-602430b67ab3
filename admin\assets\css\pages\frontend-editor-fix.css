/**
 * Frontend Editor Fixes
 * 
 * This CSS file contains fixes for the frontend editor layout issues
 */

/* Editor Container Fixes */
.editor-container {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 500px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.editor-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    min-height: 400px;
}

.wysiwyg-editor {
    flex: 1;
    overflow: auto;
    padding: 20px;
    border: none;
    outline: none;
    min-height: 400px;
    height: auto;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Make sure content stays within the editor */
.wysiwyg-editor img {
    max-width: 100%;
    height: auto;
}

.wysiwyg-editor table {
    max-width: 100%;
    overflow-x: auto;
    display: block;
}

.wysiwyg-editor pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 100%;
    overflow-x: auto;
}

.wysiwyg-editor iframe {
    max-width: 100%;
}

/* Card body padding fix */
.card-body.p-0 .editor-container {
    border: none;
    border-radius: 0;
}

/* Card footer spacing */
.card-footer {
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Version comment input */
.version-comment {
    flex: 1;
    margin-right: 15px;
}

/* Editor actions */
.editor-actions {
    display: flex;
    gap: 10px;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .card-footer {
        flex-direction: column;
        align-items: stretch;
    }
    
    .version-comment {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .editor-actions {
        justify-content: flex-end;
    }
}
