// Admin Panel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Search Functionality
    initSearch();

    // Mobile Menu Toggle
    initMobileMenu();

    // Submenu Toggle
    initSubmenuToggle();

    // Sidebar Collapse
    initSidebarCollapse();

    // User dropdown menu toggle
    initUserDropdown();

    // Initialize tooltips
    initTooltips();

    // Initialize fixed submenu
    initFixedSubmenu();

    // Initialize table sorting
    initTableSorting();

    // Initialize notifications
    initNotifications();

    // Add data-label attributes to tables for mobile card view
    addDataLabelsToTables();
});



// User Dropdown Functionality
function initUserDropdown() {
    const userMenuToggle = document.getElementById('userMenuToggle');
    const userDropdown = document.querySelector('.admin-user-dropdown');

    if (!userMenuToggle) {
        console.log('User menu toggle not found');
        return;
    }

    if (!userDropdown) {
        console.log('User dropdown not found');
        return;
    }

    console.log('Initializing user dropdown');
    console.log('User dropdown initial state:', {
        display: window.getComputedStyle(userDropdown).display,
        visibility: window.getComputedStyle(userDropdown).visibility,
        opacity: window.getComputedStyle(userDropdown).opacity
    });

    // Toggle user dropdown on click
    userMenuToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('User menu toggle clicked');

        // Close notifications dropdown if open
        const notificationsDropdown = document.getElementById('notificationsDropdown');
        if (notificationsDropdown && notificationsDropdown.classList.contains('show')) {
            notificationsDropdown.classList.remove('show');
            const notificationsToggle = document.getElementById('notificationsToggle');
            if (notificationsToggle) notificationsToggle.classList.remove('active');
        }

        // Toggle user dropdown - simplified to match notifications dropdown
        userDropdown.classList.toggle('show');
        userMenuToggle.classList.toggle('active');

        // Add animation effect
        this.classList.add('clicked');
        setTimeout(() => {
            this.classList.remove('clicked');
        }, 300);

        console.log('User dropdown visibility:', userDropdown.classList.contains('show'));
    });

    // Close dropdown when clicking outside - simplified to match notifications dropdown
    document.addEventListener('click', function(e) {
        if (!userMenuToggle.contains(e.target) && !userDropdown.contains(e.target)) {
            if (userDropdown.classList.contains('show')) {
                userDropdown.classList.remove('show');
                userMenuToggle.classList.remove('active');
            }
        }
    });

    // Add CSS for click animation
    if (!document.getElementById('userDropdownAnimation')) {
        const style = document.createElement('style');
        style.id = 'userDropdownAnimation';
        style.textContent = `
            .admin-user-menu-toggle.clicked {
                transform: translateY(-2px);
                transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }
            .admin-user-menu-toggle.clicked .fa-chevron-down {
                transform: rotate(180deg);
                transition: transform 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    }

    // Log dropdown position for debugging
    console.log('User dropdown position:', {
        top: window.getComputedStyle(userDropdown).top,
        right: window.getComputedStyle(userDropdown).right,
        transform: window.getComputedStyle(userDropdown).transform
    });
}

// Search Functionality
function initSearch() {
    const searchInput = document.querySelector('.admin-search-input');
    const searchButton = document.querySelector('.admin-search-button');

    if (!searchInput) return;

    // Function to perform search
    const performSearch = () => {
        const searchTerm = searchInput.value.trim();
        if (searchTerm.length > 0) {
            // Redirect to search results page
            window.location.href = 'search.php?q=' + encodeURIComponent(searchTerm);
        }
    };

    // Search on Enter key
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // Search on button click
    if (searchButton) {
        searchButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            performSearch();
        });

        // Add animation effect
        searchButton.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-50%) scale(0.9)';
        });

        searchButton.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-50%) scale(1)';
        });

        searchButton.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-50%) scale(1)';
        });
    }

    // Add clear button to search input
    const searchContainer = document.querySelector('.admin-search');
    if (searchContainer) {
        // Create clear button if it doesn't exist
        let clearButton = searchContainer.querySelector('.admin-search-clear');

        if (!clearButton) {
            clearButton = document.createElement('span');
            clearButton.className = 'admin-search-clear';
            clearButton.innerHTML = '<i class="fas fa-times"></i>';
            clearButton.style.display = 'none';
            searchContainer.appendChild(clearButton);
        }

        // Show/hide clear button based on input content
        searchInput.addEventListener('input', function() {
            clearButton.style.display = this.value.length > 0 ? 'flex' : 'none';
        });

        // Initial check for existing content
        clearButton.style.display = searchInput.value.length > 0 ? 'flex' : 'none';

        // Clear search input on button click
        clearButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            searchInput.value = '';
            this.style.display = 'none';
            searchInput.focus();
        });
    }
}

// Initialize tooltips
function initTooltips() {
    // This function is now handled by tooltips.js
    // Keeping this function for backward compatibility
    // It will be called by the DOMContentLoaded event in admin.js

    // The new implementation in tooltips.js provides:
    // - More positioning options (top, bottom, left, right, auto)
    // - Different tooltip types (default, info, success, warning, danger, light)
    // - Custom width option
    // - Optional tooltip icons
    // - Better mobile support
    // - Accessibility improvements
    // - Dynamic tooltips for elements added after page load

    // For documentation on how to use the new tooltips, see:
    // - data-tooltip: The tooltip text
    // - data-tooltip-position: The tooltip position (top, bottom, left, right, auto)
    // - data-tooltip-type: The tooltip type (info, success, warning, danger, light)
    // - data-tooltip-width: Custom width for the tooltip
    // - data-tooltip-icon: Optional icon class (e.g., "fas fa-info-circle")
}

// Mobile Menu Toggle
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle active class on the button
            this.classList.toggle('active');

            // Toggle sidebar visibility
            sidebar.classList.toggle('show');

            // Handle overlay
            if (sidebar.classList.contains('show')) {
                // Create overlay if it doesn't exist
                let overlay = document.getElementById('sidebar-overlay');
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.id = 'sidebar-overlay';
                    overlay.className = 'sidebar-overlay';
                    document.body.appendChild(overlay);

                    // Close sidebar when overlay is clicked
                    overlay.addEventListener('click', function() {
                        sidebar.classList.remove('show');
                        this.style.display = 'none';
                        document.body.classList.remove('sidebar-open');
                        mobileMenuToggle.classList.remove('active');
                    });
                }

                // Show overlay
                overlay.style.display = 'block';
                document.body.classList.add('sidebar-open');
            } else {
                // Hide overlay
                const overlay = document.getElementById('sidebar-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
                document.body.classList.remove('sidebar-open');
            }
        });
    }
}

// Submenu Toggle
function initSubmenuToggle() {
    const menuToggles = document.querySelectorAll('.menu-toggle');

    // Remove any existing event listeners (to prevent duplicates)
    menuToggles.forEach(toggle => {
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);
    });

    // Get fresh references after replacing elements
    const freshMenuToggles = document.querySelectorAll('.menu-toggle');

    freshMenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Menu toggle clicked:', this.getAttribute('data-toggle'));

            const parent = this.parentElement;

            // Store the current scroll position
            const sidebar = document.querySelector('.admin-sidebar');
            const scrollPosition = sidebar ? sidebar.scrollTop : 0;

            // Close all other open submenus
            document.querySelectorAll('.has-submenu.open').forEach(item => {
                if (item !== parent) {
                    item.classList.remove('open');
                }
            });

            // Toggle current submenu
            parent.classList.toggle('open');

            // Add animation effect
            if (parent.classList.contains('open')) {
                this.classList.add('clicked');
                const submenuIcon = this.querySelector('.submenu-icon');
                if (submenuIcon) {
                    submenuIcon.classList.add('clicked');
                    setTimeout(() => {
                        submenuIcon.classList.remove('clicked');
                    }, 300);
                }

                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 300);
            }

            // Restore scroll position to prevent jumping
            if (sidebar) {
                sidebar.scrollTop = scrollPosition;
            }
        });
    });

    // Add CSS for click animation
    if (!document.getElementById('submenuAnimation')) {
        const style = document.createElement('style');
        style.id = 'submenuAnimation';
        style.textContent = `
            .menu-toggle.clicked .submenu-icon {
                transform: translateY(-50%) rotate(180deg) scale(1.2);
                transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                color: var(--primary-color);
            }
        `;
        document.head.appendChild(style);
    }
}



// Fixed Submenu on Scroll
function initFixedSubmenu() {
    const submenu = document.querySelector('.admin-submenu');
    const topbar = document.querySelector('.admin-topbar');

    if (!submenu || !topbar) return;

    // Get initial positions
    const topbarHeight = topbar.offsetHeight;
    const submenuTop = submenu.offsetTop;

    // Function to handle scroll
    function handleScroll() {
        const scrollY = window.scrollY || window.pageYOffset;
        const isMobile = window.innerWidth <= 768;

        // Add fixed class when scrolled past the submenu
        if (scrollY > submenuTop) {
            submenu.classList.add('fixed');

            // On mobile, we want the submenu right at the top
            if (isMobile) {
                submenu.style.top = '0';
            } else {
                submenu.style.top = topbarHeight + 'px';
            }

            // Add padding to prevent content jump
            if (!submenu.nextElementSibling.style.paddingTop) {
                submenu.nextElementSibling.style.paddingTop = submenu.offsetHeight + 'px';
            }
        } else {
            submenu.classList.remove('fixed');
            submenu.style.top = '';

            // Remove padding
            if (submenu.nextElementSibling.style.paddingTop) {
                submenu.nextElementSibling.style.paddingTop = '';
            }
        }
    }

    // Listen for scroll events
    window.addEventListener('scroll', handleScroll);

    // Listen for resize events to handle orientation changes
    window.addEventListener('resize', handleScroll);

    // Initial check
    handleScroll();
}

// Sidebar Collapse - Updated to use only the toggle button in the footer
function initSidebarCollapse() {
    const sidebar = document.querySelector('.admin-sidebar');
    const mainContent = document.querySelector('.admin-main');

    if (sidebar && mainContent) {
        // Remove any existing fixed position toggle buttons
        const existingButton = document.querySelector('.sidebar-collapse-toggle');
        if (existingButton) {
            existingButton.remove();
        }

        // Remove any existing desktop toggle button
        const existingDesktopToggle = document.querySelector('.desktop-sidebar-toggle');
        if (existingDesktopToggle) {
            existingDesktopToggle.remove();
        }

        // Check localStorage for saved state
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }

        // Create footer toggle button only
        createFooterToggleButton(sidebar, mainContent);
    }
}

// Desktop sidebar toggle button function removed as we're only using the footer toggle

// Create footer toggle button
function createFooterToggleButton(sidebar, mainContent) {
    // Get the sidebar toggle container
    const toggleContainer = document.getElementById('sidebarToggleContainer');
    if (!toggleContainer) {
        console.error('Sidebar toggle container not found');
        return;
    }

    // Clear any existing content
    toggleContainer.innerHTML = '';

    // Create the toggle button
    const toggleButton = document.createElement('button');
    toggleButton.className = 'sidebar-toggle';
    toggleButton.setAttribute('aria-label', 'Toggle Sidebar');
    toggleButton.setAttribute('title', 'Toggle Sidebar');

    // Force styles directly on the element with enhanced styling
    toggleButton.style.cssText = `
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        background-color: var(--primary-color) !important;
        border: none !important;
        color: var(--secondary-color) !important;
        cursor: pointer !important;
        padding: 8px !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 50% !important;
        width: 32px !important;
        height: 32px !important;
        margin-left: 10px !important;
        transition: all 0.3s ease !important;
        z-index: 1000 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    `;

    // Add hover effects
    toggleButton.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#e6b800';
        this.style.transform = 'scale(1.1)';
        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
    });

    toggleButton.addEventListener('mouseleave', function() {
        this.style.backgroundColor = 'var(--primary-color)';
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    });

    // Create icon
    const icon = document.createElement('i');
    icon.className = localStorage.getItem('sidebarCollapsed') === 'true' ? 'fas fa-angle-right' : 'fas fa-angle-left';
    toggleButton.appendChild(icon);

    // Add click event
    toggleButton.addEventListener('click', function() {
        console.log('Sidebar toggle clicked'); // Debug log
        toggleSidebar(sidebar, mainContent, icon);
    });

    // Add to container
    toggleContainer.appendChild(toggleButton);

    // Force container to be visible with direct styles
    toggleContainer.style.cssText = `
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        margin-left: auto !important;
        align-items: center !important;
        z-index: 1000 !important;
        min-width: 42px !important;
        height: 100% !important;
        justify-content: flex-end !important;
    `;

    console.log('Sidebar toggle button created and added to container'); // Debug log
}

// Toggle sidebar function (shared between desktop and footer toggle buttons)
function toggleSidebar(sidebar, mainContent, icon) {
    // Toggle collapsed class on body
    const nowCollapsed = document.body.classList.toggle('sidebar-collapsed');

    // Toggle collapsed class on sidebar
    sidebar.classList.toggle('collapsed');

    // Toggle expanded class on main content
    mainContent.classList.toggle('expanded');

    // Update icon
    icon.className = nowCollapsed ? 'fas fa-angle-right' : 'fas fa-angle-left';

    // Ensure toggle button remains visible
    const toggleContainer = document.getElementById('sidebarToggleContainer');
    const toggleButton = document.querySelector('.sidebar-toggle');

    if (toggleContainer) {
        toggleContainer.style.display = 'flex';
        toggleContainer.style.visibility = 'visible';
        toggleContainer.style.opacity = '1';
    }

    if (toggleButton) {
        toggleButton.style.display = 'flex';
        toggleButton.style.visibility = 'visible';
        toggleButton.style.opacity = '1';
    }

    // Update all toggle button icons
    const allIcons = document.querySelectorAll('.sidebar-toggle i, .desktop-sidebar-toggle i');
    allIcons.forEach(i => {
        i.className = nowCollapsed ? 'fas fa-angle-right' : 'fas fa-angle-left';
    });

    // Save to localStorage
    localStorage.setItem('sidebarCollapsed', nowCollapsed);

    // Create form data for AJAX request
    const formData = new FormData();
    formData.append('action', 'update_sidebar_preference');
    formData.append('sidebar_collapsed', nowCollapsed ? 1 : 0);

    // Send AJAX request
    fetch('ajax/update_user_settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Sidebar preference saved:', data);
    })
    .catch(error => {
        console.error('Error saving sidebar preference:', error);
    });
}

// Image preview for file inputs
document.addEventListener('DOMContentLoaded', function() {
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const preview = input.closest('.form-group').querySelector('.image-preview');
            if (!preview) return;

            const previewEmpty = preview.querySelector('.image-preview-empty');

            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    if (previewEmpty) {
                        previewEmpty.style.display = 'none';
                    }

                    let img = preview.querySelector('img');

                    if (!img) {
                        img = document.createElement('img');
                        preview.appendChild(img);
                    }

                    img.src = e.target.result;
                    preview.style.display = 'block';
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    });
});

// Table Sorting and Pagination
function initTableSorting() {
    const tables = document.querySelectorAll('.admin-table');

    tables.forEach(table => {
        const headers = table.querySelectorAll('th.sortable');

        headers.forEach(header => {
            header.addEventListener('click', function() {
                const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                const currentIsAsc = header.classList.contains('sorted-asc');

                // Remove sorted classes from all headers
                headers.forEach(h => {
                    h.classList.remove('sorted-asc', 'sorted-desc');
                });

                // Add appropriate class to current header
                if (currentIsAsc) {
                    header.classList.add('sorted-desc');
                    sortTable(table, columnIndex, false);
                } else {
                    header.classList.add('sorted-asc');
                    sortTable(table, columnIndex, true);
                }
            });
        });
    });
}

function sortTable(table, columnIndex, asc) {
    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Sort the rows
    const sortedRows = rows.sort((a, b) => {
        const aValue = getCellValue(a, columnIndex);
        const bValue = getCellValue(b, columnIndex);

        return asc ? compareValues(aValue, bValue) : compareValues(bValue, aValue);
    });

    // Remove existing rows
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // Add sorted rows
    tbody.append(...sortedRows);
}

function getCellValue(row, index) {
    const cell = row.cells[index];
    if (!cell) return '';
    return cell.textContent.trim();
}

function compareValues(a, b) {
    // Check if values are numbers
    const aNum = parseFloat(a);
    const bNum = parseFloat(b);

    if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
    }

    // Check if values are dates
    const aDate = new Date(a);
    const bDate = new Date(b);

    if (!isNaN(aDate) && !isNaN(bDate)) {
        return aDate - bDate;
    }

    // Default to string comparison
    return a.localeCompare(b);
}

// Notifications Functionality
function initNotifications() {
    const notificationsToggle = document.getElementById('notificationsToggle');
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const notificationList = document.getElementById('notificationList');
    const markAllReadBtn = document.getElementById('markAllRead');
    const notificationsBadge = document.getElementById('notificationsBadge');

    if (!notificationsToggle || !notificationsDropdown) {
        return;
    }

    // Load notifications when dropdown is opened
    notificationsToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Close user dropdown if open
        const userDropdown = document.querySelector('.admin-user-dropdown');
        const userMenuToggle = document.getElementById('userMenuToggle');
        if (userDropdown && userDropdown.classList.contains('show')) {
            userDropdown.classList.remove('show');
            if (userMenuToggle) userMenuToggle.classList.remove('active');
        }

        // Toggle notifications dropdown
        notificationsDropdown.classList.toggle('show');
        this.classList.toggle('active');

        // Load notifications if dropdown is shown
        if (notificationsDropdown.classList.contains('show')) {
            loadNotifications();
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!notificationsToggle.contains(e.target) && !notificationsDropdown.contains(e.target)) {
            notificationsDropdown.classList.remove('show');
            notificationsToggle.classList.remove('active');
        }
    });

    // Mark all notifications as read
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            fetch('api/notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'mark_all_read'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI
                    const unreadItems = notificationList.querySelectorAll('.notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // Hide badge
                    if (notificationsBadge) {
                        notificationsBadge.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
        });
    }

    // Function to load notifications
    function loadNotifications() {
        // Show loading state
        notificationList.innerHTML = `
            <li class="notification-item loading">
                <div class="notification-content">
                    <p class="notification-text">Loading notifications...</p>
                </div>
            </li>
        `;

        // Fetch notifications from API
        fetch('api/notifications.php')
            .then(response => response.json())
            .then(data => {
                // Update badge count
                if (data.unread_count > 0) {
                    if (notificationsBadge) {
                        notificationsBadge.textContent = data.unread_count;
                        notificationsBadge.style.display = 'flex';
                    }
                } else {
                    if (notificationsBadge) {
                        notificationsBadge.style.display = 'none';
                    }
                }

                // Clear loading state
                notificationList.innerHTML = '';

                // Check if there are notifications
                if (data.notifications && data.notifications.length > 0) {
                    // Render notifications
                    data.notifications.forEach(notification => {
                        const notificationItem = document.createElement('li');
                        notificationItem.className = `notification-item${notification.is_read == 0 ? ' unread' : ''}`;
                        notificationItem.dataset.id = notification.id;

                        // Format date
                        const date = new Date(notification.created_at);
                        const formattedDate = formatNotificationDate(date);

                        notificationItem.innerHTML = `
                            <div class="notification-icon ${notification.type}">
                                <i class="${notification.icon}"></i>
                            </div>
                            <div class="notification-content">
                                <h4 class="notification-title">${notification.title}</h4>
                                <p class="notification-text">${notification.message}</p>
                                <span class="notification-time">${formattedDate}</span>
                            </div>
                        `;

                        // Add click event to mark as read and navigate if there's a link
                        notificationItem.addEventListener('click', function() {
                            const notificationId = this.dataset.id;

                            // Mark as read
                            fetch('api/notifications.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    action: 'mark_read',
                                    notification_id: notificationId
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Update UI
                                    notificationItem.classList.remove('unread');

                                    // Update badge count
                                    if (notificationsBadge) {
                                        const currentCount = parseInt(notificationsBadge.textContent);
                                        if (currentCount > 1) {
                                            notificationsBadge.textContent = currentCount - 1;
                                        } else {
                                            notificationsBadge.style.display = 'none';
                                        }
                                    }

                                    // Navigate to link if exists
                                    if (notification.link) {
                                        window.location.href = notification.link;
                                    }
                                }
                            })
                            .catch(error => {
                                console.error('Error marking notification as read:', error);
                            });
                        });

                        notificationList.appendChild(notificationItem);
                    });
                } else {
                    // Show empty state
                    notificationList.innerHTML = `
                        <li class="notification-item empty">
                            <div class="notification-content">
                                <p class="notification-text">No notifications yet.</p>
                            </div>
                        </li>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationList.innerHTML = `
                    <li class="notification-item error">
                        <div class="notification-content">
                            <p class="notification-text">Error loading notifications. Please try again.</p>
                        </div>
                    </li>
                `;
            });
    }

    // Helper function to format notification date
    function formatNotificationDate(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        if (diffSec < 60) {
            return 'Just now';
        } else if (diffMin < 60) {
            return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
        } else if (diffHour < 24) {
            return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
        } else if (diffDay < 7) {
            return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
        } else {
            const options = { year: 'numeric', month: 'short', day: 'numeric' };
            return date.toLocaleDateString(undefined, options);
        }
    }

    // Check for new notifications periodically (every 60 seconds)
    setInterval(() => {
        if (!notificationsDropdown.classList.contains('show')) {
            // Only update badge if dropdown is closed
            fetch('api/notifications.php')
                .then(response => {
                    // Check if response is valid JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        throw new Error('Invalid response format: ' + contentType);
                    }
                })
                .then(data => {
                    if (data.unread_count > 0) {
                        if (notificationsBadge) {
                            notificationsBadge.textContent = data.unread_count;
                            notificationsBadge.style.display = 'flex';
                        }
                    } else {
                        if (notificationsBadge) {
                            notificationsBadge.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking notifications:', error);
                    // Don't update the badge on error to avoid flickering
                });
        }
    }, 60000);
}

// Function to add data-label attributes to table cells for mobile card view
function addDataLabelsToTables() {
    // Get all tables
    const tables = document.querySelectorAll('table, .admin-table, .table');

    tables.forEach(function(table) {
        // Get headers and rows
        const headers = table.querySelectorAll('th');
        const rows = table.querySelectorAll('tbody tr');

        // Add data-title attribute to rows
        rows.forEach(function(row) {
            // Get title from the first cell or use a default
            let title = '';
            const titleCell = row.querySelector('.title-column .post-info strong, td:first-child strong');

            if (titleCell) {
                title = titleCell.textContent.trim();
            } else {
                // Try to get title from the first cell
                const firstCell = row.querySelector('td:first-child');
                if (firstCell) {
                    title = firstCell.textContent.trim();
                }

                // If still empty, try to get title from the current page
                if (!title) {
                    const currentPage = window.location.pathname.split('/').pop();

                    if (currentPage === 'all_news.php') {
                        title = 'News Item';
                    } else if (currentPage === 'users.php') {
                        title = 'User';
                    } else if (currentPage === 'contact_submissions.php') {
                        title = 'Message';
                    } else {
                        title = 'Item';
                    }
                }
            }

            // Set data-title attribute
            row.setAttribute('data-title', title);

            // Add data-label attributes to cells
            const cells = row.querySelectorAll('td');
            cells.forEach(function(cell, index) {
                if (index < headers.length) {
                    const headerText = headers[index].textContent.trim();
                    cell.setAttribute('data-label', headerText);
                }
            });
        });
    });
}
