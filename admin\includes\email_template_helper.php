<?php
/**
 * Email Template Helper Functions
 *
 * This file contains functions for formatting email templates with theme colors and logo
 */

// Disable error reporting for production
error_reporting(0);
ini_set('display_errors', 0);

/**
 * Get appearance settings from database
 * 
 * @param object $conn Database connection
 * @return array Appearance settings
 */
function getAppearanceSettings($conn) {
    $settings = [
        'theme_color' => '#3c3c45',
        'secondary_color' => '#f1ca2f',
        'admin_logo' => 'images/logo.png'
    ];
    
    // First try appearance category
    $sql = "SELECT setting_key, setting_value FROM system_settings WHERE category = 'appearance'";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            switch ($row['setting_key']) {
                case 'theme_color':
                case 'primary_color':
                    $settings['theme_color'] = $row['setting_value'];
                    break;
                case 'secondary_color':
                    $settings['secondary_color'] = $row['setting_value'];
                    break;
                case 'admin_logo':
                    $settings['admin_logo'] = $row['setting_value'];
                    break;
            }
        }
    }
    
    return $settings;
}

/**
 * Format plain text email content with HTML template using theme colors
 * 
 * @param string $subject Email subject
 * @param string $content Plain text email content
 * @param array $appearance_settings Appearance settings
 * @param string $site_url Site URL
 * @param string $company_name Company name
 * @return string HTML formatted email
 */
function formatEmailWithTemplate($subject, $content, $appearance_settings, $site_url = '', $company_name = 'Manage Inc') {
    // Get theme color and logo
    $theme_color = $appearance_settings['theme_color'] ?? '#3c3c45';
    $secondary_color = $appearance_settings['secondary_color'] ?? '#f1ca2f';
    $logo_path = $appearance_settings['admin_logo'] ?? 'images/logo.png';
    
    // Make sure logo path is absolute
    if (strpos($logo_path, 'http') !== 0) {
        // If it's a relative path, convert to absolute
        if (strpos($logo_path, '/') === 0) {
            // Path starts with /, assume it's relative to domain root
            $logo_path = $site_url . $logo_path;
        } else {
            // Path is relative to current directory
            $logo_path = $site_url . '/' . $logo_path;
        }
    }
    
    // Convert plain text to HTML
    $html_content = nl2br(htmlspecialchars($content));
    
    // Create HTML email template
    $html_template = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($subject) . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
                background-color: #f5f5f5;
            }
            .email-container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                overflow: hidden;
            }
            .email-header {
                background-color: ' . $theme_color . ';
                padding: 20px;
                text-align: center;
            }
            .email-header img {
                max-width: 200px;
                max-height: 60px;
            }
            .email-content {
                padding: 30px;
                background-color: #ffffff;
            }
            .email-footer {
                padding: 20px;
                text-align: center;
                font-size: 12px;
                color: #777;
                background-color: #f9f9f9;
                border-top: 1px solid #e0e0e0;
            }
            h1, h2, h3, h4, h5, h6 {
                color: ' . $theme_color . ';
            }
            a {
                color: ' . $secondary_color . ';
                text-decoration: none;
            }
            a:hover {
                text-decoration: underline;
            }
            .button {
                display: inline-block;
                padding: 10px 20px;
                background-color: ' . $secondary_color . ';
                color: #ffffff;
                text-decoration: none;
                border-radius: 4px;
                margin: 20px 0;
            }
            .button:hover {
                background-color: ' . adjustBrightness($secondary_color, -20) . ';
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <img src="' . $logo_path . '" alt="' . htmlspecialchars($company_name) . '">
            </div>
            <div class="email-content">
                <h2>' . htmlspecialchars($subject) . '</h2>
                ' . $html_content . '
            </div>
            <div class="email-footer">
                <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($company_name) . '. All rights reserved.</p>
                <p>This email was sent from a notification-only address that cannot accept incoming email.</p>
            </div>
        </div>
    </body>
    </html>
    ';
    
    return $html_template;
}

/**
 * Adjust color brightness
 * 
 * @param string $hex Hex color code
 * @param int $steps Steps to adjust brightness (-255 to 255)
 * @return string Adjusted hex color
 */
function adjustBrightness($hex, $steps) {
    // Steps should be between -255 and 255. Negative = darker, positive = lighter
    $steps = max(-255, min(255, $steps));
    
    // Format the hex color string
    $hex = str_replace('#', '', $hex);
    if (strlen($hex) == 3) {
        $hex = str_repeat(substr($hex, 0, 1), 2) . str_repeat(substr($hex, 1, 1), 2) . str_repeat(substr($hex, 2, 1), 2);
    }
    
    // Get decimal values
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    // Adjust
    $r = max(0, min(255, $r + $steps));
    $g = max(0, min(255, $g + $steps));
    $b = max(0, min(255, $b + $steps));
    
    // Convert back to hex
    $r_hex = str_pad(dechex($r), 2, '0', STR_PAD_LEFT);
    $g_hex = str_pad(dechex($g), 2, '0', STR_PAD_LEFT);
    $b_hex = str_pad(dechex($b), 2, '0', STR_PAD_LEFT);
    
    return '#' . $r_hex . $g_hex . $b_hex;
}
?>
