/* Inbox Page Styles */
.inbox-page {
    background-color: #f8f9fa;
}

.inbox-wrapper {
    max-width: 100%;
    margin: 0 auto;
}

/* Override admin-container max-width for inbox page */
.inbox-page .admin-container,
body.inbox-page .admin-container {
    max-width: none !important;
    width: 100% !important;
    padding: var(--spacing-4);
}

/* Ensure inbox wrapper uses full width */
.inbox-page .inbox-wrapper,
body.inbox-page .inbox-wrapper {
    max-width: none !important;
    width: 100% !important;
    margin: 0;
}

/* Inbox Header */
.inbox-header {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inbox-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.inbox-stat {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    min-width: 100px;
}

.inbox-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.inbox-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Inbox Toolbar - Single Row Layout */
.inbox-toolbar {
    background: #fff;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inbox-toolbar-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filters-section {
    flex: 1;
    min-width: 0; /* Allow shrinking */
}

.actions-section {
    flex-shrink: 0; /* Prevent shrinking */
}

/* Filter Form Layout */
.inbox-filter-form {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group select {
    min-width: 140px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
}

/* Search Group */
.search-group {
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
}

.search-group input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 200px;
}

.search-btn {
    padding: 8px 12px;
    background: #f1ca2f;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: #e0b929;
}

.clear-search {
    padding: 8px;
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.clear-search:hover {
    color: #333;
    background: #f8f9fa;
}

/* Bulk Action Form */
.bulk-action-form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bulk-action-form select {
    min-width: 120px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
}

/* Per Page Selector */
.per-page-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.per-page-selector label {
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}

.per-page-selector select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
    min-width: 70px;
}

.filter-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

/* Message List */
.message-list {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-item {
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-sender {
    font-weight: 600;
    color: #333;
}

.message-email {
    color: #666;
    font-size: 14px;
    margin-left: 10px;
}

.message-date {
    color: #666;
    font-size: 13px;
}

.message-subject {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.message-preview {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.message-action {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    color: #666;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.message-action:hover {
    background: #f8f9fa;
    color: #333;
}

.message-action.primary {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.message-action.primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.message-action.danger {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
}

.message-action.danger:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Single Message View */
.message-detail {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-detail-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.message-detail-body {
    padding: 20px;
}

.message-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-weight: 600;
    color: #333;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.meta-value {
    color: #666;
    font-size: 14px;
}

.message-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* Bulk Actions */
.bulk-actions {
    background: #fff;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.bulk-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Pagination */
.pagination-wrapper {
    background: #fff;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

/* Empty State */
.empty-inbox {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-inbox-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-inbox h3 {
    color: #333;
    margin-bottom: 10px;
}

.empty-inbox p {
    color: #666;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Toolbar responsive layout */
    .inbox-toolbar-row {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .toolbar-section {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filters-section,
    .actions-section {
        flex: none;
    }

    .inbox-filter-form {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group,
    .search-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .filter-group select,
    .search-group input {
        min-width: auto;
        width: 100%;
    }

    .search-group {
        flex-direction: row;
        align-items: center;
    }

    .bulk-action-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .bulk-action-form select {
        min-width: auto;
        width: 100%;
    }

    .per-page-selector {
        justify-content: space-between;
    }

    .per-page-selector select {
        min-width: auto;
        flex: 1;
        max-width: 100px;
    }

    /* Message layout responsive */
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .message-actions {
        flex-wrap: wrap;
    }

    .message-meta {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .inbox-toolbar {
        padding: 12px 15px;
    }

    .search-group input {
        min-width: auto;
        flex: 1;
    }

    .filter-group select,
    .bulk-action-form select,
    .per-page-selector select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark Mode Support */
body.dark-mode .inbox-page {
    background-color: #1a1a1a;
}

body.dark-mode .inbox-header,
body.dark-mode .inbox-toolbar,
body.dark-mode .message-list,
body.dark-mode .message-detail,
body.dark-mode .empty-inbox {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-mode .filter-group select,
body.dark-mode .search-group input,
body.dark-mode .bulk-action-form select,
body.dark-mode .per-page-selector select {
    background-color: #3a3a3a;
    border-color: #404040;
    color: #e0e0e0;
}

body.dark-mode .search-btn {
    background-color: #f1ca2f;
    color: #333;
}

body.dark-mode .search-btn:hover {
    background-color: #e0b929;
}

body.dark-mode .clear-search {
    color: #b0b0b0;
}

body.dark-mode .clear-search:hover {
    color: #e0e0e0;
    background-color: #3a3a3a;
}

body.dark-mode .per-page-selector label {
    color: #b0b0b0;
}

body.dark-mode .message-item {
    border-bottom-color: #404040;
}

body.dark-mode .message-item:hover {
    background-color: #3a3a3a;
}

body.dark-mode .message-item.unread {
    background-color: #3d3520;
    border-left-color: #ffc107;
}

body.dark-mode .message-content {
    background-color: #3a3a3a;
    border-color: #404040;
}

body.dark-mode .message-detail-header {
    background-color: #3a3a3a;
    border-bottom-color: #404040;
}
