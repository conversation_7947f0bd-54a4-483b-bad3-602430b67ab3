<?php
/**
 * Logo Helper Class
 * 
 * Provides functionality to get appropriate logos for different page types
 */

class LogoHelper {
    private $conn;
    private $logos = [];
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->loadLogos();
    }
    
    /**
     * Load all logo settings from database
     */
    private function loadLogos() {
        try {
            $sql = "SELECT setting_key, setting_value FROM system_settings 
                    WHERE category = 'appearance' 
                    AND setting_key IN ('admin_logo', 'login_logo', 'main_logo', 'small_logo', 'favicon')";
            
            $result = $this->conn->query($sql);
            
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $this->logos[$row['setting_key']] = $row['setting_value'];
                }
            }
        } catch (Exception $e) {
            error_log("Error loading logos: " . $e->getMessage());
        }
    }
    
    /**
     * Get logo for admin panel
     * 
     * @return string Logo path or default
     */
    public function getAdminLogo() {
        return $this->getLogo('admin_logo', 'admin/images/logo.png');
    }
    
    /**
     * Get logo for login and authentication pages
     * 
     * @return string Logo path or fallback
     */
    public function getLoginLogo() {
        // Try login_logo first, then admin_logo, then default
        $logo = $this->getLogo('login_logo');
        if (empty($logo)) {
            $logo = $this->getLogo('admin_logo', 'admin/images/logo.png');
        }
        return $logo;
    }
    
    /**
     * Get logo for main website pages
     * 
     * @return string Logo path or fallback
     */
    public function getMainLogo() {
        // Try main_logo first, then admin_logo, then default
        $logo = $this->getLogo('main_logo');
        if (empty($logo)) {
            $logo = $this->getLogo('admin_logo', 'images/logo.png');
        }
        return $logo;
    }
    
    /**
     * Get logo for small pages (index.php, logout.php, reset-password.php, etc.)
     * 
     * @return string Logo path or fallback
     */
    public function getSmallLogo() {
        // Try small_logo first, then login_logo, then admin_logo, then default
        $logo = $this->getLogo('small_logo');
        if (empty($logo)) {
            $logo = $this->getLogo('login_logo');
        }
        if (empty($logo)) {
            $logo = $this->getLogo('admin_logo', 'images/logo-small.png');
        }
        return $logo;
    }
    
    /**
     * Get favicon
     * 
     * @return string Favicon path or default
     */
    public function getFavicon() {
        return $this->getLogo('favicon', 'favicon.ico');
    }
    
    /**
     * Get logo by type with optional default
     * 
     * @param string $type Logo type
     * @param string $default Default logo path
     * @return string Logo path
     */
    private function getLogo($type, $default = '') {
        $logo = isset($this->logos[$type]) ? $this->logos[$type] : '';
        
        // If logo is set and file exists, return it
        if (!empty($logo)) {
            // Check if it's a relative path and file exists
            if (file_exists($logo) || file_exists('../' . $logo)) {
                return $logo;
            }
        }
        
        // Return default if provided and exists
        if (!empty($default)) {
            if (file_exists($default) || file_exists('../' . $default)) {
                return $default;
            }
        }
        
        return '';
    }
    
    /**
     * Get logo for current page automatically
     * 
     * @return string Logo path
     */
    public function getLogoForCurrentPage() {
        $current_script = basename($_SERVER['SCRIPT_NAME']);
        $current_dir = basename(dirname($_SERVER['SCRIPT_NAME']));
        
        // Admin panel pages
        if ($current_dir === 'admin' || strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
            return $this->getAdminLogo();
        }
        
        // Login and authentication pages
        if (in_array($current_script, ['login.php', 'register.php', 'forgot-password.php', 'reset-password.php'])) {
            return $this->getLoginLogo();
        }
        
        // Small pages
        if (in_array($current_script, ['index.php', 'logout.php', 'reset-password.php', '404.php', '500.php'])) {
            return $this->getSmallLogo();
        }
        
        // Default to main logo for other pages
        return $this->getMainLogo();
    }
    
    /**
     * Generate HTML img tag for logo
     * 
     * @param string $type Logo type or 'auto' for automatic detection
     * @param string $alt Alt text
     * @param string $class CSS classes
     * @param array $attributes Additional attributes
     * @return string HTML img tag
     */
    public function getLogoHtml($type = 'auto', $alt = 'Logo', $class = '', $attributes = []) {
        // Get logo path
        switch ($type) {
            case 'admin':
                $logo = $this->getAdminLogo();
                break;
            case 'login':
                $logo = $this->getLoginLogo();
                break;
            case 'main':
                $logo = $this->getMainLogo();
                break;
            case 'small':
                $logo = $this->getSmallLogo();
                break;
            case 'auto':
            default:
                $logo = $this->getLogoForCurrentPage();
                break;
        }
        
        // If no logo found, return empty string
        if (empty($logo)) {
            return '';
        }
        
        // Build HTML attributes
        $attrs = ['src' => $logo, 'alt' => $alt];
        
        if (!empty($class)) {
            $attrs['class'] = $class;
        }
        
        // Add custom attributes
        $attrs = array_merge($attrs, $attributes);
        
        // Build HTML
        $html = '<img';
        foreach ($attrs as $key => $value) {
            $html .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
        }
        $html .= '>';
        
        return $html;
    }
    
    /**
     * Check if a specific logo type is configured
     * 
     * @param string $type Logo type
     * @return bool True if logo is configured and file exists
     */
    public function hasLogo($type) {
        $logo = $this->getLogo($type);
        return !empty($logo);
    }
    
    /**
     * Get all configured logos
     * 
     * @return array Array of logo types and their paths
     */
    public function getAllLogos() {
        return [
            'admin' => $this->getAdminLogo(),
            'login' => $this->getLoginLogo(),
            'main' => $this->getMainLogo(),
            'small' => $this->getSmallLogo(),
            'favicon' => $this->getFavicon()
        ];
    }
    
    /**
     * Update logo setting
     * 
     * @param string $type Logo type
     * @param string $path Logo path
     * @return bool Success status
     */
    public function updateLogo($type, $path) {
        try {
            $valid_types = ['admin_logo', 'login_logo', 'main_logo', 'small_logo', 'favicon'];
            
            if (!in_array($type, $valid_types)) {
                return false;
            }
            
            $stmt = $this->conn->prepare("UPDATE system_settings SET setting_value = ? WHERE category = 'appearance' AND setting_key = ?");
            $stmt->bind_param("ss", $path, $type);
            
            if ($stmt->execute()) {
                // Update local cache
                $this->logos[$type] = $path;
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Error updating logo: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Global helper function to get logo HTML
 * 
 * @param string $type Logo type or 'auto'
 * @param string $alt Alt text
 * @param string $class CSS classes
 * @param array $attributes Additional attributes
 * @return string HTML img tag
 */
function get_logo_html($type = 'auto', $alt = 'Logo', $class = '', $attributes = []) {
    global $conn;
    
    if (!$conn) {
        return '';
    }
    
    static $logoHelper = null;
    
    if ($logoHelper === null) {
        $logoHelper = new LogoHelper($conn);
    }
    
    return $logoHelper->getLogoHtml($type, $alt, $class, $attributes);
}

/**
 * Global helper function to get logo path
 * 
 * @param string $type Logo type or 'auto'
 * @return string Logo path
 */
function get_logo_path($type = 'auto') {
    global $conn;
    
    if (!$conn) {
        return '';
    }
    
    static $logoHelper = null;
    
    if ($logoHelper === null) {
        $logoHelper = new LogoHelper($conn);
    }
    
    switch ($type) {
        case 'admin':
            return $logoHelper->getAdminLogo();
        case 'login':
            return $logoHelper->getLoginLogo();
        case 'main':
            return $logoHelper->getMainLogo();
        case 'small':
            return $logoHelper->getSmallLogo();
        case 'favicon':
            return $logoHelper->getFavicon();
        case 'auto':
        default:
            return $logoHelper->getLogoForCurrentPage();
    }
}
?>
