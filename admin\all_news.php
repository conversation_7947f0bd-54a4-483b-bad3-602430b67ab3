<?php
session_start();
require_once 'config.php';
require_once 'lib/Permissions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to view or manage news
if (!$permissions->hasPermission('manage_news') && !$permissions->hasPermission('view_news')) {
    $_SESSION['error_message'] = "You do not have permission to access the News section.";
    redirect('dashboard.php');
}

// Check if news and categories tables exist
$news_table_check = $conn->query("SHOW TABLES LIKE 'news'");
$categories_table_check = $conn->query("SHOW TABLES LIKE 'categories'");

// Create tables if they don't exist
if ($news_table_check->num_rows == 0) {
    $create_news_table = "CREATE TABLE news (
        id INT(11) NOT NULL AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        featured_image VARCHAR(255) DEFAULT NULL,
        category_id INT(11) DEFAULT NULL,
        author_id INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_author_id (author_id),
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_news_table);
}

if ($categories_table_check->num_rows == 0) {
    $create_categories_table = "CREATE TABLE categories (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_categories_table);

    // Add some default categories
    $default_categories = [
        ['name' => 'Technology', 'slug' => 'technology'],
        ['name' => 'Business', 'slug' => 'business'],
        ['name' => 'Cloud', 'slug' => 'cloud'],
        ['name' => 'Security', 'slug' => 'security']
    ];

    foreach ($default_categories as $category) {
        $name = $category['name'];
        $slug = $category['slug'];
        $insert_category = "INSERT INTO categories (name, slug) VALUES ('$name', '$slug')";
        $conn->query($insert_category);
    }
}

// Get news settings
require_once 'lib/NewsSettings.php';
$newsSettings = new NewsSettings($conn);
$adminSettings = $newsSettings->getAdminSettings();

// Pagination settings
// Check if per_page is set in the URL
if (isset($_GET['per_page']) && is_numeric($_GET['per_page'])) {
    $items_per_page = (int)$_GET['per_page'];

    // Get valid per_page values from admin settings
    $valid_per_page_values = $adminSettings['items_per_page_options'];

    // Ensure it's a valid value
    if (!in_array($items_per_page, $valid_per_page_values)) {
        $items_per_page = $adminSettings['news_per_page']; // Default from settings if invalid
    }
} else {
    // Get user's items_per_page setting if available
    $items_per_page = $adminSettings['news_per_page']; // Default from settings

    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $settings_query = "SELECT items_per_page FROM user_settings WHERE user_id = $user_id";
        $settings_result = $conn->query($settings_query);

        if ($settings_result && $settings_result->num_rows > 0) {
            $user_settings = $settings_result->fetch_assoc();
            $items_per_page = (int)$user_settings['items_per_page'];
        }
    }
}

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page); // Ensure page is at least 1
$offset = ($page - 1) * $items_per_page;

// Get sorting parameters
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : '';
$sort_order = isset($_GET['order']) ? $_GET['order'] : '';

// If sort parameters are not set, use default from admin settings
if (empty($sort_by) || empty($sort_order)) {
    // Parse default sort order from admin settings
    $default_sort = $adminSettings['default_sort_order'];

    switch ($default_sort) {
        case 'newest':
            $sort_by = 'created_at';
            $sort_order = 'DESC';
            break;
        case 'oldest':
            $sort_by = 'created_at';
            $sort_order = 'ASC';
            break;
        case 'title_asc':
            $sort_by = 'title';
            $sort_order = 'ASC';
            break;
        case 'title_desc':
            $sort_by = 'title';
            $sort_order = 'DESC';
            break;
        default:
            $sort_by = 'created_at';
            $sort_order = 'DESC';
    }
}

// Validate sort parameters
$allowed_sort_fields = ['title', 'category_name', 'created_at'];
if (!in_array($sort_by, $allowed_sort_fields)) {
    $sort_by = 'created_at';
}

$allowed_sort_orders = ['ASC', 'DESC'];
if (!in_array(strtoupper($sort_order), $allowed_sort_orders)) {
    $sort_order = 'DESC';
}

// Get category filter
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Count total news posts for pagination
$count_sql = "SELECT COUNT(*) as total FROM news";
if ($category_filter > 0) {
    $count_sql .= " WHERE category_id = $category_filter";
}
$count_result = $conn->query($count_sql);
$count_row = $count_result->fetch_assoc();
$total_items = $count_row['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get all categories for filter dropdown
$categories_sql = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_sql);

// Check which image column exists (image or featured_image)
$image_column = 'image'; // Default
$column_check = $conn->query("SHOW COLUMNS FROM news LIKE 'featured_image'");
if ($column_check && $column_check->num_rows > 0) {
    $image_column = 'featured_image';
}

// Get news posts with categories, pagination and sorting
$sql = "SELECT n.*, n.$image_column as image, c.name as category_name
        FROM news n
        LEFT JOIN categories c ON n.category_id = c.id";

// Add category filter if selected
if ($category_filter > 0) {
    $sql .= " WHERE n.category_id = $category_filter";
}

$sql .= " ORDER BY " . ($sort_by === 'category_name' ? 'c.name' : "n.$sort_by") . " $sort_order
        LIMIT $offset, $items_per_page";

$result = $conn->query($sql);

// Set page title
$page_title = "All News";

// Add CSS for the all news page
$extra_css = '<link rel="stylesheet" href="assets/css/pages/all_news.css?v=' . time() . '">';
?>

<?php include 'includes/header.php'; ?>

<!-- Pagination styles moved to all_news.css -->

    <div class="admin-container">
        <?php
        // Count total news posts
        $total_news_count = $total_items;

        // Count news by category
        $category_counts = [];
        $category_count_sql = "SELECT c.name, COUNT(n.id) as count
                              FROM categories c
                              LEFT JOIN news n ON c.id = n.category_id
                              GROUP BY c.id
                              ORDER BY count DESC
                              LIMIT 1";
        $category_count_result = $conn->query($category_count_sql);
        $top_category = '';
        $top_category_count = 0;

        if ($category_count_result && $category_count_result->num_rows > 0) {
            $top_category_row = $category_count_result->fetch_assoc();
            $top_category = $top_category_row['name'];
            $top_category_count = (int)$top_category_row['count'];
        }

        // Set up variables for content header
        $page_icon = 'fas fa-newspaper';
        $page_title = 'All News Posts';
        $stats_data = [
            ['icon' => 'fas fa-newspaper', 'text' => "Total: $total_news_count", 'highlight' => false]
        ];

        if (!empty($top_category)) {
            $stats_data[] = ['icon' => 'fas fa-tag', 'text' => "Top Category: $top_category ($top_category_count)", 'highlight' => false];
        }

        // Only show Add New Post button if user has permission to create news
        $primary_action = $permissions->hasPermission('create_news') ?
            ['url' => 'create_news.php', 'icon' => 'fas fa-plus-circle', 'text' => 'Add New Post'] :
            null;

        // Include the content header
        include 'includes/content-header.php';
        ?>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="admin-alert success">
                <i class="fas fa-check-circle"></i> <?php echo $_SESSION['success_message']; ?>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="admin-alert error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $_SESSION['error_message']; ?>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <!-- Table Actions -->
        <div class="admin-table-actions">
            <div class="admin-table-filters">
                <div class="admin-table-filter">
                    <label for="category-filter">Category:</label>
                    <select id="category-filter" onchange="applyFilters()">
                        <option value="0">All Categories</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="admin-table-filter">
                    <label for="sort-by">Sort By:</label>
                    <select id="sort-by" onchange="applyFilters()">
                        <option value="created_at" <?php echo $sort_by == 'created_at' ? 'selected' : ''; ?>>Date</option>
                        <option value="title" <?php echo $sort_by == 'title' ? 'selected' : ''; ?>>Title</option>
                        <option value="category_name" <?php echo $sort_by == 'category_name' ? 'selected' : ''; ?>>Category</option>
                    </select>
                </div>

                <div class="admin-table-filter">
                    <label for="sort-order">Order:</label>
                    <select id="sort-order" onchange="applyFilters()">
                        <option value="DESC" <?php echo $sort_order == 'DESC' ? 'selected' : ''; ?>>Descending</option>
                        <option value="ASC" <?php echo $sort_order == 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                    </select>
                </div>
            </div>

            <?php if ($permissions->hasPermission('manage_news')): ?>
            <div class="admin-table-bulk-actions">
                <form id="bulk-action-form" method="post" action="process_bulk_action.php">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="post_ids" id="bulk-post-ids" value="">
                    <input type="hidden" name="action_type" id="bulk-action-type" value="">
                    <input type="hidden" name="category_id" id="bulk-category-id" value="">

                    <select id="bulk-action">
                        <option value="">Bulk Actions</option>
                        <option value="delete">Delete</option>
                        <option value="category">Update Category</option>
                    </select>
                    <div id="bulk-category-select" class="d-none">
                        <select id="bulk-category">
                            <option value="">Select Category</option>
                            <?php
                            // Reset the categories result pointer
                            $categories_result->data_seek(0);
                            while ($category = $categories_result->fetch_assoc()):
                            ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <button type="button" class="admin-btn" onclick="applyBulkAction()">Apply</button>
                </form>
            </div>
            <?php endif; ?>
        </div>

        <?php if ($result->num_rows > 0): ?>
            <div class="admin-table-responsive">
                <table class="admin-table wp-style">
                    <thead>
                        <tr>
                            <?php if ($permissions->hasPermission('manage_news')): ?>
                            <th class="check-column">
                                <input type="checkbox" id="select-all">
                            </th>
                            <?php endif; ?>
                            <th class="title-column">Title</th>
                            <th class="category-column">Category</th>
                            <th class="date-column">Date</th>
                            <th class="actions-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <?php if ($permissions->hasPermission('manage_news')): ?>
                                <td class="check-column">
                                    <input type="checkbox" name="post_ids[]" value="<?php echo $row['id']; ?>">
                                </td>
                                <?php endif; ?>
                                <td class="title-column">
                                    <div class="post-title-area">
                                        <div class="post-thumbnail">
                                            <img src="../images/news/<?php echo $row['image']; ?>" alt="<?php echo $row['title']; ?>">
                                        </div>
                                        <div class="post-info">
                                            <strong><?php echo $row['title']; ?></strong>
                                            <div class="row-actions">
                                                <?php if ($permissions->hasPermission('manage_news')): ?>
                                                <span class="edit"><a href="edit_news.php?id=<?php echo $row['id']; ?>">Edit</a> | </span>
                                                <span class="duplicate"><a href="duplicate_news.php?id=<?php echo $row['id']; ?>">Duplicate</a> | </span>
                                                <span class="trash"><a href="delete_news.php?id=<?php echo $row['id']; ?>" onclick="return confirm('Are you sure you want to delete this post?')">Delete</a> | </span>
                                                <?php endif; ?>
                                                <span class="view"><a href="../<?php echo !empty($row['slug']) ? $row['slug'] . '.html' : 'news-detail.php?id=' . $row['id']; ?>" target="_blank">View</a></span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="category-column" data-label="Category">
                                    <?php echo !empty($row['category_name']) ? $row['category_name'] : '<span class="no-category">Uncategorized</span>'; ?>
                                </td>
                                <td class="date-column" data-label="Date">
                                    <div class="post-date">
                                        <span class="date-display"><?php echo date('Y/m/d', strtotime($row['created_at'])); ?></span>
                                        <span class="time-display"><?php echo date('g:i a', strtotime($row['created_at'])); ?></span>
                                    </div>
                                </td>
                                <td class="actions-column" data-label="Actions">
                                    <div class="news-actions">
                                        <?php if ($permissions->hasPermission('manage_news')): ?>
                                        <a href="edit_news.php?id=<?php echo $row['id']; ?>" class="admin-btn" title="Edit"><i class="fas fa-edit"></i></a>
                                        <a href="duplicate_news.php?id=<?php echo $row['id']; ?>" class="admin-btn primary" title="Duplicate"><i class="fas fa-copy"></i></a>
                                        <?php endif; ?>
                                        <a href="../<?php echo !empty($row['slug']) ? $row['slug'] . '.html' : 'news-detail.php?id=' . $row['id']; ?>" class="admin-btn secondary" title="View" target="_blank"><i class="fas fa-eye"></i></a>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <?php
                // Set up pagination parameters
                $base_url = 'all_news.php';
                $query_params = [
                    'sort' => $sort_by,
                    'order' => $sort_order,
                    'category' => $category_filter
                ];

                // Include the pagination component
                include 'includes/pagination.php';
                ?>
            </div>
        <?php else: ?>
            <div class="admin-table-empty">
                <i class="fas fa-newspaper"></i>
                <p>No news posts found. Click "Add New Post" to create your first news post.</p>
            </div>
        <?php endif; ?>
    </div>

    <script>
    // Apply mobile optimizations on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Add data-title attribute to each row for better mobile display
        const tableRows = document.querySelectorAll('.admin-table tbody tr');
        tableRows.forEach(row => {
            const titleCell = row.querySelector('.title-column .post-info strong');
            if (titleCell) {
                row.setAttribute('data-title', titleCell.textContent.trim());
            }
        });

        // Fix row actions display by removing separator characters on mobile
        function adjustRowActions() {
            if (window.innerWidth <= 767) {
                // Remove separator characters
                document.querySelectorAll('.row-actions span').forEach(span => {
                    const text = span.innerHTML;
                    if (text.includes('|')) {
                        span.innerHTML = text.replace(/\|/g, '');
                    }
                });

                // Hide bulk actions on mobile
                const bulkActionsForm = document.querySelector('.admin-table-bulk-actions');
                if (bulkActionsForm) {
                    bulkActionsForm.classList.add('d-none');
                    bulkActionsForm.classList.remove('d-flex');
                }
            } else {
                // Show bulk actions on desktop
                const bulkActionsForm = document.querySelector('.admin-table-bulk-actions');
                if (bulkActionsForm) {
                    bulkActionsForm.classList.remove('d-none');
                    bulkActionsForm.classList.add('d-flex');
                }
            }
        }

        // Run on load and resize
        adjustRowActions();
        window.addEventListener('resize', adjustRowActions);
    });

    function applyFilters() {
        const categoryFilter = document.getElementById('category-filter').value;
        const sortBy = document.getElementById('sort-by').value;
        const sortOrder = document.getElementById('sort-order').value;
        const itemsPerPage = document.getElementById('items-per-page')?.value || <?php echo $items_per_page; ?>;

        window.location.href = `all_news.php?page=1&sort=${sortBy}&order=${sortOrder}&category=${categoryFilter}&per_page=${itemsPerPage}`;
    }

    // changeItemsPerPage function is now included in the pagination component

    // Show/hide category select based on bulk action
    document.getElementById('bulk-action').addEventListener('change', function() {
        const categorySelect = document.getElementById('bulk-category-select');
        if (this.value === 'category') {
            categorySelect.classList.remove('d-none');
            categorySelect.classList.add('d-inline-block');
        } else {
            categorySelect.classList.add('d-none');
            categorySelect.classList.remove('d-inline-block');
        }
    });

    function applyBulkAction() {
        const bulkAction = document.getElementById('bulk-action').value;
        if (!bulkAction) {
            alert('Please select an action');
            return;
        }

        const selectedPosts = document.querySelectorAll('input[name="post_ids[]"]:checked');
        if (selectedPosts.length === 0) {
            alert('Please select at least one post');
            return;
        }

        const postIds = Array.from(selectedPosts).map(checkbox => checkbox.value).join(',');

        // Set the values in the hidden form fields
        document.getElementById('bulk-post-ids').value = postIds;
        document.getElementById('bulk-action-type').value = bulkAction;

        if (bulkAction === 'delete') {
            if (confirm('Are you sure you want to delete the selected posts?')) {
                document.getElementById('bulk-action-form').submit();
            }
        } else if (bulkAction === 'category') {
            const categoryId = document.getElementById('bulk-category').value;
            if (!categoryId) {
                alert('Please select a category');
                return;
            }

            document.getElementById('bulk-category-id').value = categoryId;

            if (confirm('Are you sure you want to update the category for the selected posts?')) {
                document.getElementById('bulk-action-form').submit();
            }
        }
    }

    // Select all checkbox functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="post_ids[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    </script>

<?php include 'includes/footer.php'; ?>
