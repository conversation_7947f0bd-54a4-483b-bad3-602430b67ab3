/**
 * Admin Tables CSS - Modern Overhaul
 *
 * This file contains enhanced table styles for the admin panel.
 */

/* Base Table */
.table {
  width: 100%;
  margin-bottom: 24px;
  color: #333;
  vertical-align: top;
  border-color: #e0e0e0;
  border-collapse: separate;
  border-spacing: 0;
}

.table > :not(caption) > * > * {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.table > thead {
  vertical-align: bottom;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table > tbody {
  vertical-align: inherit;
}

/* Table Head */
.table thead th {
  font-weight: 600;
  color: #333;
  background-color: #f5f7fa;
  font-size: 14px;
  letter-spacing: 0.3px;
  white-space: nowrap;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  transition: background-color 0.2s ease;
}

.table thead th:hover {
  background-color: #eef1f6;
}

/* Table Row Hover */
.table-hover > tbody > tr {
  transition: background-color 0.2s ease;
}

.table-hover > tbody > tr:hover {
  background-color: #f9f9f9;
}

/* Table Striped */
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Table Bordered */
.table-bordered {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-bordered > :not(caption) > * > * {
  border: 1px solid #e0e0e0;
}

/* Table Borderless */
.table-borderless > :not(caption) > * > * {
  border-bottom: 0;
}

.table-borderless > :not(:first-child) {
  border-top: 0;
}

/* Table Small */
.table-sm > :not(caption) > * > * {
  padding: 10px 14px;
}

/* Table Responsive */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 0; /* Remove bottom margin to avoid double spacing */
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* News Table Specific Styles */
.news-title-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.news-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.news-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-title-area:hover .news-thumbnail {
  transform: translateY(-2px);
}

.news-title-area:hover .news-thumbnail img {
  transform: scale(1.05);
}

.news-info {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.news-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  color: #333;
  transition: color 0.2s ease;
}

.news-title:hover {
  color: var(--primary-color);
}

.news-actions-inline {
  font-size: 13px;
  color: #666;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.news-actions-inline .action-link {
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
}

.news-actions-inline .action-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.news-category {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.news-category:hover {
  background-color: rgba(var(--primary-rgb), 0.15);
}

.news-date {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-display {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.time-display {
  color: #666;
  font-size: 13px;
}

.news-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

/* Table Variants */
.table-primary,
.table-primary > th,
.table-primary > td {
  background-color: rgba(var(--primary-rgb), 0.1);
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
  background-color: rgba(44, 62, 80, 0.1);
}

.table-success,
.table-success > th,
.table-success > td {
  background-color: rgba(16, 185, 129, 0.1);
}

.table-danger,
.table-danger > th,
.table-danger > td {
  background-color: rgba(239, 68, 68, 0.1);
}

.table-warning,
.table-warning > th,
.table-warning > td {
  background-color: rgba(245, 158, 11, 0.1);
}

.table-info,
.table-info > th,
.table-info > td {
  background-color: rgba(59, 130, 246, 0.1);
}

.table-light,
.table-light > th,
.table-light > td {
  background-color: #f8f9fa;
}

.table-dark,
.table-dark > th,
.table-dark > td {
  background-color: #343a40;
  color: #fff;
}

/* Table Dark */
.table-dark {
  color: #fff;
  background-color: #343a40;
}

.table-dark > :not(caption) > * > * {
  border-color: rgba(255, 255, 255, 0.1);
}

.table-dark.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-dark.table-hover > tbody > tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

/* Table Caption */
.table-caption-top caption {
  caption-side: top;
}

/* Table Actions */
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

.table-action-btn {
  width: 36px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: #f5f5f5;
  color: #555;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.table-action-btn:hover {
  background-color: #e0e0e0;
}

.table-action-btn.edit:hover {
  background-color: var(--primary-color);
  color: white;
}

.table-action-btn.delete:hover {
  background-color: var(--danger-color);
  color: white;
}

.table-action-btn.view:hover {
  background-color: var(--info-color);
  color: white;
}

/* Table Status */
.table-status {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
}

.table-status.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.table-status.inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.table-status.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.table-status.draft {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* Table Checkbox */
.table-checkbox {
  width: 40px;
  text-align: center;
}

.table-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #ccc;
  position: relative;
  appearance: none;
  background-color: #fff;
  transition: all 0.2s ease;
}

.table-checkbox input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.table-checkbox input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Table Sortable */
.table-sortable th {
  cursor: pointer;
  position: relative;
  padding-right: 30px;
}

.table-sortable th::after {
  content: "";
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.table-sortable th:hover::after {
  opacity: 1;
}

.table-sortable th.sort-asc::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='18 15 12 9 6 15'%3E%3C/polyline%3E%3C/svg%3E");
  opacity: 1;
}

.table-sortable th.sort-desc::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  opacity: 1;
}

/* Table Pagination */
.table-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24px;
  padding: 16px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-pagination-info {
  font-size: 14px;
  color: #666;
}

.table-pagination-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-pagination-btn {
  min-width: 36px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  border-radius: 8px;
  background-color: #fff;
  color: #333;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.table-pagination-btn:hover {
  background-color: #f5f5f5;
}

.table-pagination-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  font-weight: 600;
}

.table-pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Table Filter */
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.table-filter-select {
  min-width: 150px;
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

.table-filter-search {
  margin-left: auto;
  position: relative;
}

.table-filter-search input {
  width: 250px;
  padding: 10px 14px 10px 40px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.table-filter-search::before {
  content: "";
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

/* Table Empty */
.table-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-empty-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 20px;
}

.table-empty-text {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.table-empty-subtext {
  font-size: 14px;
  color: #666;
  max-width: 400px;
  margin: 0 auto;
}

/* Responsive Tables */
/* Table Column Widths */
.title-column {
  min-width: 300px;
}

.category-column {
  width: 150px;
}

.date-column {
  width: 150px;
}

.actions-column {
  width: 120px;
  text-align: right;
}

/* WordPress Style Table */
.admin-table.wp-style {
  border-collapse: collapse;
}

.admin-table.wp-style th {
  font-weight: 600;
  text-align: left;
}

.admin-table.wp-style .check-column {
  width: 2.2em;
  padding: 8px 0 0 3px;
  vertical-align: top;
}

.admin-table.wp-style .row-actions {
  visibility: hidden;
  font-size: 13px;
  color: #666;
  padding: 2px 0 0;
}

.admin-table.wp-style tr:hover .row-actions {
  visibility: visible;
}

@media (max-width: 768px) {
  /* Make all tables responsive by default */
  table, .table, .admin-table {
    width: 100% !important;
  }

  /* Show row actions on mobile */
  .admin-table.wp-style .row-actions,
  .row-actions {
    visibility: visible !important;
  }

  /* Mobile Card View - Universal for all tables */
  table, .table, .admin-table,
  .mobile-card-view,
  .table-responsive-card {
    border: 0 !important;
    background-color: transparent !important;
    width: 100% !important;
    margin-bottom: 0 !important;
    box-shadow: none !important;
  }

  table thead, .table thead, .admin-table thead,
  .mobile-card-view thead,
  .table-responsive-card thead {
    display: none !important;
  }

  table tbody tr, .table tbody tr, .admin-table tbody tr,
  .mobile-card-view tbody tr,
  .table-responsive-card tr {
    display: block !important;
    margin-bottom: 1.5rem !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    background-color: #fff !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid #eee !important;
  }

  /* Add title to each card */
  table tbody tr:before, .table tbody tr:before, .admin-table tbody tr:before,
  .mobile-card-view tbody tr:before {
    content: attr(data-title);
    display: block !important;
    padding: 12px 15px !important;
    background-color: #f5f7fa !important;
    color: #333 !important;
    font-weight: 600 !important;
    border-bottom: 1px solid #eee !important;
    font-size: 16px !important;
  }

  /* Style each cell as a row in the card */
  table td, .table td, .admin-table td,
  .mobile-card-view td,
  .table-responsive-card td {
    display: flex !important;
    padding: 12px 15px !important;
    text-align: left !important;
    border-bottom: 1px solid #f0f0f0 !important;
    align-items: center !important;
    min-height: 30px !important;
    position: relative !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  table td:last-child, .table td:last-child, .admin-table td:last-child,
  .mobile-card-view td:last-child,
  .table-responsive-card td:last-child {
    border-bottom: none !important;
  }

  /* Add label to each cell */
  table td:before, .table td:before, .admin-table td:before,
  .mobile-card-view td:before,
  .table-responsive-card td::before {
    content: attr(data-label);
    width: 40% !important;
    font-weight: 600 !important;
    color: #555 !important;
    padding-right: 15px !important;
    text-align: left !important;
    flex-shrink: 0 !important;
  }

  /* Special column handling */
  .check-column,
  .mobile-card-view .check-column {
    position: absolute !important;
    top: 12px !important;
    right: 15px !important;
    border: none !important;
    z-index: 10 !important;
    background: transparent !important;
  }

  .check-column:before,
  .mobile-card-view .check-column:before {
    display: none !important;
  }

  .title-column,
  .mobile-card-view .title-column {
    border-top: none !important;
  }

  .actions-column,
  .mobile-card-view .actions-column,
  .table-responsive-card .actions-column {
    justify-content: flex-start !important;
  }

  .actions-column:before,
  .mobile-card-view .actions-column:before,
  .table-responsive-card .actions-column:before {
    display: none !important;
  }

  .actions-column .news-actions,
  .actions-column .actions,
  .mobile-card-view .actions-column .news-actions,
  .mobile-card-view .actions-column .actions,
  .table-responsive-card .table-actions {
    justify-content: flex-start !important;
    width: 100% !important;
  }

  /* Hide certain columns on mobile */
  .check-column,
  .mobile-card-view .check-column {
    display: none !important;
  }

  /* Adjust row actions */
  .row-actions,
  .mobile-card-view .row-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 5px !important;
    margin-top: 8px !important;
    visibility: visible !important;
  }

  .row-actions span,
  .mobile-card-view .row-actions span {
    display: block !important;
  }

  /* Fix for nested tables */
  table table, .table table, .admin-table table,
  .mobile-card-view table {
    display: block !important;
    width: 100% !important;
  }

  /* Fix for images in cards */
  table img, .table img, .admin-table img,
  .mobile-card-view img {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Fix for buttons in cards */
  table .admin-btn, .table .admin-btn, .admin-table .admin-btn,
  table button, .table button, .admin-table button,
  .mobile-card-view .admin-btn,
  .mobile-card-view button {
    margin: 2px !important;
  }

  /* Fix for post thumbnails */
  .post-thumbnail, .news-thumbnail,
  .mobile-card-view .post-thumbnail,
  .mobile-card-view .news-thumbnail {
    width: 50px !important;
    height: 50px !important;
    flex-shrink: 0 !important;
  }

  /* Fix for post title area */
  .post-title-area, .news-title-area,
  .mobile-card-view .post-title-area,
  .mobile-card-view .news-title-area {
    flex-direction: row !important;
    align-items: center !important;
  }

  /* Fix for pagination */
  .table-pagination,
  .admin-table-pagination {
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
  }

  .table-pagination-info,
  .admin-table-pagination-info {
    margin-bottom: 10px !important;
    text-align: center !important;
    width: 100% !important;
  }

  .table-pagination-actions,
  .admin-table-pagination-controls {
    justify-content: center !important;
    width: 100% !important;
  }

  /* Fix for table filters and bulk actions */
  .admin-table-actions,
  .table-filter {
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100% !important;
  }

  .admin-table-filters,
  .table-filter-item {
    width: 100% !important;
    margin-bottom: 10px !important;
  }

  .admin-table-bulk-actions {
    width: 100% !important;
    flex-wrap: wrap !important;
  }

  .admin-table-bulk-actions select,
  .admin-table-bulk-actions button,
  .table-filter-select {
    width: 100% !important;
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .category-column,
  .date-column {
    display: none !important;
  }

  .news-title,
  .post-info strong {
    max-width: 200px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
}
