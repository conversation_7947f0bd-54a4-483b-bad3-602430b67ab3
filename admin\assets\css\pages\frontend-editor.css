/**
 * Frontend Editor CSS
 *
 * Styles for the frontend editor functionality
 */

/* Editor Container */
.editor-container {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 500px;
    border: 1px solid var(--border-color, #dce0e3);
    border-radius: var(--radius-md, 4px);
    overflow: hidden;
}

.editor-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    min-height: 400px;
}

.wysiwyg-editor {
    flex: 1;
    overflow: auto;
    padding: 20px;
    border: none;
    outline: none;
    min-height: 400px;
    height: auto;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Make sure content stays within the editor */
.wysiwyg-editor img {
    max-width: 100%;
    height: auto;
}

.wysiwyg-editor table {
    max-width: 100%;
    overflow-x: auto;
    display: block;
}

.wysiwyg-editor pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 100%;
    overflow-x: auto;
}

.wysiwyg-editor iframe {
    max-width: 100%;
}

/* Card body padding fix */
.card-body.p-0 .editor-container {
    border: none;
    border-radius: 0;
}

/* Card footer spacing */
.card-footer {
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Version comment input */
.version-comment {
    flex: 1;
    margin-right: 15px;
}

/* Editor actions */
.editor-actions {
    display: flex;
    gap: 10px;
}

/* Simple Editor Styles */
.simple-editor-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.simple-editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.simple-editor-toolbar-group {
    display: flex;
    gap: 2px;
    margin-right: 10px;
}

.simple-editor-toolbar-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
}

.simple-editor-toolbar-button:hover {
    background-color: #f0f0f0;
}

.simple-editor-content {
    padding: 15px;
    min-height: 300px;
    overflow-y: auto;
    background-color: #fff;
}

/* Directory navigation */
.directory-navigation {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.directory-breadcrumb {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
    font-size: 14px;
}

.directory-breadcrumb span {
    color: #6c757d;
    margin-right: 5px;
}

.directory-breadcrumb a {
    color: #2c3e50;
    text-decoration: none;
    padding: 3px 6px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.directory-breadcrumb a:hover {
    background-color: #e9ecef;
    color: #f1ca2f;
}

/* File list */
.file-list {
    margin-top: 15px;
}

.file-list .admin-table {
    border-collapse: collapse;
    width: 100%;
}

.file-list .admin-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: left;
    padding: 12px 15px;
    border-bottom: 2px solid #dee2e6;
}

.file-list .admin-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.file-list .admin-table tr:hover {
    background-color: #f8f9fa;
}

.file-list .admin-table a {
    color: #2c3e50;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-list .admin-table a:hover {
    color: #f1ca2f;
}

.file-list .admin-table i {
    color: #6c757d;
    width: 16px;
    text-align: center;
}

.file-list .admin-table a:hover i {
    color: #f1ca2f;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 5px;
}

.admin-btn.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* File editor */
.code-editor {
    width: 100%;
    min-height: 400px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: 15px;
    border: 1px solid #dce0e3;
    border-radius: 4px;
    background-color: #f8f9fa;
    color: #333;
    resize: vertical;
}

/* Image preview */
.image-preview {
    margin-bottom: 20px;
    text-align: center;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

/* Content-only notice styling */
.content-only-notice {
    background-color: #f8f9fa;
    border-left: 4px solid #17a2b8;
    padding: 10px 15px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
}

.content-only-notice i {
    color: #17a2b8;
    font-size: 18px;
    margin-right: 10px;
}

.form-hint {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

/* Card footer */
.admin-card-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: 4px solid transparent;
    position: relative;
    display: flex;
    align-items: center;
    transition: opacity 0.5s ease;
}

.alert i:first-child {
    margin-right: 10px;
    font-size: 16px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.close-alert {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: inherit;
    opacity: 0.7;
    cursor: pointer;
    padding: 5px;
    font-size: 14px;
}

.close-alert:hover {
    opacity: 1;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 8px;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner i {
    font-size: 24px;
    color: #f1ca2f;
}

.loading-spinner span {
    font-size: 14px;
    color: #2c3e50;
}

/* Dynamic file styling */
.compact-file-selector option.dynamic-file {
    color: #dc3545;
    font-weight: 500;
    background-color: #fff3cd;
}

/* Dynamic file warning */
.dynamic-file-warning {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 10px 15px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
    color: #856404;
    display: flex;
    align-items: center;
}

.dynamic-file-warning i {
    color: #ffc107;
    font-size: 18px;
    margin-right: 10px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-dialog {
    margin: 50px auto;
    max-width: 800px;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
}

.close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Version Preview */
.version-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.code-preview {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    max-height: 400px;
    overflow: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Loading Overlay */
#loadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
    .directory-breadcrumb {
        font-size: 13px;
    }

    .file-list .admin-table {
        display: block;
        overflow-x: auto;
    }

    .admin-card-footer {
        flex-direction: column;
    }

    .admin-card-footer .admin-btn {
        width: 100%;
        text-align: center;
    }

    .code-editor {
        font-size: 13px;
    }

    .modal-dialog {
        margin: 20px;
        max-width: calc(100% - 40px);
    }

    .card-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .version-comment {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .editor-actions {
        justify-content: flex-end;
    }
}
