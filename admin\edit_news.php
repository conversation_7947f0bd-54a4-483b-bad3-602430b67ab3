<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('dashboard.php');
}

$id = intval($_GET['id']);
$error = '';
$success = '';

// Set page title
$page_title = "Edit News";

// Add CSS for the edit news page
$extra_css = '<link rel="stylesheet" href="assets/css/pages/news_editor.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/pages/edit_news.css?v=' . time() . '">';

// Get news post data using prepared statement
$sql = "SELECT * FROM news WHERE id = ?";
$stmt = $conn->prepare($sql);
if (!$stmt) {
    error_log("Failed to prepare statement: " . $conn->error);
    redirect('dashboard.php');
}

$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    redirect('dashboard.php');
}

$news = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get CSRF token if available
    $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    // Verify CSRF token if provided, otherwise proceed (for backward compatibility)
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error = "Security validation failed. Please try again.";
        error_log("CSRF token validation failed during news edit");
    } else {
        $title = sanitize($_POST['title']);
        $content = $_POST['content']; // Don't sanitize HTML content
        $slug = sanitize($_POST['slug']);
        $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;

        // If slug is empty, generate it from the title
        if (empty($slug)) {
            $slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9-]+/', '-', $title)));
        }

        // Validate input
        if (empty($title) || empty($content)) {
            $error = "Please fill in all required fields";
        } else {
            $image_name = $news['featured_image']; // Default to existing image

            // Check if new image is uploaded
            if ($_FILES['image']['size'] > 0) {
                // Handle image upload
                $target_dir = "../images/news/";

                // Check if directory exists, if not create it
                if (!file_exists($target_dir)) {
                    mkdir($target_dir, 0777, true);
                }

                // Make sure the directory is writable
                if (!is_writable($target_dir)) {
                    chmod($target_dir, 0777);
                }

                $file_name = basename($_FILES["image"]["name"]);
                $target_file = $target_dir . $file_name;
                $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
                $uploadOk = 1;

                // Detailed error checking
                if(!isset($_FILES["image"]) || $_FILES["image"]["error"] > 0) {
                    $error = "File upload error: " . $_FILES["image"]["error"];
                    $uploadOk = 0;
                }
                // Check if image file is a actual image
                elseif ($_FILES["image"]["tmp_name"] == "" || !file_exists($_FILES["image"]["tmp_name"])) {
                    $error = "No file was uploaded or temporary file doesn't exist.";
                    $uploadOk = 0;
                }
                else {
                    $check = getimagesize($_FILES["image"]["tmp_name"]);
                    if ($check === false) {
                        $error = "File is not an image.";
                        $uploadOk = 0;
                    }
                    // Check file size (limit to 5MB)
                    elseif ($_FILES["image"]["size"] > 5000000) {
                        $error = "Sorry, your file is too large.";
                        $uploadOk = 0;
                    }
                    // Allow certain file formats
                    elseif ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif") {
                        $error = "Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
                        $uploadOk = 0;
                    }
                }

                // If everything is ok, try to upload file
                if ($uploadOk == 1) {
                    // Generate unique filename to prevent overwriting
                    $new_filename = uniqid() . '.' . $imageFileType;
                    $target_file = $target_dir . $new_filename;

                    if (move_uploaded_file($_FILES["image"]["tmp_name"], $target_file)) {
                        // Delete old image if it exists
                        if (file_exists($target_dir . $news['featured_image'])) {
                            unlink($target_dir . $news['featured_image']);
                        }

                        $image_name = $new_filename;
                    } else {
                        $error = "Sorry, there was an error uploading your file. Error code: " . $_FILES["image"]["error"] .
                                ". Target path: " . $target_file .
                                ". Is writable: " . (is_writable($target_dir) ? 'Yes' : 'No');
                    }
                }
            }

            if (empty($error)) {
                // Update database using prepared statement
                $sql = "UPDATE news SET title = ?, content = ?, slug = ?, featured_image = ?, category_id = ? WHERE id = ?";

                $stmt = $conn->prepare($sql);
                if (!$stmt) {
                    $error = "Failed to prepare statement: " . $conn->error;
                } else {
                    $stmt->bind_param("ssssii", $title, $content, $slug, $image_name, $category_id, $id);

                    if ($stmt->execute()) {
                        $success = "News post updated successfully!";

                        // Update sitemap
                        if (function_exists('afterNewsOperation')) {
                            afterNewsOperation();
                        }

                        // Refresh news data using prepared statement
                        $refresh_sql = "SELECT * FROM news WHERE id = ?";
                        $refresh_stmt = $conn->prepare($refresh_sql);
                        $refresh_stmt->bind_param("i", $id);
                        $refresh_stmt->execute();
                        $result = $refresh_stmt->get_result();
                        $news = $result->fetch_assoc();
                    } else {
                        $error = "Error updating news: " . $stmt->error;
                    }
                }
            }
        }
    }
}
?>
<?php
// Get categories
$categories_sql = "SELECT * FROM categories ORDER BY name ASC";
$categories_result = $conn->query($categories_sql);

// Get current category
$category_id = $news['category_id'] ?? 0;
?>
<?php
// Add page-specific class to body
$body_class = 'page-edit_news';
include 'includes/header.php';
?>

    <div class="admin-container">
        <?php
        // Set up variables for enhanced header
        $page_icon = 'fas fa-edit';
        $page_subtitle = 'Editing: ' . htmlspecialchars($news['title']);
        $back_link = ['url' => 'all_news.php', 'text' => 'Back to News'];
        $primary_action = [
            'url' => '../' . (isset($news['slug']) && !empty($news['slug']) ? $news['slug'] . '.html' : 'news-detail.html?id=' . $id),
            'icon' => 'fas fa-eye',
            'text' => 'Preview',
            'target' => '_blank',
            'class' => 'outline'
        ];

        // Include the content header
        include 'includes/content-header.php';
        ?>

        <?php if (!empty($error) || !empty($success)): ?>
            <div class="admin-alerts-container">
                <?php if (!empty($error)): ?>
                    <div class="admin-alert error">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                        <button type="button" class="admin-alert-close"><i class="fas fa-times"></i></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="admin-alert success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        <button type="button" class="admin-alert-close"><i class="fas fa-times"></i></button>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <form class="admin-form news-editor-form" method="post" action="" enctype="multipart/form-data">
            <!-- CSRF Protection -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

            <div class="admin-content-grid">
                <!-- Main Content Area -->
                <div class="admin-content-main">
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-heading"></i> Post Details</h3>
                            <div class="admin-card-meta">
                                <span class="admin-card-meta-item">
                                    <i class="fas fa-calendar"></i> Created: <?php echo date('M d, Y', strtotime($news['created_at'])); ?>
                                </span>
                                <?php if (isset($news['updated_at']) && $news['updated_at'] != $news['created_at']): ?>
                                <span class="admin-card-meta-item">
                                    <i class="fas fa-edit"></i> Updated: <?php echo date('M d, Y', strtotime($news['updated_at'])); ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="title" class="form-label">Title <span class="required">*</span></label>
                                <input type="text" id="title" name="title" class="form-control" value="<?php echo htmlspecialchars($news['title']); ?>" placeholder="Enter news title" required>
                            </div>

                            <div class="form-group">
                                <label for="slug" class="form-label">Slug (for SEO-friendly URLs)</label>
                                <div class="input-group">
                                    <span class="input-group-text">/news/</span>
                                    <input type="text" id="slug" name="slug" class="form-control" value="<?php echo isset($news['slug']) ? htmlspecialchars($news['slug']) : ''; ?>" placeholder="my-news-title">
                                </div>
                                <div class="form-text">Use only letters, numbers, and hyphens. Leave empty to generate from title.</div>
                            </div>

                            <div class="form-group">
                                <label for="content" class="form-label">Content <span class="required">*</span></label>
                                <textarea id="content" name="content" class="wysiwyg-editor" placeholder="Enter news content" required><?php echo htmlspecialchars($news['content']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="admin-content-sidebar">
                    <!-- Publish Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-paper-plane"></i> Publish</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-actions">
                                <button type="submit" class="admin-btn primary btn-lg btn-block">
                                    <i class="fas fa-save"></i> Update Post
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Category Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-folder"></i> Category</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="category_id" class="form-label">Select Category</label>
                                <select id="category_id" name="category_id" class="form-select">
                                    <option value="0">-- Select Category --</option>
                                    <?php if ($categories_result && $categories_result->num_rows > 0): ?>
                                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image Card -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 class="admin-card-title"><i class="fas fa-image"></i> Featured Image</h3>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group">
                                <label for="image" class="form-label">Current Image</label>
                                <div class="image-preview-container">
                                    <div class="image-preview">
                                        <?php if (!empty($news['featured_image'])): ?>
                                            <img src="../images/news/<?php echo htmlspecialchars($news['featured_image']); ?>" alt="<?php echo htmlspecialchars($news['title']); ?>">
                                        <?php else: ?>
                                            <div class="image-preview-empty">
                                                <i class="fas fa-image"></i>
                                                <p>No image selected</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="custom-file-upload">
                                        <input type="file" id="image" name="image" class="custom-file-input">
                                        <label for="image" class="custom-file-label">Change image</label>
                                    </div>
                                </div>
                                <div class="form-text">Leave empty to keep current image. Max file size: 5MB.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Include our custom WYSIWYG editor -->
    <script src="js/editor.js?v=<?php echo time(); ?>"></script>
    <script src="js/editor-mobile.js?v=<?php echo time(); ?>"></script>

    <!-- Include image upload and form validation -->
    <script src="js/image-upload.js?v=<?php echo time(); ?>"></script>
    <script src="js/news-form-validation.js?v=<?php echo time(); ?>"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the editor
            new SimpleEditor('.wysiwyg-editor', {
                height: 400,
                placeholder: 'Enter news content here...'
            });

            // Auto-generate slug from title
            const titleInput = document.getElementById('title');
            const slugInput = document.getElementById('slug');

            // Function to generate slug
            function generateSlug(text) {
                return text
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-')      // Replace multiple hyphens with single hyphen
                    .trim();                  // Trim whitespace
            }

            // Generate slug on input (while typing)
            titleInput.addEventListener('input', function() {
                // Only generate slug if the slug field is empty or hasn't been manually edited
                if (slugInput.dataset.userEdited !== 'true') {
                    slugInput.value = generateSlug(this.value);
                }
            });

            // Also handle blur event for compatibility
            titleInput.addEventListener('blur', function() {
                // Only generate slug if the slug field is empty
                if (slugInput.value === '') {
                    slugInput.value = generateSlug(this.value);
                }
            });

            // Mark slug as user-edited when user types in it
            slugInput.addEventListener('input', function() {
                this.dataset.userEdited = 'true';
            });

            // If slug is cleared, reset the user-edited flag
            slugInput.addEventListener('blur', function() {
                if (this.value === '') {
                    this.dataset.userEdited = 'false';
                    // Generate from current title
                    if (titleInput.value !== '') {
                        this.value = generateSlug(titleInput.value);
                    }
                }
            });

            // Handle alert close buttons
            const alertCloseButtons = document.querySelectorAll('.admin-alert-close');
            alertCloseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const alert = this.closest('.admin-alert');
                    if (alert) {
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            alert.style.display = 'none';
                        }, 300);
                    }
                });
            });

            // Handle file input change to show file name
            const fileInput = document.getElementById('image');
            const fileLabel = document.querySelector('.custom-file-label');

            if (fileInput && fileLabel) {
                fileInput.addEventListener('change', function() {
                    if (this.files && this.files.length > 0) {
                        fileLabel.textContent = this.files[0].name;
                    } else {
                        fileLabel.textContent = 'Change image';
                    }
                });
            }
        });
    </script>

<?php include 'includes/footer.php'; ?>
