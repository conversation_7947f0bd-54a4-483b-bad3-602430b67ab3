/**
 * Contact Form Handler
 *
 * This script handles the submission of the main contact form via AJAX.
 * It validates the form inputs and displays success/error messages with auto-dismiss.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find the contact form
    const contactForm = document.querySelector('.contact-form');

    // Check for URL parameters that might indicate form submission status
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('form_status')) {
        const status = urlParams.get('form_status');
        if (status === 'success') {
            showMessage('success', '<i class="fas fa-check-circle"></i> Thank you for your message. We\'ll get back to you soon!');
        } else if (status === 'error') {
            showMessage('error', '<i class="fas fa-exclamation-circle"></i> An error occurred while sending your message. Please try again later.');
        }
    }

    // Auto-dismiss success messages after 5 seconds
    const successMessage = document.querySelector('.contact-success-message');
    if (successMessage && successMessage.style.display !== 'none') {
        // Add transition styles
        successMessage.style.transition = 'opacity 0.5s ease-in-out';

        // Set timeout to hide the message
        setTimeout(() => {
            successMessage.style.opacity = '0';
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 500);
        }, 5000);
    }

    // Auto-dismiss error messages after 8 seconds
    const errorMessage = document.querySelector('.contact-error-message');
    if (errorMessage && errorMessage.style.display !== 'none') {
        // Add transition styles
        errorMessage.style.transition = 'opacity 0.5s ease-in-out';

        // Set timeout to hide the message
        setTimeout(() => {
            errorMessage.style.opacity = '0';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 500);
        }, 8000);
    }

    // If form exists, add AJAX handling
    if (contactForm) {
        // Create message elements if they don't exist
        if (!document.querySelector('.contact-success-message')) {
            const successDiv = document.createElement('div');
            successDiv.className = 'contact-success-message';
            successDiv.style.display = 'none';
            successDiv.style.backgroundColor = '#d4edda';
            successDiv.style.color = '#155724';
            successDiv.style.padding = '15px';
            successDiv.style.marginBottom = '20px';
            successDiv.style.borderRadius = '4px';
            successDiv.style.transition = 'opacity 0.5s ease-in-out';
            contactForm.parentNode.insertBefore(successDiv, contactForm);
        }

        if (!document.querySelector('.contact-error-message')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'contact-error-message';
            errorDiv.style.display = 'none';
            errorDiv.style.backgroundColor = '#f8d7da';
            errorDiv.style.color = '#721c24';
            errorDiv.style.padding = '15px';
            errorDiv.style.marginBottom = '20px';
            errorDiv.style.borderRadius = '4px';
            errorDiv.style.transition = 'opacity 0.5s ease-in-out';
            contactForm.parentNode.insertBefore(errorDiv, contactForm);
        }

        if (!document.querySelector('.contact-info-message')) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'contact-info-message';
            infoDiv.style.display = 'none';
            infoDiv.style.backgroundColor = '#d1ecf1';
            infoDiv.style.color = '#0c5460';
            infoDiv.style.padding = '15px';
            infoDiv.style.marginBottom = '20px';
            infoDiv.style.borderRadius = '4px';
            infoDiv.style.transition = 'opacity 0.5s ease-in-out';
            contactForm.parentNode.insertBefore(infoDiv, contactForm);
        }

        // Add submit event listener
        contactForm.addEventListener('submit', function(e) {
            // Only prevent default if we're using AJAX
            if (window.location.href.includes('.html')) {
                e.preventDefault();
            } else {
                // If we're not using AJAX, let the form submit normally
                return true;
            }

            // Basic validation
            const name = contactForm.querySelector('input[name="name"]').value.trim();
            const email = contactForm.querySelector('input[name="email"]').value.trim();
            const message = contactForm.querySelector('textarea[name="message"]').value.trim();

            if (!name || !email || !message) {
                showMessage('error', '<i class="fas fa-exclamation-circle"></i> Please fill in all required fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('error', '<i class="fas fa-exclamation-circle"></i> Please enter a valid email address.');
                return;
            }

            // Show sending message
            showMessage('info', '<i class="fas fa-info-circle"></i> Sending your message...');

            // Create form data
            const formData = new FormData(contactForm);
            formData.append('source', 'Contact Page Form');

            // Determine the correct path to process-contact.php
            let actionUrl = contactForm.getAttribute('action');
            if (!actionUrl) {
                actionUrl = '../process-contact.php';
            }

            // Send AJAX request
            fetch(actionUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', '<i class="fas fa-check-circle"></i> ' + data.message);
                    contactForm.reset();
                } else {
                    showMessage('error', '<i class="fas fa-exclamation-circle"></i> ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('error', '<i class="fas fa-exclamation-circle"></i> An error occurred while sending your message. Please try again later.');
            });
        });
    }

    // Function to show message with auto-dismiss for success messages
    function showMessage(type, text) {
        const successMsg = document.querySelector('.contact-success-message');
        const errorMsg = document.querySelector('.contact-error-message');
        const infoMsg = document.querySelector('.contact-info-message');

        // Clear any existing timeout
        if (window.contactMessageTimeout) {
            clearTimeout(window.contactMessageTimeout);
        }

        // Hide all messages first
        successMsg.style.display = 'none';
        errorMsg.style.display = 'none';
        infoMsg.style.display = 'none';

        // Reset opacity
        successMsg.style.opacity = '1';
        errorMsg.style.opacity = '1';
        infoMsg.style.opacity = '1';

        let messageElement;

        if (type === 'success') {
            messageElement = successMsg;
        } else if (type === 'error') {
            messageElement = errorMsg;
        } else if (type === 'info') {
            messageElement = infoMsg;
        }

        if (messageElement) {
            messageElement.innerHTML = text;
            messageElement.style.display = 'block';

            // Scroll to message
            window.scrollTo({
                top: messageElement.offsetTop - 100,
                behavior: 'smooth'
            });

            // Auto-dismiss after 5 seconds for success messages
            if (type === 'success') {
                window.contactMessageTimeout = setTimeout(() => {
                    messageElement.style.opacity = '0';
                    setTimeout(() => {
                        messageElement.style.display = 'none';
                    }, 500);
                }, 5000);
            }
        }
    }
});
