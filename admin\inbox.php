<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage inbox
if (!$permissions->hasPermission('manage_inbox')) {
    $_SESSION['error_message'] = "You do not have permission to access the Inbox.";
    header('Location: dashboard.php');
    exit;
}

// Set page title
$page_title = 'Inbox';
$body_class = 'inbox-page';

// Initialize variables
$submissions = [];
$notifications = [];
$all_messages = [];
$single_submission = null;
$single_notification = null;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$type = isset($_GET['type']) ? $_GET['type'] : 'all'; // all, contact, notification

// Get items per page from URL, user settings, or default
if (isset($_GET['per_page']) && is_numeric($_GET['per_page'])) {
    $items_per_page = (int)$_GET['per_page'];
    // Ensure it's a valid value
    $valid_per_page_values = [5, 10, 25, 50, 100];
    if (!in_array($items_per_page, $valid_per_page_values)) {
        $items_per_page = 10; // Default if invalid
    }
} else {
    // Get user's items_per_page setting if available
    $items_per_page = 10; // Default value
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $settings_query = "SELECT items_per_page FROM user_settings WHERE user_id = $user_id";
        $settings_result = $conn->query($settings_query);

        if ($settings_result && $settings_result->num_rows > 0) {
            $user_settings = $settings_result->fetch_assoc();
            $items_per_page = (int)$user_settings['items_per_page'];
        }
    }
}

$offset = ($page - 1) * $items_per_page;
$total_submissions = 0;
$total_notifications = 0;
$total_messages = 0;
$unread_count = 0;
$success_message = '';
$error_message = '';

// Get the base URL for the admin panel
$admin_base_url = '';
if (isset($_SERVER['HTTP_HOST'])) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $script_name = isset($_SERVER['SCRIPT_NAME']) ? $_SERVER['SCRIPT_NAME'] : '';
    $admin_base_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . dirname($script_name);

    // Fix the URL if it contains double slashes
    $admin_base_url = rtrim($admin_base_url, '/');
}

// Include header
require_once 'includes/header.php';

// Handle marking as read
if (isset($_GET['mark_read']) && isset($_GET['id'])) {
    // Get CSRF token if available
    $csrf_token = isset($_GET['csrf_token']) ? $_GET['csrf_token'] : '';

    // Verify CSRF token if provided, otherwise proceed (for backward compatibility)
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $submission_id = (int)$_GET['id'];

        // Use prepared statement
        $update_sql = "UPDATE `contact_submissions` SET `is_read` = 1 WHERE `id` = ?";
        $stmt = $conn->prepare($update_sql);

        if ($stmt) {
            $stmt->bind_param("i", $submission_id);
            if ($stmt->execute()) {
                $success_message = "Message marked as read.";
                $stmt->close();

                // Redirect to the submission view
                header("Location: inbox.php?id=$submission_id");
                exit;
            } else {
                $error_message = "Failed to mark message as read: " . $stmt->error;
                $stmt->close();
            }
        } else {
            $error_message = "Failed to prepare statement: " . $conn->error;
        }
    }
}

// Handle marking as unread
if (isset($_GET['mark_unread']) && isset($_GET['id'])) {
    // Get CSRF token if available
    $csrf_token = isset($_GET['csrf_token']) ? $_GET['csrf_token'] : '';

    // Verify CSRF token if provided
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $submission_id = (int)$_GET['id'];

        // Use prepared statement
        $update_sql = "UPDATE `contact_submissions` SET `is_read` = 0 WHERE `id` = ?";
        $stmt = $conn->prepare($update_sql);

        if ($stmt) {
            $stmt->bind_param("i", $submission_id);
            if ($stmt->execute()) {
                $success_message = "Message marked as unread.";
                $stmt->close();

                // Redirect to the inbox
                header("Location: inbox.php");
                exit;
            } else {
                $error_message = "Failed to mark message as unread: " . $stmt->error;
                $stmt->close();
            }
        } else {
            $error_message = "Failed to prepare statement: " . $conn->error;
        }
    }
}

// Handle deleting submission
if (isset($_GET['delete']) && isset($_GET['id'])) {
    // Get CSRF token if available
    $csrf_token = isset($_GET['csrf_token']) ? $_GET['csrf_token'] : '';

    // Verify CSRF token if provided
    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $submission_id = (int)$_GET['id'];

        // Use prepared statement
        $delete_sql = "DELETE FROM `contact_submissions` WHERE `id` = ?";
        $stmt = $conn->prepare($delete_sql);

        if ($stmt) {
            $stmt->bind_param("i", $submission_id);
            if ($stmt->execute()) {
                $success_message = "Message deleted successfully.";
                $stmt->close();

                // Redirect to the list view after deletion
                header("Location: inbox.php");
                exit;
            } else {
                $error_message = "Failed to delete message: " . $stmt->error;
                $stmt->close();
            }
        } else {
            $error_message = "Failed to prepare statement: " . $conn->error;
        }
    }
}

// Handle bulk actions
if (isset($_POST['bulk_action']) && isset($_POST['selected_messages'])) {
    $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    if (!empty($csrf_token) && !validate_csrf_token($csrf_token)) {
        $error_message = "Security validation failed. Please try again.";
    } else {
        $action = $_POST['bulk_action'];
        $selected_ids = $_POST['selected_messages'];

        if (!empty($selected_ids) && is_array($selected_ids)) {
            $success_count = 0;

            switch ($action) {
                case 'mark_read':
                    foreach ($selected_ids as $id) {
                        $id = (int)$id;
                        $stmt = $conn->prepare("UPDATE `contact_submissions` SET `is_read` = 1 WHERE `id` = ?");
                        if ($stmt) {
                            $stmt->bind_param("i", $id);
                            if ($stmt->execute()) {
                                $success_count++;
                            }
                            $stmt->close();
                        }
                    }
                    $success_message = "$success_count messages marked as read.";
                    break;

                case 'mark_unread':
                    foreach ($selected_ids as $id) {
                        $id = (int)$id;
                        $stmt = $conn->prepare("UPDATE `contact_submissions` SET `is_read` = 0 WHERE `id` = ?");
                        if ($stmt) {
                            $stmt->bind_param("i", $id);
                            if ($stmt->execute()) {
                                $success_count++;
                            }
                            $stmt->close();
                        }
                    }
                    $success_message = "$success_count messages marked as unread.";
                    break;

                case 'delete':
                    foreach ($selected_ids as $id) {
                        $id = (int)$id;
                        $stmt = $conn->prepare("DELETE FROM `contact_submissions` WHERE `id` = ?");
                        if ($stmt) {
                            $stmt->bind_param("i", $id);
                            if ($stmt->execute()) {
                                $success_count++;
                            }
                            $stmt->close();
                        }
                    }
                    $success_message = "$success_count messages deleted.";
                    break;
            }
        } else {
            $error_message = "No messages selected.";
        }
    }
}

// Check if viewing a single submission or notification
if (isset($_GET['id'])) {
    $item_id = (int)$_GET['id'];
    $item_type = isset($_GET['item_type']) ? $_GET['item_type'] : 'contact'; // contact or notification

    // Check database connection
    if (!$conn) {
        $error_message = "Database connection error. Please try again.";

        // Try to reconnect
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($conn->connect_error) {
            $error_message = "Database connection failed. Please contact the administrator.";
        }
    }

    if ($conn) {
        // Make sure the contact_submissions table exists
        $table_check = "SHOW TABLES LIKE 'contact_submissions'";
        $table_result = $conn->query($table_check);

        if (!$table_result) {
            $error_message = "Error checking database tables.";
        } elseif ($table_result->num_rows == 0) {
            // Create the table
            $create_table = "CREATE TABLE `contact_submissions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) NOT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `message` text NOT NULL,
                `source` varchar(50) NOT NULL DEFAULT 'Unknown',
                `is_read` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if ($conn->query($create_table)) {
                $error_message = "Contact submissions table was missing and has been created. Please try again.";
            } else {
                $error_message = "Error creating database table.";
            }
        } else {
            // Table exists, proceed with query based on item type
            try {
                if ($item_type === 'notification') {
                    // Handle notification
                    $stmt = $conn->prepare("SELECT * FROM `notifications` WHERE `id` = ?");
                    if (!$stmt) {
                        $error_message = "Database prepare error: " . $conn->error;
                        $single_notification = null;
                    } else {
                        $stmt->bind_param("i", $item_id);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if (!$result) {
                            $error_message = "Database query error: " . $stmt->error;
                            $single_notification = null;
                        } elseif ($result->num_rows > 0) {
                            $single_notification = $result->fetch_assoc();

                            // Mark as read if not already read
                            if (!$single_notification['is_read']) {
                                $update_stmt = $conn->prepare("UPDATE `notifications` SET `is_read` = 1 WHERE `id` = ?");
                                if ($update_stmt) {
                                    $update_stmt->bind_param("i", $item_id);
                                    $update_stmt->execute();
                                    $update_stmt->close();
                                    $single_notification['is_read'] = 1;
                                }
                            }
                        } else {
                            $error_message = "Notification not found with ID: " . $item_id;
                            $single_notification = null;
                        }

                        $stmt->close();
                    }
                } else {
                    // Handle contact submission (default)
                    $stmt = $conn->prepare("SELECT * FROM `contact_submissions` WHERE `id` = ?");
                    if (!$stmt) {
                        $error_message = "Database prepare error: " . $conn->error;
                        $single_submission = null;
                    } else {
                        $stmt->bind_param("i", $item_id);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if (!$result) {
                            $error_message = "Database query error: " . $stmt->error;
                            $single_submission = null;
                        } elseif ($result->num_rows > 0) {
                            $single_submission = $result->fetch_assoc();

                            // Mark as read if not already read
                            if (!$single_submission['is_read']) {
                                $update_stmt = $conn->prepare("UPDATE `contact_submissions` SET `is_read` = 1 WHERE `id` = ?");
                                if ($update_stmt) {
                                    $update_stmt->bind_param("i", $item_id);
                                    $update_stmt->execute();
                                    $update_stmt->close();
                                    $single_submission['is_read'] = 1;
                                }
                            }
                        } else {
                            $error_message = "Message not found with ID: " . $item_id;
                            $single_submission = null;
                        }

                        $stmt->close();
                    }
                }
            } catch (Exception $e) {
                $error_message = "An error occurred while retrieving the message: " . $e->getMessage();
                $single_submission = null;
                $single_notification = null;
            }
        }
    } else {
        $error_message = "Could not establish database connection.";
    }
} else {
    // Fetch both contact submissions and notifications

    // Get contact submissions
    $contact_where_clause = "";
    $contact_params = [];
    $contact_types = "";

    // Filter by read/unread status for contact submissions
    if ($filter === 'unread') {
        $contact_where_clause = "WHERE `is_read` = 0";
    } elseif ($filter === 'read') {
        $contact_where_clause = "WHERE `is_read` = 1";
    }

    // Add search functionality for contact submissions
    if (!empty($search)) {
        $search_term = "%$search%";
        if (empty($contact_where_clause)) {
            $contact_where_clause = "WHERE (`name` LIKE ? OR `email` LIKE ? OR `message` LIKE ?)";
        } else {
            $contact_where_clause .= " AND (`name` LIKE ? OR `email` LIKE ? OR `message` LIKE ?)";
        }
        $contact_params[] = $search_term;
        $contact_params[] = $search_term;
        $contact_params[] = $search_term;
        $contact_types .= "sss";
    }

    // Get notifications
    $notification_where_clause = "";
    $notification_params = [];
    $notification_types = "";

    // Filter by read/unread status for notifications
    if ($filter === 'unread') {
        $notification_where_clause = "WHERE `is_read` = 0";
    } elseif ($filter === 'read') {
        $notification_where_clause = "WHERE `is_read` = 1";
    }

    // Add search functionality for notifications
    if (!empty($search)) {
        $search_term = "%$search%";
        if (empty($notification_where_clause)) {
            $notification_where_clause = "WHERE (`title` LIKE ? OR `message` LIKE ?)";
        } else {
            $notification_where_clause .= " AND (`title` LIKE ? OR `message` LIKE ?)";
        }
        $notification_params[] = $search_term;
        $notification_params[] = $search_term;
        $notification_types .= "ss";
    }

    // Get total count for contact submissions
    if ($type === 'all' || $type === 'contact') {
        $contact_count_sql = "SELECT COUNT(*) as total FROM `contact_submissions` $contact_where_clause";

        if (!empty($contact_params)) {
            $stmt = $conn->prepare($contact_count_sql);
            if ($stmt) {
                $stmt->bind_param($contact_types, ...$contact_params);
                $stmt->execute();
                $count_result = $stmt->get_result();
                if ($count_result && $count_result->num_rows > 0) {
                    $count_row = $count_result->fetch_assoc();
                    $total_submissions = (int)$count_row['total'];
                }
                $stmt->close();
            }
        } else {
            $count_result = $conn->query($contact_count_sql);
            if ($count_result && $count_result->num_rows > 0) {
                $count_row = $count_result->fetch_assoc();
                $total_submissions = (int)$count_row['total'];
            }
        }
    }

    // Get total count for notifications
    if ($type === 'all' || $type === 'notification') {
        $notification_count_sql = "SELECT COUNT(*) as total FROM `notifications` $notification_where_clause";

        if (!empty($notification_params)) {
            $stmt = $conn->prepare($notification_count_sql);
            if ($stmt) {
                $stmt->bind_param($notification_types, ...$notification_params);
                $stmt->execute();
                $count_result = $stmt->get_result();
                if ($count_result && $count_result->num_rows > 0) {
                    $count_row = $count_result->fetch_assoc();
                    $total_notifications = (int)$count_row['total'];
                }
                $stmt->close();
            }
        } else {
            $count_result = $conn->query($notification_count_sql);
            if ($count_result && $count_result->num_rows > 0) {
                $count_row = $count_result->fetch_assoc();
                $total_notifications = (int)$count_row['total'];
            }
        }
    }

    // Calculate total messages
    $total_messages = $total_submissions + $total_notifications;

    // Get unread count
    $unread_count_sql = "SELECT COUNT(*) as unread FROM `contact_submissions` WHERE `is_read` = 0";
    $unread_result = $conn->query($unread_count_sql);
    if ($unread_result && $unread_result->num_rows > 0) {
        $unread_row = $unread_result->fetch_assoc();
        $unread_count = (int)$unread_row['unread'];
    }

    // Get contact submissions with pagination using prepared statement
    if ($type === 'all' || $type === 'contact') {
        $sql = "SELECT *, 'contact' as message_type FROM `contact_submissions` $contact_where_clause ORDER BY `created_at` DESC LIMIT ?, ?";

        if (!empty($contact_params)) {
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $contact_params[] = $offset;
                $contact_params[] = $items_per_page;
                $contact_types .= "ii";
                $stmt->bind_param($contact_types, ...$contact_params);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $submissions[] = $row;
                    }
                }
                $stmt->close();
            }
        } else {
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ii", $offset, $items_per_page);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $submissions[] = $row;
                    }
                }
                $stmt->close();
            }
        }
    }

    // Get notifications with pagination using prepared statement
    if ($type === 'all' || $type === 'notification') {
        $sql = "SELECT *, 'notification' as message_type, title as name, '' as email, '' as phone, message, created_at, is_read FROM `notifications` $notification_where_clause ORDER BY `created_at` DESC LIMIT ?, ?";

        if (!empty($notification_params)) {
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $notification_params[] = $offset;
                $notification_params[] = $items_per_page;
                $notification_types .= "ii";
                $stmt->bind_param($notification_types, ...$notification_params);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $notifications[] = $row;
                    }
                }
                $stmt->close();
            }
        } else {
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ii", $offset, $items_per_page);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $notifications[] = $row;
                    }
                }
                $stmt->close();
            }
        }
    }

    // Combine and sort all messages by date
    $all_messages = array_merge($submissions, $notifications);
    usort($all_messages, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    // Apply pagination to combined results
    $all_messages = array_slice($all_messages, 0, $items_per_page);
}

// Calculate total pages for pagination
$total_pages = ceil($total_submissions / $items_per_page);

// Set up variables for enhanced header
$page_icon = 'fas fa-inbox';
$page_subtitle = "Manage your contact form submissions. Total: $total_submissions" . ($unread_count > 0 ? ", Unread: $unread_count" : "");

// Set page title based on view
if ($single_submission) {
    $page_title = "View Message";
    $back_link = ['url' => 'inbox.php', 'text' => 'Back to Inbox'];
} else {
    if ($filter === 'unread') {
        $page_title = "Inbox - Unread Messages";
    } elseif ($filter === 'read') {
        $page_title = "Inbox - Read Messages";
    } else {
        $page_title = "Inbox";
    }

    if (!empty($search)) {
        $page_title .= " - Search: " . htmlspecialchars($search);
    }
}

// Add CSS for the inbox page
$extra_css = '<link rel="stylesheet" href="assets/css/pages/inbox.css?v=' . time() . '">';
?>

<div class="admin-container">
    <?php
    // Include the content header
    include 'includes/content-header.php';
    ?>

    <div class="inbox-wrapper">
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <?php if (isset($_GET['id'])): ?>
                <div class="admin-alert-actions">
                    <a href="<?php echo htmlspecialchars('inbox.php?id=' . $_GET['id']); ?>" class="admin-btn small">
                        <i class="fas fa-sync"></i> Try Again
                    </a>
                    <a href="<?php echo htmlspecialchars('inbox.php'); ?>" class="admin-btn small">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if ($single_submission && !$error_message): ?>
            <!-- Success message for reply -->
            <?php if (isset($_SESSION['reply_success'])): ?>
                <div class="admin-alert success">
                    <i class="fas fa-check-circle"></i> <?php echo $_SESSION['reply_success']; ?>
                    <button type="button" class="admin-alert-close" onclick="this.parentElement.style.display='none';">×</button>
                </div>
                <?php unset($_SESSION['reply_success']); ?>
            <?php endif; ?>

            <!-- Error message for reply -->
            <?php if (isset($_SESSION['reply_error'])): ?>
                <div class="admin-alert error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $_SESSION['reply_error']; ?>
                    <button type="button" class="admin-alert-close" onclick="this.parentElement.style.display='none';">×</button>
                </div>
                <?php unset($_SESSION['reply_error']); ?>
            <?php endif; ?>

            <!-- Get reply history -->
            <?php
            $replies = [];
            $replies_sql = "SELECT r.*, u.username, u.profile_image
                           FROM contact_replies r
                           LEFT JOIN users u ON r.user_id = u.id
                           WHERE r.submission_id = ?
                           ORDER BY r.created_at DESC";

            try {
                $stmt = $conn->prepare($replies_sql);
                if ($stmt) {
                    $stmt->bind_param("i", $single_submission['id']);
                    $stmt->execute();
                    $replies_result = $stmt->get_result();

                    if ($replies_result && $replies_result->num_rows > 0) {
                        while ($row = $replies_result->fetch_assoc()) {
                            $replies[] = $row;
                        }
                    }
                    $stmt->close();
                }
            } catch (Exception $e) {
                // Silently handle error
            }
            ?>

            <!-- New Message View Layout -->
            <div class="message-view-layout">
                <div class="message-view-header">
                    <div class="message-view-actions">
                        <a href="inbox.php" class="admin-btn">
                            <i class="fas fa-arrow-left"></i> Back to Inbox
                        </a>

                        <div class="message-action-buttons">
                            <?php if ($single_submission['is_read']): ?>
                                <a href="<?php echo htmlspecialchars('inbox.php?mark_unread=1&id=' . $single_submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn">
                                    <i class="fas fa-envelope"></i> Mark as Unread
                                </a>
                            <?php else: ?>
                                <a href="<?php echo htmlspecialchars('inbox.php?mark_read=1&id=' . $single_submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn">
                                    <i class="fas fa-envelope-open"></i> Mark as Read
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo htmlspecialchars('inbox.php?delete=1&id=' . $single_submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn danger" onclick="return confirm('Are you sure you want to delete this message?');">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>

                <div class="message-view-container">
                    <div class="message-view-sidebar">
                        <div class="message-contact-card">
                            <div class="contact-header">
                                <div class="contact-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="contact-name">
                                    <h3><?php echo htmlspecialchars($single_submission['name']); ?></h3>
                                    <span class="contact-source"><?php echo htmlspecialchars($single_submission['source']); ?></span>
                                </div>
                            </div>

                            <div class="contact-details">
                                <div class="contact-detail-item">
                                    <i class="fas fa-envelope"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($single_submission['email']); ?>">
                                        <?php echo htmlspecialchars($single_submission['email']); ?>
                                    </a>
                                </div>

                                <?php if (!empty($single_submission['phone'])): ?>
                                <div class="contact-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:<?php echo htmlspecialchars($single_submission['phone']); ?>">
                                        <?php echo htmlspecialchars($single_submission['phone']); ?>
                                    </a>
                                </div>
                                <?php endif; ?>

                                <div class="contact-detail-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo date('F j, Y, g:i a', strtotime($single_submission['created_at'])); ?></span>
                                </div>

                                <div class="contact-detail-item">
                                    <i class="fas fa-tag"></i>
                                    <span>
                                        <?php if ($single_submission['is_read']): ?>
                                            <span class="status-badge read">Read</span>
                                        <?php else: ?>
                                            <span class="status-badge unread">Unread</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="contact-actions">
                                <a href="mailto:<?php echo htmlspecialchars($single_submission['email']); ?>" class="admin-btn full-width">
                                    <i class="fas fa-envelope"></i> Email Directly
                                </a>
                                <?php if (!empty($single_submission['phone'])): ?>
                                <a href="tel:<?php echo htmlspecialchars($single_submission['phone']); ?>" class="admin-btn full-width">
                                    <i class="fas fa-phone"></i> Call
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="message-view-content">
                        <div class="message-card">
                            <div class="message-card-header">
                                <h3>Message</h3>
                                <span class="message-date"><?php echo date('F j, Y, g:i a', strtotime($single_submission['created_at'])); ?></span>
                            </div>

                            <div class="message-card-body">
                                <div class="message-text">
                                    <?php echo nl2br(htmlspecialchars($single_submission['message'])); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Reply Form -->
                        <div class="reply-card">
                            <div class="reply-card-header">
                                <h3><i class="fas fa-reply"></i> Quick Reply</h3>
                            </div>

                            <div class="reply-card-body">
                                <form method="post" action="send_reply.php" id="reply-form">
                                    <!-- CSRF Protection -->
                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                    <input type="hidden" name="submission_id" value="<?php echo $single_submission['id']; ?>">
                                    <input type="hidden" name="recipient_email" value="<?php echo htmlspecialchars($single_submission['email']); ?>">
                                    <input type="hidden" name="recipient_name" value="<?php echo htmlspecialchars($single_submission['name']); ?>">

                                    <div class="form-group">
                                        <label for="reply_subject">Subject:</label>
                                        <input type="text" id="reply_subject" name="reply_subject" value="Re: Contact Form Submission" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="reply_message">Message:</label>
                                        <div class="reply-toolbar">
                                            <button type="button" class="reply-toolbar-btn" data-action="bold" title="Bold"><i class="fas fa-bold"></i></button>
                                            <button type="button" class="reply-toolbar-btn" data-action="italic" title="Italic"><i class="fas fa-italic"></i></button>
                                            <button type="button" class="reply-toolbar-btn" data-action="link" title="Insert Link"><i class="fas fa-link"></i></button>
                                            <button type="button" class="reply-toolbar-btn" data-action="template" title="Use Template"><i class="fas fa-file-alt"></i></button>
                                        </div>
                                        <textarea id="reply_message" name="reply_message" rows="6" required></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="button" class="admin-btn preview-reply-btn">
                                            <i class="fas fa-eye"></i> Preview
                                        </button>
                                        <button type="submit" class="admin-btn" id="send-reply-btn">
                                            <i class="fas fa-paper-plane"></i> Send Reply
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Reply History -->
                        <?php if (!empty($replies)): ?>
                        <div class="reply-history-card">
                            <div class="reply-history-header">
                                <h3><i class="fas fa-history"></i> Reply History</h3>
                                <span class="reply-count"><?php echo count($replies); ?> <?php echo count($replies) === 1 ? 'Reply' : 'Replies'; ?></span>
                            </div>

                            <div class="reply-history-body">
                                <?php foreach ($replies as $reply): ?>
                                <div class="reply-item">
                                    <div class="reply-item-header">
                                        <div class="reply-user-info">
                                            <span class="reply-user-name"><?php echo htmlspecialchars($reply['username']); ?></span>
                                            <span class="reply-date"><?php echo date('M j, Y, g:i a', strtotime($reply['created_at'])); ?></span>
                                        </div>
                                        <div class="reply-subject">
                                            <?php echo htmlspecialchars($reply['subject']); ?>
                                        </div>
                                    </div>
                                    <div class="reply-item-content">
                                        <?php echo nl2br(htmlspecialchars($reply['message'])); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Preview Modal -->
            <div id="preview-modal" class="admin-modal">
                <div class="admin-modal-content">
                    <div class="admin-modal-header">
                        <h3><i class="fas fa-eye"></i> Preview Reply</h3>
                        <button type="button" class="admin-modal-close">&times;</button>
                    </div>
                    <div class="admin-modal-body">
                        <div class="preview-subject">
                            <strong>Subject:</strong> <span id="preview-subject"></span>
                        </div>
                        <div class="preview-message">
                            <div id="preview-message-content"></div>
                        </div>
                    </div>
                    <div class="admin-modal-footer">
                        <button type="button" class="admin-btn admin-modal-close">
                            <i class="fas fa-times"></i> Close
                        </button>
                        <button type="button" class="admin-btn" id="send-from-preview">
                            <i class="fas fa-paper-plane"></i> Send Reply
                        </button>
                    </div>
                </div>
            </div>

            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Reply toolbar functionality
                const replyToolbarButtons = document.querySelectorAll('.reply-toolbar-btn');
                const replyMessage = document.getElementById('reply_message');
                const previewModal = document.getElementById('preview-modal');
                const previewSubject = document.getElementById('preview-subject');
                const previewMessageContent = document.getElementById('preview-message-content');
                const sendFromPreviewBtn = document.getElementById('send-from-preview');
                const replyForm = document.getElementById('reply-form');
                const replySubject = document.getElementById('reply_subject');
                const sendReplyBtn = document.getElementById('send-reply-btn');
                const previewReplyBtn = document.querySelector('.preview-reply-btn');
                const modalCloseButtons = document.querySelectorAll('.admin-modal-close');

                // Preview functionality
                if (previewReplyBtn && previewModal) {
                    previewReplyBtn.addEventListener('click', function() {
                        if (replyMessage.value.trim() === '') {
                            alert('Please enter a message before previewing.');
                            return;
                        }

                        // Set preview content
                        previewSubject.textContent = replySubject.value;
                        previewMessageContent.innerHTML = nl2br(replyMessage.value);

                        // Show modal
                        previewModal.style.display = 'block';
                    });

                    // Close modal when clicking close button
                    modalCloseButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            previewModal.style.display = 'none';
                        });
                    });

                    // Close modal when clicking outside
                    window.addEventListener('click', function(e) {
                        if (e.target === previewModal) {
                            previewModal.style.display = 'none';
                        }
                    });

                    // Send from preview
                    if (sendFromPreviewBtn) {
                        sendFromPreviewBtn.addEventListener('click', function() {
                            replyForm.submit();
                        });
                    }
                }

                // Toolbar buttons
                if (replyToolbarButtons.length && replyMessage) {
                    replyToolbarButtons.forEach(button => {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            const action = this.getAttribute('data-action');
                            if (!action) return;

                            // Get cursor position
                            const startPos = replyMessage.selectionStart;
                            const endPos = replyMessage.selectionEnd;
                            const selectedText = replyMessage.value.substring(startPos, endPos);

                            switch (action) {
                                case 'bold':
                                    wrapText(replyMessage, '**', '**', selectedText, startPos, endPos);
                                    break;
                                case 'italic':
                                    wrapText(replyMessage, '_', '_', selectedText, startPos, endPos);
                                    break;
                                case 'link':
                                    const url = prompt('Enter URL:', 'https://');
                                    if (url) {
                                        wrapText(replyMessage, '[', `](${url})`, selectedText || 'link text', startPos, endPos);
                                    }
                                    break;
                                case 'template':
                                    // Insert a template response with variables
                                    const template = `Dear ${<?php echo json_encode(htmlspecialchars($single_submission['name'])); ?>},\n\nThank you for contacting us. We have received your message and will respond to your inquiry as soon as possible.\n\nBest regards,\n${<?php echo json_encode(htmlspecialchars($_SESSION['username'] ?? 'Admin')); ?>}\nManage Inc. Team`;

                                    replyMessage.value = template;
                                    break;
                            }

                            // Add active state to button
                            this.classList.add('active');
                            setTimeout(() => {
                                this.classList.remove('active');
                            }, 300);
                        });
                    });
                }

                // Helper function to wrap text with formatting
                function wrapText(textarea, before, after, selectedText, startPos, endPos) {
                    const newText = before + selectedText + after;
                    textarea.value =
                        textarea.value.substring(0, startPos) +
                        newText +
                        textarea.value.substring(endPos);

                    // Set selection to the wrapped text
                    textarea.focus();
                    textarea.selectionStart = startPos + before.length;
                    textarea.selectionEnd = startPos + before.length + selectedText.length;
                }

                // Helper function to convert newlines to <br> tags
                function nl2br(str) {
                    return str.replace(/\n/g, '<br>');
                }

                // Close alert buttons
                const alertCloseButtons = document.querySelectorAll('.admin-alert-close');
                alertCloseButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        this.parentElement.style.display = 'none';
                    });
                });

                // Auto-hide alerts after 5 seconds
                const alerts = document.querySelectorAll('.admin-alert');
                alerts.forEach(alert => {
                    setTimeout(() => {
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            alert.style.display = 'none';
                        }, 500);
                    }, 5000);
                });
            });
            </script>
        <?php elseif (isset($_GET['id']) && $error_message): ?>
            <!-- Error fallback view -->
            <div class="message-error-view">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2>Unable to Load Message</h2>
                <p>We encountered an error while trying to load message #<?php echo (int)$_GET['id']; ?>.</p>
                <div class="error-actions">
                    <a href="inbox.php?id=<?php echo (int)$_GET['id']; ?>" class="admin-btn">
                        <i class="fas fa-sync"></i> Try Again
                    </a>
                    <a href="inbox.php" class="admin-btn">
                        <i class="fas fa-arrow-left"></i> Back to Inbox
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- Inbox Toolbar -->
            <div class="inbox-toolbar">
                <div class="inbox-filters">
                    <form method="get" action="inbox.php" class="inbox-filter-form">
                        <div class="filter-group">
                            <select name="filter" id="filter-status" onchange="this.form.submit()">
                                <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Messages</option>
                                <option value="unread" <?php echo $filter === 'unread' ? 'selected' : ''; ?>>Unread</option>
                                <option value="read" <?php echo $filter === 'read' ? 'selected' : ''; ?>>Read</option>
                            </select>
                        </div>

                        <div class="search-group">
                            <input type="text" name="search" placeholder="Search messages..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                            <?php if (!empty($search)): ?>
                            <a href="inbox.php" class="clear-search">
                                <i class="fas fa-times"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>

                <div class="inbox-actions">
                    <form method="post" action="inbox.php" id="bulk-action-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <select name="bulk_action" id="bulk-action">
                            <option value="">Bulk Actions</option>
                            <option value="mark_read">Mark as Read</option>
                            <option value="mark_unread">Mark as Unread</option>
                            <option value="delete">Delete</option>
                        </select>
                        <button type="submit" class="admin-btn" id="apply-bulk-action" disabled>Apply</button>
                    </form>

                    <div class="per-page-selector">
                        <label for="per-page">Show:</label>
                        <select name="per_page" id="per-page" onchange="window.location.href='inbox.php?per_page='+this.value">
                            <?php foreach ([5, 10, 25, 50, 100] as $value): ?>
                            <option value="<?php echo $value; ?>" <?php echo $items_per_page == $value ? 'selected' : ''; ?>><?php echo $value; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Submissions List View -->
            <div class="inbox-messages">
                <?php if (empty($submissions)): ?>
                    <div class="empty-inbox">
                        <div class="empty-inbox-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h2>Your inbox is empty</h2>
                        <?php if (!empty($search)): ?>
                            <p>No messages found matching your search criteria.</p>
                            <a href="inbox.php" class="admin-btn">Clear Search</a>
                        <?php elseif ($filter !== 'all'): ?>
                            <p>No <?php echo $filter === 'unread' ? 'unread' : 'read'; ?> messages found.</p>
                            <a href="inbox.php" class="admin-btn">Show All Messages</a>
                        <?php else: ?>
                            <p>There are no messages in your inbox yet.</p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="messages-table-container">
                        <table class="messages-table">
                            <thead>
                                <tr>
                                    <th class="checkbox-column">
                                        <input type="checkbox" id="select-all-messages">
                                    </th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Source</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($submissions as $submission): ?>
                                    <tr class="<?php echo $submission['is_read'] ? 'message-row' : 'message-row unread'; ?>">
                                        <td class="checkbox-column">
                                            <input type="checkbox" name="selected_messages[]" form="bulk-action-form" value="<?php echo $submission['id']; ?>" class="message-checkbox">
                                        </td>
                                        <td class="name-column">
                                            <a href="<?php echo htmlspecialchars('inbox.php?id=' . $submission['id']); ?>" class="message-link">
                                                <?php echo htmlspecialchars($submission['name']); ?>
                                            </a>
                                        </td>
                                        <td class="email-column"><?php echo htmlspecialchars($submission['email']); ?></td>
                                        <td class="source-column"><?php echo htmlspecialchars($submission['source']); ?></td>
                                        <td class="date-column"><?php echo date('M j, Y, g:i a', strtotime($submission['created_at'])); ?></td>
                                        <td class="status-column">
                                            <?php if ($submission['is_read']): ?>
                                                <span class="status-badge read">Read</span>
                                            <?php else: ?>
                                                <span class="status-badge unread">Unread</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="actions-column" data-label="Actions">
                                            <div class="admin-table-actions">
                                                <a href="<?php echo htmlspecialchars('inbox.php?id=' . $submission['id']); ?>" class="admin-btn admin-btn-sm admin-btn-primary" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($submission['is_read']): ?>
                                                    <a href="<?php echo htmlspecialchars('inbox.php?mark_unread=1&id=' . $submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn admin-btn-sm admin-btn-secondary" title="Mark as Unread">
                                                        <i class="fas fa-envelope"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="<?php echo htmlspecialchars('inbox.php?mark_read=1&id=' . $submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn admin-btn-sm admin-btn-success" title="Mark as Read">
                                                        <i class="fas fa-envelope-open"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="<?php echo htmlspecialchars('inbox.php?delete=1&id=' . $submission['id'] . '&csrf_token=' . generate_csrf_token()); ?>" class="admin-btn admin-btn-sm admin-btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this message?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="inbox-pagination">
                        <?php
                        // Set up pagination parameters
                        $base_url = 'inbox.php';
                        $query_params = [];

                        // Add existing query parameters
                        if (!empty($filter) && $filter !== 'all') {
                            $query_params['filter'] = $filter;
                        }

                        if (!empty($search)) {
                            $query_params['search'] = $search;
                        }

                        if ($items_per_page !== 10) {
                            $query_params['per_page'] = $items_per_page;
                        }

                        $total_items = $total_submissions;

                        // Include the pagination component
                        include 'includes/pagination.php';
                        ?>
                    </div>
                <?php endif; ?>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Select all checkbox functionality
                    const selectAllCheckbox = document.getElementById('select-all-messages');
                    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
                    const bulkActionButton = document.getElementById('apply-bulk-action');

                    if (selectAllCheckbox) {
                        selectAllCheckbox.addEventListener('change', function() {
                            messageCheckboxes.forEach(checkbox => {
                                checkbox.checked = selectAllCheckbox.checked;
                            });

                            updateBulkActionButton();
                        });
                    }

                    // Individual checkbox change
                    messageCheckboxes.forEach(checkbox => {
                        checkbox.addEventListener('change', function() {
                            updateBulkActionButton();

                            // Update select all checkbox
                            if (selectAllCheckbox) {
                                const allChecked = Array.from(messageCheckboxes).every(cb => cb.checked);
                                const someChecked = Array.from(messageCheckboxes).some(cb => cb.checked);

                                selectAllCheckbox.checked = allChecked;
                                selectAllCheckbox.indeterminate = someChecked && !allChecked;
                            }
                        });
                    });

                    // Update bulk action button state
                    function updateBulkActionButton() {
                        if (bulkActionButton) {
                            const anyChecked = Array.from(messageCheckboxes).some(cb => cb.checked);
                            bulkActionButton.disabled = !anyChecked;
                        }
                    }

                    // Confirm bulk delete
                    const bulkActionForm = document.getElementById('bulk-action-form');
                    if (bulkActionForm) {
                        bulkActionForm.addEventListener('submit', function(e) {
                            const bulkAction = document.getElementById('bulk-action').value;
                            if (bulkAction === 'delete') {
                                const checkedCount = Array.from(messageCheckboxes).filter(cb => cb.checked).length;
                                if (!confirm(`Are you sure you want to delete ${checkedCount} message${checkedCount !== 1 ? 's' : ''}?`)) {
                                    e.preventDefault();
                                }
                            }
                        });
                    }
                });
            </script>
        <?php endif; ?>
    </div>
</div>

<style>
    /* Admin table actions - ensure single row layout */
    .admin-table-actions {
        display: flex !important;
        align-items: center !important;
        gap: 5px !important;
        justify-content: flex-start !important;
        flex-wrap: nowrap !important;
        white-space: nowrap !important;
        flex-direction: row !important;
    }

    .admin-table-actions .admin-btn {
        flex-shrink: 0;
        min-width: auto;
        padding: 6px 8px;
        font-size: 12px;
    }

    .actions-column {
        width: 120px;
        min-width: 120px;
        text-align: left;
    }

    /* Override mobile responsive rules for inbox actions */
    @media (max-width: 768px) {
        .inbox-page .admin-table-actions {
            flex-direction: row !important;
            align-items: center !important;
            width: auto !important;
        }

        .inbox-page .actions-column .admin-table-actions {
            justify-content: flex-start !important;
            width: auto !important;
        }
    }

    /* Table styles - optimized for reduced row height */
    .unread-row {
        font-weight: 600; /* Changed from bold to 600 for better spacing */
        background-color: rgba(241, 202, 47, 0.05);
    }
    .unread-row:hover {
        background-color: rgba(241, 202, 47, 0.1);
    }

    /* Status badge styles - optimized for reduced row height */
    /* Note: These styles are now defined in table-optimizations.css */

    /* Enhanced header styles */
    .enhanced-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .enhanced-header .header-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .enhanced-header h2 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--secondary-color);
    }

    .enhanced-header h2 i {
        color: var(--primary-color);
    }

    .inbox-stats {
        display: flex;
        gap: 15px;
    }

    .inbox-stat-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
        color: var(--text-light);
        background-color: white;
        padding: 5px 10px;
        border-radius: 20px;
        border: 1px solid #eee;
    }

    .inbox-stat-item.has-unread {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .inbox-stat-item.has-unread i {
        color: #f1ca2f;
    }

    /* Submission view container - two-column layout */
    .submission-view-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
    }
    .submission-view-main {
        flex: 1;
        min-width: 0; /* Prevent flex items from overflowing */
        flex-basis: 60%; /* Increased from 45% to give more space to the main content */
    }
    .submission-view-sidebar {
        flex-basis: 35%; /* Reduced from 50% to 35% */
        min-width: 300px; /* Reduced from 350px */
    }

    /* Fix for textarea width */
    #reply-form textarea,
    #reply-form input[type="text"] {
        width: 100%;
        box-sizing: border-box;
    }

    /* Fix for email visibility */
    .contact-email {
        word-break: break-all;
        display: block;
        width: 100%;
    }

    .contact-email a {
        color: var(--primary-color);
        font-weight: 500;
    }

    /* Submission details styles */
    .submission-details {
        display: flex;
        flex-direction: column;
        gap: 25px;
    }
    .submission-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    .submission-meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .submission-meta-item i {
        color: var(--primary-color);
        font-size: 14px;
    }
    .submission-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 5px;
    }
    .submission-field {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }
    .submission-field:last-child {
        border-bottom: none;
    }
    .submission-field label {
        font-weight: 600;
        color: #444;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .submission-field label i {
        color: var(--primary-color);
    }
    .submission-value {
        line-height: 1.6;
        font-size: 15px;
    }
    .submission-value.message {
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 8px;
        white-space: pre-wrap;
        border: 1px solid #eee;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
        width: 100%;
        box-sizing: border-box;
        text-align: left;
    }
    .email-link, .phone-link {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s ease;
    }
    .email-link:hover, .phone-link:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    /* Reply history styles */
    .reply-history {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    .reply-item {
        background-color: #fff;
        border-radius: 8px;
        border: 1px solid #eee;
        overflow: hidden;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    .reply-item:hover {
        box-shadow: 0 3px 8px rgba(0,0,0,0.08);
    }
    .reply-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
    }
    .reply-user {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .reply-user-avatar, .reply-user-avatar-placeholder {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }
    .reply-user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .reply-user-info {
        display: flex;
        flex-direction: column;
    }
    .reply-user-name {
        font-weight: 600;
        color: #333;
    }
    .reply-date {
        font-size: 12px;
        color: #6c757d;
    }
    .reply-subject {
        font-weight: 600;
        color: #495057;
    }
    .reply-subject i {
        color: var(--primary-color);
        margin-right: 5px;
    }
    .reply-content {
        padding: 20px;
        line-height: 1.6;
        white-space: pre-wrap;
    }
    .reply-count-badge {
        display: inline-block;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        background-color: var(--primary-light);
        color: var(--primary-color);
    }

    /* Contact info card */
    .contact-info-card {
        margin-bottom: 20px;
    }
    .contact-info-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 12px 0;
        border-bottom: 1px solid #eee;
    }
    .contact-info-item:last-child {
        border-bottom: none;
    }
    .contact-info-item i {
        color: var(--primary-color);
        font-size: 16px;
        margin-top: 3px;
    }
    .contact-info-content {
        flex: 1;
    }
    .contact-info-content label {
        display: block;
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 3px;
    }

    .contact-info-content div {
        font-size: 14px;
        color: #333;
    }
    .contact-info-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 15px;
    }
    .full-width {
        width: 100%;
    }

    /* Quick Reply form styles */
    .admin-card-header h3 {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .admin-card-header h3 i {
        color: var(--primary-color);
    }
    .admin-card-header h3:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background-color: var(--primary-color);
        border-radius: 2px;
    }

    /* Reply header row with toolbar */
    .reply-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .reply-toolbar-container {
        flex-shrink: 0;
    }

    .reply-toolbar {
        display: flex;
        gap: 5px;
        padding: 8px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    /* Variables dropdown styles */
    .variables-dropdown {
        position: relative;
        display: inline-block;
    }

    .variables-dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        background-color: #fff;
        min-width: 200px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 4px;
        border: 1px solid #dee2e6;
        z-index: 100;
        max-height: 300px;
        overflow-y: auto;
    }

    .variables-dropdown-content.show {
        display: block;
    }

    .variables-dropdown-header {
        padding: 8px 12px;
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        font-size: 13px;
    }

    .variables-dropdown-content a {
        color: #495057;
        padding: 8px 12px;
        text-decoration: none;
        display: block;
        font-size: 13px;
        transition: background-color 0.2s;
        border-bottom: 1px solid #f1f1f1;
    }

    .variables-dropdown-content a:last-child {
        border-bottom: none;
    }

    .variables-dropdown-content a:hover {
        background-color: #f8f9fa;
        color: var(--primary-color);
    }

    /* Reply footer row with hint and counter */
    .reply-footer-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }

    .form-hint {
        margin: 0;
        font-size: 13px;
        color: #6c757d;
    }
    .reply-toolbar-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: none;
        background-color: transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .reply-toolbar-btn:hover {
        background-color: #e9ecef;
    }
    .reply-toolbar-btn.active {
        background-color: var(--primary-light);
        color: var(--primary-color);
    }
    .reply-toolbar-separator {
        width: 1px;
        height: 24px;
        background-color: #dee2e6;
        margin: 0 5px;
    }
    #reply_message {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        resize: vertical;
        min-height: 200px;
        text-align: left !important;
        font-family: inherit;
        font-size: 14px;
        line-height: 1.5;
        padding: 15px;
        direction: ltr;
    }
    .character-counter {
        text-align: right;
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }
    .character-counter.warning {
        color: #dc3545;
    }

    /* Alert styles */
    .admin-alert {
        position: relative;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        transition: opacity 0.3s ease;
    }
    .admin-alert i {
        margin-right: 10px;
        font-size: 18px;
    }
    .admin-alert.success {
        background-color: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }
    .admin-alert.error {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }
    .admin-alert-close {
        position: absolute;
        top: 10px;
        right: 10px;
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: inherit;
        opacity: 0.7;
    }
    .admin-alert-close:hover {
        opacity: 1;
    }

    /* Alert actions */
    .admin-alert-actions {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }
    .admin-alert-actions .admin-btn {
        padding: 6px 12px;
        font-size: 13px;
    }

    /* Modal styles */
    .admin-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.5);
    }
    .admin-modal-content {
        background-color: #fff;
        margin: 10% auto;
        width: 80%;
        max-width: 700px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        animation: modalFadeIn 0.3s;
    }
    @keyframes modalFadeIn {
        from {opacity: 0; transform: translateY(-20px);}
        to {opacity: 1; transform: translateY(0);}
    }
    .admin-modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .admin-modal-header h3 {
        margin: 0;
        font-size: 18px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .admin-modal-header h3 i {
        color: var(--primary-color);
    }
    .admin-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #6c757d;
    }
    .admin-modal-close:hover {
        color: #333;
    }
    .admin-modal-body {
        padding: 20px;
        max-height: 60vh;
        overflow-y: auto;
    }
    .admin-modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    .preview-subject {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }
    .preview-message {
        background-color: #f9f9f9;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #eee;
    }
    #preview-message-content {
        line-height: 1.6;
    }

    /* Responsive styles */
    @media (max-width: 992px) {
        .submission-view-container {
            flex-direction: column-reverse; /* Changed to column-reverse to show quick reply first on mobile */
        }
        .submission-view-main, .submission-view-sidebar {
            flex-basis: 100%;
            width: 100%;
        }

        /* Enhanced header responsive styles */
        .enhanced-header .header-main {
            flex-direction: column;
            align-items: flex-start;
        }

        .inbox-stats {
            width: 100%;
            justify-content: space-between;
        }
    }

    @media (max-width: 768px) {
        /* Enhanced header mobile styles */
        .enhanced-header {
            padding: 12px 15px;
            margin-bottom: 15px;
        }

        .inbox-stat-item {
            font-size: 12px;
            padding: 4px 8px;
        }

        /* Other mobile styles */
        .submission-meta {
            flex-direction: column;
            gap: 15px;
            padding: 12px;
        }
        .submission-field {
            padding-bottom: 12px;
        }
        .submission-value.message {
            padding: 15px;
        }
        .admin-alert-actions {
            flex-direction: column;
            align-items: flex-start;
        }
        .reply-header {
            flex-direction: column;
            align-items: flex-start;
        }
        .admin-modal-content {
            width: 95%;
            margin: 5% auto;
        }
        .reply-header-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        .reply-footer-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        .character-counter {
            align-self: flex-end;
            width: 100%;
        }
    }

    /* Dark mode styles */
    body.dark-mode .admin-card {
        background-color: var(--secondary-dark);
        border-color: var(--border-color);
    }

    /* Dark mode for enhanced header */
    body.dark-mode .enhanced-header {
        background-color: rgba(255, 255, 255, 0.05);
        border-color: var(--primary-color);
    }

    body.dark-mode .inbox-stat-item {
        background-color: var(--secondary-dark);
        border-color: var(--border-color);
        color: var(--text-color);
    }

    body.dark-mode .inbox-stat-item.has-unread {
        background-color: rgba(241, 202, 47, 0.1);
        border-color: var(--primary-dark);
        color: var(--primary-light);
    }
    body.dark-mode .admin-card-header {
        border-bottom-color: var(--border-color);
    }
    body.dark-mode .submission-meta {
        background-color: rgba(255, 255, 255, 0.05);
    }
    body.dark-mode .submission-field {
        border-bottom-color: var(--border-color);
    }
    body.dark-mode .submission-value.message {
        background-color: rgba(255, 255, 255, 0.03);
        border-color: var(--border-color);
    }
    body.dark-mode .reply-item {
        background-color: var(--secondary-dark);
        border-color: var(--border-color);
    }
    body.dark-mode .reply-header {
        background-color: rgba(255, 255, 255, 0.03);
        border-bottom-color: var(--border-color);
    }
    body.dark-mode .reply-toolbar {
        background-color: rgba(255, 255, 255, 0.05);
        border-color: var(--border-color);
    }
    body.dark-mode #reply_message {
        background-color: rgba(255, 255, 255, 0.03);
        border-color: var(--border-color);
        color: var(--text-color);
    }
    body.dark-mode .contact-info-item {
        border-bottom-color: var(--border-color);
    }
    body.dark-mode .contact-info-content div {
        color: var(--text-color);
    }
    body.dark-mode .contact-email a,
    body.dark-mode .email-link,
    body.dark-mode .phone-link {
        color: var(--primary-color);
    }

    /* Dark mode for variables dropdown */
    body.dark-mode .variables-dropdown-content {
        background-color: var(--secondary-dark);
        border-color: var(--border-color);
    }

    body.dark-mode .variables-dropdown-header {
        background-color: rgba(255, 255, 255, 0.05);
        color: var(--text-color);
        border-color: var(--border-color);
    }

    body.dark-mode .variables-dropdown-content a {
        color: var(--text-color);
        border-color: var(--border-color);
    }

    body.dark-mode .variables-dropdown-content a:hover {
        background-color: rgba(255, 255, 255, 0.05);
        color: var(--primary-color);
    }
</style>

<!-- Pagination script is now included in the pagination component -->

</div> <!-- Close admin-container -->

<?php include 'includes/footer.php'; ?>
