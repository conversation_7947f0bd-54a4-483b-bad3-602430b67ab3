/**
 * TinyMCE HTML Editor
 * Replaces CodeMirror with TinyMCE for better visual editing
 */

// Global variables
let tinymceEditor = null;
let currentMode = null; // Will be set to 'code' when editor initializes
let isModified = false;
let currentFilePath = '';
let siteBaseUrl = '';

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get current file path and site base URL
    const urlParams = new URLSearchParams(window.location.search);
    currentFilePath = urlParams.get('file') || '';

    // Detect site base URL
    const currentPath = window.location.pathname;
    const adminIndex = currentPath.indexOf('/admin/');
    if (adminIndex !== -1) {
        siteBaseUrl = window.location.origin + currentPath.substring(0, adminIndex);
    } else {
        siteBaseUrl = window.location.origin;
    }

    console.log('Site Base URL detected:', siteBaseUrl);
    console.log('Current file path:', currentFilePath);

    function initializeEditor() {
        if (document.getElementById('tinymce-editor')) {
            initTinyMCE();
            initModeToggle();
            initVersionHistory();
            loadVersionHistory();
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);
        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

function initTinyMCE() {
    // Extract CSS files from the original HTML content
    const originalContent = document.getElementById('tinymce-editor').value;
    console.log('Original HTML content length:', originalContent.length);
    console.log('Original HTML preview:', originalContent.substring(0, 500) + '...');

    const cssFiles = extractCSSFiles(originalContent);
    console.log('CSS files to load in TinyMCE:', cssFiles);

    // Create custom code editor container
    createCustomCodeEditor();

    tinymce.init({
        selector: '#tinymce-editor',
        height: 600,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'codesample'
        ],
        toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',

        // Load CSS files from the HTML content for proper styling
        content_css: cssFiles.length > 0 ? cssFiles : false,
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; line-height:1.6; margin:20px; padding:10px; background:#fff; }',

        // Allow editing in both modes
        readonly: false,

        // Start in code mode
        init_instance_callback: function(editor) {
            tinymceEditor = editor;

            // Initialize currentMode
            currentMode = 'code';

            // Start in code mode
            setTimeout(() => {
                switchToCodeMode();
            }, 200);

            // Track changes
            editor.on('change input', function() {
                handleContentChange();
            });

            // Convert paths and block header/footer when setting content for visual mode
            editor.on('BeforeSetContent', function(e) {
                if (currentMode === 'visual') {
                    e.content = convertPathsForDisplay(e.content);
                    e.content = blockHeaderFooterEditing(e.content);
                }
            });

            // Block header/footer editing after content is set
            editor.on('SetContent', function() {
                if (currentMode === 'visual') {
                    setTimeout(() => {
                        blockHeaderFooterElements();
                    }, 100);
                }
            });
        },

        // Custom setup
        setup: function(editor) {
            // Remove the default code plugin to prevent modal
            // We'll use our custom code editor instead
        },

        // Convert relative URLs to absolute for proper display
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        document_base_url: siteBaseUrl + '/'
    });
}

function createCustomCodeEditor() {
    // Create custom code editor container that will replace TinyMCE in code mode
    const editorContainer = document.querySelector('.html-editor-container');
    if (!editorContainer) return;

    const codeEditorHTML = `
        <div id="custom-code-editor" style="display: none;">
            <textarea id="code-textarea" style="
                width: 100%;
                height: 600px;
                border: none;
                outline: none;
                padding: 15px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.5;
                background: #f8f9fa;
                color: #333;
                resize: none;
                box-sizing: border-box;
            "></textarea>
        </div>
    `;

    editorContainer.insertAdjacentHTML('beforeend', codeEditorHTML);
}

function initModeToggle() {
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');
    const formatCodeBtn = document.getElementById('formatCode');

    if (codeModeBtn) {
        codeModeBtn.addEventListener('click', switchToCodeMode);
    }

    if (visualModeBtn) {
        visualModeBtn.addEventListener('click', switchToVisualMode);
    }

    if (formatCodeBtn) {
        formatCodeBtn.addEventListener('click', formatCode);
    }
}

function switchToCodeMode() {
    if (!tinymceEditor) return;

    // Check if we're switching from visual mode before changing currentMode
    const wasVisualMode = currentMode === 'visual';

    currentMode = 'code';

    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');

    if (codeModeBtn) {
        codeModeBtn.classList.add('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.remove('active');
    }

    // Get current content
    let content;

    if (wasVisualMode) {
        // If switching from visual mode, get content from TinyMCE and reconstruct full HTML
        const bodyContent = tinymceEditor.getContent();
        content = reconstructFullContent(bodyContent);
    } else {
        // If starting fresh or switching from code mode, get original content
        const originalTextarea = document.getElementById('tinymce-editor');
        content = originalTextarea ? originalTextarea.value : tinymceEditor.getContent();
    }

    // Convert paths back to relative for code editing
    content = convertPathsForSaving(content);

    // Hide TinyMCE visual editor
    const tinymceContainer = document.querySelector('.tox-tinymce');
    if (tinymceContainer) {
        tinymceContainer.style.display = 'none';
    }

    // Show custom code editor
    const customCodeEditor = document.getElementById('custom-code-editor');
    const codeTextarea = document.getElementById('code-textarea');

    if (customCodeEditor && codeTextarea) {
        customCodeEditor.style.display = 'block';
        codeTextarea.value = content;

        // Add header/footer protection to code mode (temporarily disabled for debugging)
        // addCodeModeProtection(codeTextarea);
        console.log('Code mode protection temporarily disabled for debugging');

        // Focus the textarea
        codeTextarea.focus();
    }
}

function switchToVisualMode() {
    if (!tinymceEditor) return;

    // Check if we're switching from code mode before changing currentMode
    const wasCodeMode = currentMode === 'code';

    currentMode = 'visual';

    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');

    if (codeModeBtn) {
        codeModeBtn.classList.remove('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.add('active');
    }

    // Get current content
    let content;

    if (wasCodeMode) {
        // If switching from code mode, get content from custom code editor
        const codeTextarea = document.getElementById('code-textarea');
        if (codeTextarea && codeTextarea.value) {
            content = codeTextarea.value;
        } else {
            // Fallback to original content
            const originalTextarea = document.getElementById('tinymce-editor');
            content = originalTextarea ? originalTextarea.value : '';
        }
    } else {
        // If starting fresh, get original content
        const originalTextarea = document.getElementById('tinymce-editor');
        content = originalTextarea ? originalTextarea.value : '';
    }

    console.log('Switching to visual mode with content:', content.substring(0, 200) + '...');

    // Convert paths to absolute for proper display
    content = convertPathsForDisplay(content);

    // Extract only body content for visual editing (protect header/footer)
    content = extractEditableContent(content);

    console.log('Extracted editable content:', content.substring(0, 200) + '...');

    // Hide custom code editor
    const customCodeEditor = document.getElementById('custom-code-editor');
    if (customCodeEditor) {
        customCodeEditor.style.display = 'none';
    }

    // Show TinyMCE visual editor
    const tinymceContainer = document.querySelector('.tox-tinymce');
    if (tinymceContainer) {
        tinymceContainer.style.display = 'block';
    }

    // Set content in visual mode
    if (content && content.trim()) {
        console.log('Setting content in TinyMCE:', content.substring(0, 200) + '...');
        tinymceEditor.setContent(content);
    } else {
        console.warn('No content to set in visual mode, using placeholder');
        tinymceEditor.setContent('<p>No editable content found. This may be because the HTML file only contains header/footer sections.</p>');
    }

    // Add visual mode protection
    setTimeout(() => {
        blockHeaderFooterElements();
    }, 100);
}

function formatCode() {
    if (typeof html_beautify === 'undefined') return;

    let content;

    if (currentMode === 'code') {
        // Format code in custom code editor
        const codeTextarea = document.getElementById('code-textarea');
        if (codeTextarea) {
            content = codeTextarea.value;
            const formatted = html_beautify(content, {
                indent_size: 2,
                wrap_line_length: 120,
                preserve_newlines: true
            });
            codeTextarea.value = formatted;
        }
    } else {
        // Format code in visual mode
        content = tinymceEditor.getContent();
        const formatted = html_beautify(content, {
            indent_size: 2,
            wrap_line_length: 120,
            preserve_newlines: true
        });
        tinymceEditor.setContent(formatted);
    }
}

function convertPathsForDisplay(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');

    // Convert ALL relative paths to absolute URLs for proper display
    content = content.replace(/\b(src|href)="(?!https?:\/\/|\/\/|data:|mailto:|tel:|#)([^"]+)"/gi, function(match, attribute, path) {
        // Skip if already converted
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        if (path.startsWith('/')) {
            return `${attribute}="${window.location.origin}${path}"`;
        }
        return `${attribute}="${baseUrl}/${path}"`;
    });

    // Also convert CSS background images
    content = content.replace(/background-image:\s*url\(["']?(?!https?:\/\/|\/\/|data:)([^"')]+)["']?\)/gi, function(match, path) {
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        if (path.startsWith('/')) {
            return match.replace(path, `${window.location.origin}${path}`);
        }
        return match.replace(path, `${baseUrl}/${path}`);
    });

    console.log('Converted paths for display:', content.substring(0, 200) + '...');
    return content;
}

function convertPathsForSaving(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');
    const baseUrlEscaped = baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const originEscaped = window.location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Convert absolute URLs back to relative paths
    content = content.replace(new RegExp(`\\b(src|href)="${baseUrlEscaped}/([^"]*)"`, 'gi'), '$1="$2"');
    content = content.replace(new RegExp(`\\b(src|href)="${originEscaped}/([^"]*)"`, 'gi'), '$1="/$2"');

    return content;
}

function handleContentChange() {
    if (!isModified && currentFilePath) {
        // First edit - acquire lock
        isModified = true;

        // Update lock status in UI
        updateLockStatus(true, null);

        // Show unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.style.display = 'inline-block';
        }

        // Acquire lock on server
        if (typeof acquireLock === 'function') {
            acquireLock(currentFilePath).then(success => {
                if (!success) {
                    console.error('Failed to acquire lock on server');
                }
            });
        }
    }
}

function updateLockStatus(isLocked, lockInfo) {
    const lockStatus = document.getElementById('lockStatus');
    if (lockStatus) {
        if (isLocked) {
            lockStatus.className = 'lock-status locked';
            lockStatus.innerHTML = '<i class="fas fa-lock"></i> File is locked for editing';
        } else {
            lockStatus.className = 'lock-status not-locked';
            lockStatus.innerHTML = '<i class="fas fa-unlock"></i> File is not locked';
        }
    }
}

// Save functionality
function saveFile() {
    if (!tinymceEditor) return;

    // Get content based on current mode
    let content;

    if (currentMode === 'code') {
        // Get content from custom code textarea
        const codeTextarea = document.getElementById('code-textarea');
        if (codeTextarea) {
            content = codeTextarea.value;
        } else {
            content = tinymceEditor.getContent();
        }
    } else {
        // Get content from visual editor and reconstruct full HTML
        const bodyContent = tinymceEditor.getContent();
        content = reconstructFullContent(bodyContent);
    }

    // Ensure paths are relative for saving
    content = convertPathsForSaving(content);

    // Update the textarea value for form submission
    const textarea = document.getElementById('tinymce-editor');
    if (textarea) {
        textarea.value = content;
    }

    console.log('Saving content:', content.substring(0, 200) + '...');

    // Show loading state
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // Submit the form
    const form = document.getElementById('fileEditForm');
    if (form) {
        form.submit();
    }
}

// Initialize save button
document.addEventListener('DOMContentLoaded', function() {
    const saveButton = document.querySelector('button[name="save_file"]');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            saveFile();
        });
    }
});

// CSS extraction function
function extractCSSFiles(content) {
    const cssFiles = [];
    const cssRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi;
    let match;

    while ((match = cssRegex.exec(content)) !== null) {
        let cssPath = match[1];

        // Convert relative paths to absolute URLs
        if (!cssPath.startsWith('http') && !cssPath.startsWith('//')) {
            if (cssPath.startsWith('/')) {
                cssPath = window.location.origin + cssPath;
            } else {
                const baseUrl = siteBaseUrl.replace(/\/$/, '');
                cssPath = baseUrl + '/' + cssPath;
            }
        }

        cssFiles.push(cssPath);
    }

    console.log('Extracted CSS files:', cssFiles);
    return cssFiles;
}

// Header/Footer protection functions
function extractEditableContent(content) {
    console.log('Original content for extraction:', content.substring(0, 500) + '...');

    // Extract only the body content for visual editing
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        console.log('Found body content:', bodyMatch[1].substring(0, 300) + '...');

        // Remove header and footer from body content
        let bodyContent = bodyMatch[1];
        bodyContent = bodyContent.replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '');
        bodyContent = bodyContent.replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

        // If there's a main tag, extract its content, otherwise use the cleaned body content
        const mainMatch = bodyContent.match(/<main[^>]*>([\s\S]*?)<\/main>/i);
        if (mainMatch) {
            console.log('Found main content:', mainMatch[1].substring(0, 300) + '...');
            return mainMatch[1];
        }

        console.log('Body content after header/footer removal:', bodyContent.substring(0, 300) + '...');
        return bodyContent;
    }

    // If no body tags, remove head section and return the rest
    let editableContent = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    editableContent = editableContent.replace(/<\/?html[^>]*>/gi, '');
    editableContent = editableContent.replace(/<\/?body[^>]*>/gi, '');
    editableContent = editableContent.replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '');
    editableContent = editableContent.replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    // Check for main content
    const mainMatch = editableContent.match(/<main[^>]*>([\s\S]*?)<\/main>/i);
    if (mainMatch) {
        console.log('Found main content (no body):', mainMatch[1].substring(0, 300) + '...');
        return mainMatch[1];
    }

    console.log('Extracted editable content (no body tags):', editableContent.substring(0, 300) + '...');
    return editableContent;
}

function blockHeaderFooterEditing(content) {
    // Completely remove header and footer sections from visual editing
    // They will be preserved in the original content and restored on save
    content = content.replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '');
    content = content.replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    return content;
}

function blockHeaderFooterElements() {
    // Additional protection: make any remaining header/footer elements completely uneditable
    const editor = tinymceEditor;
    if (!editor) return;

    const editorBody = editor.getBody();
    if (!editorBody) return;

    // Find and block any header/footer elements that might have slipped through
    const headers = editorBody.querySelectorAll('header');
    const footers = editorBody.querySelectorAll('footer');

    [...headers, ...footers].forEach(element => {
        element.contentEditable = 'false';
        element.style.cssText = `
            pointer-events: none !important;
            user-select: none !important;
            opacity: 0.5 !important;
            background: #f0f0f0 !important;
            border: 2px dashed #ccc !important;
            position: relative !important;
        `;

        // Add overlay to completely block interaction
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(240, 240, 240, 0.8) !important;
            z-index: 1000 !important;
            pointer-events: all !important;
            cursor: not-allowed !important;
        `;
        overlay.title = 'Header/Footer sections cannot be edited';

        if (element.style.position !== 'relative') {
            element.style.position = 'relative';
        }
        element.appendChild(overlay);
    });
}

function addCodeModeProtection(textarea) {
    if (!textarea) return;

    // Remove any existing protection listeners
    const existingTextarea = document.getElementById('code-textarea-protected');
    if (existingTextarea && existingTextarea !== textarea) {
        // Remove old protection
        existingTextarea.id = 'code-textarea';
    }

    // Mark as protected
    textarea.id = 'code-textarea-protected';

    console.log('Adding code mode protection to textarea with content length:', textarea.value.length);

    // Block editing of header/footer sections in code mode
    let originalValue = textarea.value;
    let isBlocking = false;

    // Store original header/footer content with more specific matching
    const originalHeaderMatch = originalValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
    const originalFooterMatch = originalValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);

    console.log('Original header found:', !!originalHeaderMatch);
    console.log('Original footer found:', !!originalFooterMatch);

    // Only add protection if there are actually header/footer sections to protect
    if (!originalHeaderMatch && !originalFooterMatch) {
        console.log('No header/footer found, skipping protection');
        return;
    }

    textarea.addEventListener('input', function(e) {
        if (isBlocking) return;

        const currentValue = textarea.value;

        // Only check if content is significantly different (not just whitespace changes)
        if (Math.abs(currentValue.length - originalValue.length) < 10) {
            originalValue = currentValue;
            return;
        }

        const currentHeaderMatch = currentValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
        const currentFooterMatch = currentValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);

        // Check if header or footer content has been modified
        let headerChanged = false;
        let footerChanged = false;

        // More precise header comparison
        if (originalHeaderMatch && currentHeaderMatch) {
            if (originalHeaderMatch.length !== currentHeaderMatch.length) {
                headerChanged = true;
            } else {
                for (let i = 0; i < originalHeaderMatch.length; i++) {
                    if (originalHeaderMatch[i] !== currentHeaderMatch[i]) {
                        headerChanged = true;
                        break;
                    }
                }
            }
        } else if (originalHeaderMatch || currentHeaderMatch) {
            headerChanged = true;
        }

        // More precise footer comparison
        if (originalFooterMatch && currentFooterMatch) {
            if (originalFooterMatch.length !== currentFooterMatch.length) {
                footerChanged = true;
            } else {
                for (let i = 0; i < originalFooterMatch.length; i++) {
                    if (originalFooterMatch[i] !== currentFooterMatch[i]) {
                        footerChanged = true;
                        break;
                    }
                }
            }
        } else if (originalFooterMatch || currentFooterMatch) {
            footerChanged = true;
        }

        if (headerChanged || footerChanged) {
            console.log('Header/footer change detected, reverting');
            isBlocking = true;
            textarea.value = originalValue;

            // Show blocking message
            showBlockingMessage('Header and footer sections cannot be edited in code mode.');

            // Reset cursor position
            textarea.setSelectionRange(textarea.value.length, textarea.value.length);

            setTimeout(() => {
                isBlocking = false;
            }, 100);
        } else {
            originalValue = currentValue;
        }
    });
}

function showBlockingMessage(message) {
    // Remove existing message
    const existingMessage = document.querySelector('.blocking-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create blocking message
    const messageDiv = document.createElement('div');
    messageDiv.className = 'blocking-message';
    messageDiv.innerHTML = `<i class="fas fa-ban"></i> ${message}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #dc3545;
        color: white;
        padding: 15px 25px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-size: 14px;
        font-weight: bold;
    `;

    document.body.appendChild(messageDiv);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}

function reconstructFullContent(editedContent) {
    // Get the original full content
    const originalContent = document.getElementById('tinymce-editor').value;

    console.log('Reconstructing full content with edited content:', editedContent.substring(0, 200) + '...');

    // First, try to replace main content
    const originalMainMatch = originalContent.match(/<main[^>]*>([\s\S]*?)<\/main>/i);
    if (originalMainMatch) {
        console.log('Replacing main content');
        return originalContent.replace(originalMainMatch[1], editedContent);
    }

    // If no main tag, try to replace body content (excluding header/footer)
    const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        let originalBodyContent = bodyMatch[1];

        // Extract header and footer from original body
        const headerMatch = originalBodyContent.match(/<header[^>]*>[\s\S]*?<\/header>/i);
        const footerMatch = originalBodyContent.match(/<footer[^>]*>[\s\S]*?<\/footer>/i);

        // Reconstruct body with header + edited content + footer
        let newBodyContent = '';
        if (headerMatch) {
            newBodyContent += headerMatch[0] + '\n\n';
        }
        newBodyContent += editedContent;
        if (footerMatch) {
            newBodyContent += '\n\n' + footerMatch[0];
        }

        console.log('Replacing body content with header/footer preserved');
        return originalContent.replace(bodyMatch[1], newBodyContent);
    }

    // If no body tags, return the edited content as-is
    console.log('No body/main tags found, returning edited content as-is');
    return editedContent;
}

// Export functions for use by other scripts
window.tinymceHtmlEditor = {
    switchToCodeMode,
    switchToVisualMode,
    formatCode,
    saveFile,
    convertPathsForDisplay,
    convertPathsForSaving,
    extractEditableContent,
    blockHeaderFooterEditing
};
