/**
 * TinyMCE HTML Editor
 * Replaces CodeMirror with TinyMCE for better visual editing
 */

// Global variables
let tinymceEditor = null;
let currentMode = 'code';
let isModified = false;
let currentFilePath = '';
let siteBaseUrl = '';

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get current file path and site base URL
    const urlParams = new URLSearchParams(window.location.search);
    currentFilePath = urlParams.get('file') || '';
    
    // Detect site base URL
    const currentPath = window.location.pathname;
    const adminIndex = currentPath.indexOf('/admin/');
    if (adminIndex !== -1) {
        siteBaseUrl = window.location.origin + currentPath.substring(0, adminIndex);
    } else {
        siteBaseUrl = window.location.origin;
    }
    
    console.log('Site Base URL detected:', siteBaseUrl);
    console.log('Current file path:', currentFilePath);

    function initializeEditor() {
        if (document.getElementById('tinymce-editor')) {
            initTinyMCE();
            initModeToggle();
            initVersionHistory();
            loadVersionHistory();
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);
        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

function initTinyMCE() {
    tinymce.init({
        selector: '#tinymce-editor',
        height: 600,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'codesample'
        ],
        toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | code | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        
        // Start in code mode
        init_instance_callback: function(editor) {
            tinymceEditor = editor;
            
            // Set initial mode to code
            editor.mode.set('readonly');
            switchToCodeMode();
            
            // Track changes
            editor.on('change', function() {
                handleContentChange();
            });
            
            // Convert paths when switching modes
            editor.on('BeforeSetContent', function(e) {
                if (currentMode === 'visual') {
                    e.content = convertPathsForDisplay(e.content);
                }
            });
            
            editor.on('GetContent', function(e) {
                if (currentMode === 'visual') {
                    e.content = convertPathsForSaving(e.content);
                }
            });
        },
        
        // Custom setup
        setup: function(editor) {
            // Add custom code mode button
            editor.ui.registry.addButton('codemode', {
                text: 'Code',
                onAction: function() {
                    switchToCodeMode();
                }
            });
            
            // Add custom visual mode button  
            editor.ui.registry.addButton('visualmode', {
                text: 'Visual',
                onAction: function() {
                    switchToVisualMode();
                }
            });
        }
    });
}

function initModeToggle() {
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');
    const formatCodeBtn = document.getElementById('formatCode');
    
    if (codeModeBtn) {
        codeModeBtn.addEventListener('click', switchToCodeMode);
    }
    
    if (visualModeBtn) {
        visualModeBtn.addEventListener('click', switchToVisualMode);
    }
    
    if (formatCodeBtn) {
        formatCodeBtn.addEventListener('click', formatCode);
    }
}

function switchToCodeMode() {
    if (!tinymceEditor) return;
    
    currentMode = 'code';
    
    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');
    
    if (codeModeBtn) {
        codeModeBtn.classList.add('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.remove('active');
    }
    
    // Switch TinyMCE to code mode
    tinymceEditor.mode.set('readonly');
    
    // Get content and convert paths back to relative
    let content = tinymceEditor.getContent();
    content = convertPathsForSaving(content);
    
    // Set content in code mode
    tinymceEditor.setContent(content);
    
    // Enable code plugin
    tinymceEditor.execCommand('mceCodeEditor');
}

function switchToVisualMode() {
    if (!tinymceEditor) return;
    
    currentMode = 'visual';
    
    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');
    
    if (codeModeBtn) {
        codeModeBtn.classList.remove('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.add('active');
    }
    
    // Get content and convert paths to absolute for display
    let content = tinymceEditor.getContent();
    content = convertPathsForDisplay(content);
    
    // Switch TinyMCE to design mode
    tinymceEditor.mode.set('design');
    
    // Set content in visual mode
    tinymceEditor.setContent(content);
}

function formatCode() {
    if (!tinymceEditor || typeof html_beautify === 'undefined') return;
    
    const content = tinymceEditor.getContent();
    const formatted = html_beautify(content, {
        indent_size: 2,
        wrap_line_length: 120,
        preserve_newlines: true
    });
    
    tinymceEditor.setContent(formatted);
}

function convertPathsForDisplay(content) {
    if (!siteBaseUrl || !content) return content;
    
    const baseUrl = siteBaseUrl.replace(/\/$/, '');
    
    // Convert relative paths to absolute URLs for proper display
    content = content.replace(/\b(src|href)="(?!https?:\/\/|\/\/|data:|mailto:|tel:|#)([^"]+)"/gi, function(match, attribute, path) {
        if (path.startsWith('/')) {
            return `${attribute}="${window.location.origin}${path}"`;
        }
        return `${attribute}="${baseUrl}/${path}"`;
    });
    
    return content;
}

function convertPathsForSaving(content) {
    if (!siteBaseUrl || !content) return content;
    
    const baseUrl = siteBaseUrl.replace(/\/$/, '');
    const baseUrlEscaped = baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const originEscaped = window.location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // Convert absolute URLs back to relative paths
    content = content.replace(new RegExp(`\\b(src|href)="${baseUrlEscaped}/([^"]*)"`, 'gi'), '$1="$2"');
    content = content.replace(new RegExp(`\\b(src|href)="${originEscaped}/([^"]*)"`, 'gi'), '$1="/$2"');
    
    return content;
}

function handleContentChange() {
    if (!isModified && currentFilePath) {
        // First edit - acquire lock
        isModified = true;
        
        // Update lock status in UI
        updateLockStatus(true, null);
        
        // Show unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.style.display = 'inline-block';
        }
        
        // Acquire lock on server
        if (typeof acquireLock === 'function') {
            acquireLock(currentFilePath).then(success => {
                if (!success) {
                    console.error('Failed to acquire lock on server');
                }
            });
        }
    }
}

function updateLockStatus(isLocked, lockInfo) {
    const lockStatus = document.getElementById('lockStatus');
    if (lockStatus) {
        if (isLocked) {
            lockStatus.className = 'lock-status locked';
            lockStatus.innerHTML = '<i class="fas fa-lock"></i> File is locked for editing';
        } else {
            lockStatus.className = 'lock-status not-locked';
            lockStatus.innerHTML = '<i class="fas fa-unlock"></i> File is not locked';
        }
    }
}

// Save functionality
function saveFile() {
    if (!tinymceEditor) return;
    
    // Get content and ensure paths are relative for saving
    let content = tinymceEditor.getContent();
    content = convertPathsForSaving(content);
    
    // Update the textarea value for form submission
    const textarea = document.getElementById('tinymce-editor');
    if (textarea) {
        textarea.value = content;
    }
    
    // Show loading state
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
    
    // Submit the form
    const form = document.getElementById('fileEditForm');
    if (form) {
        form.submit();
    }
}

// Initialize save button
document.addEventListener('DOMContentLoaded', function() {
    const saveButton = document.querySelector('button[name="save_file"]');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            saveFile();
        });
    }
});

// Export functions for use by other scripts
window.tinymceHtmlEditor = {
    switchToCodeMode,
    switchToVisualMode,
    formatCode,
    saveFile,
    convertPathsForDisplay,
    convertPathsForSaving
};
