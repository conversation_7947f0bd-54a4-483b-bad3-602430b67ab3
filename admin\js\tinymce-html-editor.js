/**
 * TinyMCE HTML Editor
 * Replaces CodeMirror with TinyMCE for better visual editing
 */

// Global variables
let tinymceEditor = null;
let currentMode = 'code';
let isModified = false;
let currentFilePath = '';
let siteBaseUrl = '';

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get current file path and site base URL
    const urlParams = new URLSearchParams(window.location.search);
    currentFilePath = urlParams.get('file') || '';

    // Detect site base URL
    const currentPath = window.location.pathname;
    const adminIndex = currentPath.indexOf('/admin/');
    if (adminIndex !== -1) {
        siteBaseUrl = window.location.origin + currentPath.substring(0, adminIndex);
    } else {
        siteBaseUrl = window.location.origin;
    }

    console.log('Site Base URL detected:', siteBaseUrl);
    console.log('Current file path:', currentFilePath);

    function initializeEditor() {
        if (document.getElementById('tinymce-editor')) {
            initTinyMCE();
            initModeToggle();
            initVersionHistory();
            loadVersionHistory();
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);
        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

function initTinyMCE() {
    // Extract CSS files from the original HTML content
    const originalContent = document.getElementById('tinymce-editor').value;
    const cssFiles = extractCSSFiles(originalContent);

    // Calculate dynamic height based on content
    const contentHeight = calculateContentHeight(originalContent);

    tinymce.init({
        selector: '#tinymce-editor',
        height: contentHeight,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | fullscreen | help',

        // Load CSS files from the HTML content for proper styling
        content_css: cssFiles.length > 0 ? cssFiles : false,
        content_style: cssFiles.length === 0 ? 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; line-height:1.6; margin:20px; }' : '',

        // Auto-resize to content
        autoresize_min_height: 400,
        autoresize_max_height: 2000,
        autoresize_bottom_margin: 50,

        // Allow editing
        readonly: false,

        // Start in visual mode by default
        init_instance_callback: function(editor) {
            tinymceEditor = editor;

            // Start in visual mode
            setTimeout(() => {
                switchToVisualMode();
            }, 100);

            // Track changes
            editor.on('change input', function() {
                handleContentChange();
                // Auto-resize after content changes
                setTimeout(() => {
                    autoResizeEditor();
                }, 100);
            });

            // Convert paths and block header/footer when setting content for visual mode
            editor.on('BeforeSetContent', function(e) {
                if (currentMode === 'visual') {
                    e.content = convertPathsForDisplay(e.content);
                    e.content = blockHeaderFooterEditing(e.content);
                }
            });

            // Block header/footer editing after content is set
            editor.on('SetContent', function() {
                if (currentMode === 'visual') {
                    setTimeout(() => {
                        blockHeaderFooterElements();
                        autoResizeEditor();
                    }, 100);
                }
            });
        },

        // Convert relative URLs to absolute for proper display
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        document_base_url: siteBaseUrl + '/'
    });
}

function initModeToggle() {
    // No mode toggle needed - visual mode only
    console.log('Visual-only mode initialized');
}

function switchToCodeMode() {
    if (!tinymceEditor) return;

    currentMode = 'code';

    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');

    if (codeModeBtn) {
        codeModeBtn.classList.add('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.remove('active');
    }

    // Get current content and convert paths back to relative for code editing
    let content = tinymceEditor.getContent();
    content = convertPathsForSaving(content);

    // Switch to code mode using TinyMCE's code plugin
    tinymceEditor.execCommand('mceCodeEditor');

    // Set the content in the code editor
    setTimeout(() => {
        const codeTextarea = document.querySelector('.tox-textarea');
        if (codeTextarea) {
            codeTextarea.value = content;

            // Add header/footer protection to code mode
            addCodeModeProtection(codeTextarea);
        }
    }, 100);
}

function switchToVisualMode() {
    if (!tinymceEditor) return;

    currentMode = 'visual';

    // Get content from the original textarea (full HTML)
    const originalTextarea = document.getElementById('tinymce-editor');
    let content = originalTextarea ? originalTextarea.value : tinymceEditor.getContent();

    // Convert paths to absolute for proper display
    content = convertPathsForDisplay(content);

    // Extract only body content for visual editing (protect header/footer)
    content = extractEditableContent(content);

    // Set content in visual mode
    tinymceEditor.setContent(content);

    // Auto-resize and add protection
    setTimeout(() => {
        blockHeaderFooterElements();
        autoResizeEditor();
    }, 100);
}

function formatCode() {
    if (!tinymceEditor || typeof html_beautify === 'undefined') return;

    const content = tinymceEditor.getContent();
    const formatted = html_beautify(content, {
        indent_size: 2,
        wrap_line_length: 120,
        preserve_newlines: true
    });

    tinymceEditor.setContent(formatted);
}

function convertPathsForDisplay(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');

    // Convert ALL relative paths to absolute URLs for proper display
    content = content.replace(/\b(src|href)="(?!https?:\/\/|\/\/|data:|mailto:|tel:|#)([^"]+)"/gi, function(match, attribute, path) {
        // Skip if already converted
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        if (path.startsWith('/')) {
            return `${attribute}="${window.location.origin}${path}"`;
        }
        return `${attribute}="${baseUrl}/${path}"`;
    });

    // Also convert CSS background images
    content = content.replace(/background-image:\s*url\(["']?(?!https?:\/\/|\/\/|data:)([^"')]+)["']?\)/gi, function(match, path) {
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        if (path.startsWith('/')) {
            return match.replace(path, `${window.location.origin}${path}`);
        }
        return match.replace(path, `${baseUrl}/${path}`);
    });

    console.log('Converted paths for display:', content.substring(0, 200) + '...');
    return content;
}

function convertPathsForSaving(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');
    const baseUrlEscaped = baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const originEscaped = window.location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Convert absolute URLs back to relative paths
    content = content.replace(new RegExp(`\\b(src|href)="${baseUrlEscaped}/([^"]*)"`, 'gi'), '$1="$2"');
    content = content.replace(new RegExp(`\\b(src|href)="${originEscaped}/([^"]*)"`, 'gi'), '$1="/$2"');

    return content;
}

function handleContentChange() {
    if (!isModified && currentFilePath) {
        // First edit - acquire lock
        isModified = true;

        // Update lock status in UI
        updateLockStatus(true, null);

        // Show unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.style.display = 'inline-block';
        }

        // Acquire lock on server
        if (typeof acquireLock === 'function') {
            acquireLock(currentFilePath).then(success => {
                if (!success) {
                    console.error('Failed to acquire lock on server');
                }
            });
        }
    }
}

function updateLockStatus(isLocked, lockInfo) {
    const lockStatus = document.getElementById('lockStatus');
    if (lockStatus) {
        if (isLocked) {
            lockStatus.className = 'lock-status locked';
            lockStatus.innerHTML = '<i class="fas fa-lock"></i> File is locked for editing';
        } else {
            lockStatus.className = 'lock-status not-locked';
            lockStatus.innerHTML = '<i class="fas fa-unlock"></i> File is not locked';
        }
    }
}

// Save functionality
function saveFile() {
    if (!tinymceEditor) return;

    // Get content based on current mode
    let content;

    if (currentMode === 'code') {
        // Get content from code textarea if in code mode
        const codeTextarea = document.querySelector('.tox-textarea');
        if (codeTextarea) {
            content = codeTextarea.value;
        } else {
            content = tinymceEditor.getContent();
        }
    } else {
        // Get content from visual editor and reconstruct full HTML
        const bodyContent = tinymceEditor.getContent();
        content = reconstructFullContent(bodyContent);
    }

    // Ensure paths are relative for saving
    content = convertPathsForSaving(content);

    // Update the textarea value for form submission
    const textarea = document.getElementById('tinymce-editor');
    if (textarea) {
        textarea.value = content;
    }

    console.log('Saving content:', content.substring(0, 200) + '...');

    // Show loading state
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // Submit the form
    const form = document.getElementById('fileEditForm');
    if (form) {
        form.submit();
    }
}

// Initialize save button
document.addEventListener('DOMContentLoaded', function() {
    const saveButton = document.querySelector('button[name="save_file"]');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            saveFile();
        });
    }
});

// CSS extraction function
function extractCSSFiles(content) {
    const cssFiles = [];
    const cssRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi;
    let match;

    while ((match = cssRegex.exec(content)) !== null) {
        let cssPath = match[1];

        // Convert relative paths to absolute URLs
        if (!cssPath.startsWith('http') && !cssPath.startsWith('//')) {
            if (cssPath.startsWith('/')) {
                cssPath = window.location.origin + cssPath;
            } else {
                const baseUrl = siteBaseUrl.replace(/\/$/, '');
                cssPath = baseUrl + '/' + cssPath;
            }
        }

        cssFiles.push(cssPath);
    }

    console.log('Extracted CSS files:', cssFiles);
    return cssFiles;
}

// Height calculation function
function calculateContentHeight(content) {
    // Create a temporary div to measure content height
    const tempDiv = document.createElement('div');
    tempDiv.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 800px;
        font-family: Helvetica, Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        padding: 20px;
        visibility: hidden;
    `;

    // Extract body content for measurement
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    const bodyContent = bodyMatch ? bodyMatch[1] : content;

    tempDiv.innerHTML = bodyContent;
    document.body.appendChild(tempDiv);

    // Get the height and add some padding
    const contentHeight = tempDiv.scrollHeight + 100; // Add 100px padding

    // Remove temporary div
    document.body.removeChild(tempDiv);

    // Set minimum and maximum heights
    const minHeight = 400;
    const maxHeight = 1500;

    const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));

    console.log('Calculated editor height:', finalHeight);
    return finalHeight;
}

// Auto-resize function
function autoResizeEditor() {
    if (!tinymceEditor) return;

    try {
        const editorBody = tinymceEditor.getBody();
        if (!editorBody) return;

        // Get the content height
        const contentHeight = editorBody.scrollHeight;
        const currentHeight = parseInt(tinymceEditor.getContainer().style.height) || 600;

        // Calculate new height with padding
        const newHeight = Math.max(400, Math.min(1500, contentHeight + 100));

        // Only resize if there's a significant difference
        if (Math.abs(newHeight - currentHeight) > 50) {
            tinymceEditor.theme.resizeTo(null, newHeight);
            console.log('Auto-resized editor to:', newHeight);
        }
    } catch (error) {
        console.log('Auto-resize error:', error);
    }
}

// Header/Footer protection functions
function extractEditableContent(content) {
    // Extract only the body content for visual editing
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        return bodyMatch[1];
    }

    // If no body tags, remove head section and return the rest
    let editableContent = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    editableContent = editableContent.replace(/<\/?html[^>]*>/gi, '');
    editableContent = editableContent.replace(/<\/?body[^>]*>/gi, '');

    return editableContent;
}

function blockHeaderFooterEditing(content) {
    // Completely remove header and footer sections from visual editing
    // They will be preserved in the original content and restored on save
    content = content.replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '');
    content = content.replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    return content;
}

function blockHeaderFooterElements() {
    // Additional protection: make any remaining header/footer elements completely uneditable
    const editor = tinymceEditor;
    if (!editor) return;

    const editorBody = editor.getBody();
    if (!editorBody) return;

    // Find and block any header/footer elements that might have slipped through
    const headers = editorBody.querySelectorAll('header');
    const footers = editorBody.querySelectorAll('footer');

    [...headers, ...footers].forEach(element => {
        element.contentEditable = 'false';
        element.style.cssText = `
            pointer-events: none !important;
            user-select: none !important;
            opacity: 0.5 !important;
            background: #f0f0f0 !important;
            border: 2px dashed #ccc !important;
            position: relative !important;
        `;

        // Add overlay to completely block interaction
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(240, 240, 240, 0.8) !important;
            z-index: 1000 !important;
            pointer-events: all !important;
            cursor: not-allowed !important;
        `;
        overlay.title = 'Header/Footer sections cannot be edited';

        if (element.style.position !== 'relative') {
            element.style.position = 'relative';
        }
        element.appendChild(overlay);
    });
}

function addCodeModeProtection(textarea) {
    if (!textarea) return;

    // Block editing of header/footer sections in code mode
    let originalValue = textarea.value;
    let isBlocking = false;

    textarea.addEventListener('input', function(e) {
        if (isBlocking) return;

        const currentValue = textarea.value;
        const headerMatch = currentValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
        const footerMatch = currentValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);
        const originalHeaderMatch = originalValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
        const originalFooterMatch = originalValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);

        // Check if header or footer content has been modified
        let headerChanged = false;
        let footerChanged = false;

        if (headerMatch && originalHeaderMatch) {
            headerChanged = headerMatch[0] !== originalHeaderMatch[0];
        } else if (headerMatch !== originalHeaderMatch) {
            headerChanged = true;
        }

        if (footerMatch && originalFooterMatch) {
            footerChanged = footerMatch[0] !== originalFooterMatch[0];
        } else if (footerMatch !== originalFooterMatch) {
            footerChanged = true;
        }

        if (headerChanged || footerChanged) {
            isBlocking = true;
            textarea.value = originalValue;

            // Show blocking message
            showBlockingMessage('Header and footer sections cannot be edited in code mode.');

            setTimeout(() => {
                isBlocking = false;
            }, 100);
        } else {
            originalValue = currentValue;
        }
    });
}

function showBlockingMessage(message) {
    // Remove existing message
    const existingMessage = document.querySelector('.blocking-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create blocking message
    const messageDiv = document.createElement('div');
    messageDiv.className = 'blocking-message';
    messageDiv.innerHTML = `<i class="fas fa-ban"></i> ${message}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #dc3545;
        color: white;
        padding: 15px 25px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-size: 14px;
        font-weight: bold;
    `;

    document.body.appendChild(messageDiv);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}

function reconstructFullContent(bodyContent) {
    // Get the original full content
    const originalContent = document.getElementById('tinymce-editor').value;

    // Replace only the body content
    const bodyMatch = originalContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        return originalContent.replace(bodyMatch[1], bodyContent);
    }

    // If no body tags, return the body content as-is
    return bodyContent;
}

// Export functions for use by other scripts
window.tinymceHtmlEditor = {
    switchToCodeMode,
    switchToVisualMode,
    formatCode,
    saveFile,
    convertPathsForDisplay,
    convertPathsForSaving,
    extractEditableContent,
    restrictHeaderFooterEditing
};
