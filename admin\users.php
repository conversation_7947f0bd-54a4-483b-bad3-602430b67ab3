<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Check if user is admin
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    redirect('dashboard.php');
}

// Pagination settings
// Get page number from URL, default to 1
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;

// Check if per_page is set in the URL
if (isset($_GET['per_page']) && is_numeric($_GET['per_page'])) {
    $items_per_page = (int)$_GET['per_page'];
    // Ensure it's a valid value
    $valid_per_page_values = [5, 10, 25, 50, 100];
    if (!in_array($items_per_page, $valid_per_page_values)) {
        $items_per_page = 10; // Default if invalid
    }
} else {
    // Get user's items_per_page setting if available
    $items_per_page = 10; // Default value
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $settings_query = "SELECT items_per_page FROM user_settings WHERE user_id = $user_id";
        $settings_result = $conn->query($settings_query);

        if ($settings_result && $settings_result->num_rows > 0) {
            $user_settings = $settings_result->fetch_assoc();
            $items_per_page = (int)$user_settings['items_per_page'];
        }
    }
}

// Calculate offset for pagination
$offset = ($page - 1) * $items_per_page;

// Get total count of users
$count_sql = "SELECT COUNT(*) as total FROM users";
$count_result = $conn->query($count_sql);
$count_row = $count_result->fetch_assoc();
$total_items = $count_row['total'];

// Get paginated users
$sql = "SELECT * FROM users ORDER BY id ASC LIMIT $offset, $items_per_page";
$result = $conn->query($sql);

$success_message = '';
$error_message = '';

// Get all available roles
$roles_sql = "SELECT id, name FROM roles ORDER BY name";
$roles_result = $conn->query($roles_sql);
$available_roles = [];
if ($roles_result && $roles_result->num_rows > 0) {
    while ($role = $roles_result->fetch_assoc()) {
        $available_roles[$role['id']] = $role['name'];
    }
}

// Process form submission for creating a new user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_user'])) {
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $send_verification = isset($_POST['send_verification']) ? 1 : 0;
    $selected_role = isset($_POST['role_id']) ? (int)$_POST['role_id'] : 0;

    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        $error_message = "Username, email, and password are required";
    } elseif ($password !== $confirm_password) {
        $error_message = "Passwords do not match";
    } elseif ($selected_role === 0) {
        $error_message = "Please select a role for the user";
    } else {
        // Check if username or email already exists
        $check_sql = "SELECT id FROM users WHERE username = '$username' OR email = '$email'";
        $check_result = $conn->query($check_sql);

        if ($check_result->num_rows > 0) {
            $error_message = "Username or email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                // Check if we should send verification email
                if ($send_verification) {
                    // Generate verification token
                    $verification_token = bin2hex(random_bytes(32));
                    $token_expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

                    // Insert new user with verification token
                    $insert_sql = "INSERT INTO users (username, email, password, is_admin, is_verified, verification_token, token_expiry)
                                   VALUES ('$username', '$email', '$hashed_password', $is_admin, 0, '$verification_token', '$token_expiry')";
                } else {
                    // Insert new user without verification (already verified)
                    $insert_sql = "INSERT INTO users (username, email, password, is_admin, is_verified)
                                   VALUES ('$username', '$email', '$hashed_password', $is_admin, 1)";
                }

                // Check if verification_token column exists in users table
                $check_column_sql = "SHOW COLUMNS FROM users LIKE 'verification_token'";
                $check_column_result = $conn->query($check_column_sql);

                if ($check_column_result->num_rows == 0) {
                    // Add verification_token column if it doesn't exist
                    $add_column_sql = "ALTER TABLE users ADD COLUMN verification_token VARCHAR(255) DEFAULT NULL AFTER is_verified";
                    $conn->query($add_column_sql);
                }

                // Check if token_expiry column exists in users table
                $check_column_sql = "SHOW COLUMNS FROM users LIKE 'token_expiry'";
                $check_column_result = $conn->query($check_column_sql);

                if ($check_column_result->num_rows == 0) {
                    // Add token_expiry column if it doesn't exist
                    $add_column_sql = "ALTER TABLE users ADD COLUMN token_expiry DATETIME DEFAULT NULL AFTER verification_token";
                    $conn->query($add_column_sql);
                }

                $conn->query($insert_sql);

                // Get the new user ID
                $new_user_id = $conn->insert_id;

                // Assign role to the user
                $role_sql = "INSERT INTO user_roles (user_id, role_id) VALUES ($new_user_id, $selected_role)";
                $conn->query($role_sql);

                // Send verification email if requested
                $email_sent = false;
                if ($send_verification) {
                    require_once 'lib/EmailSender.php';
                    $emailSender = new EmailSender($conn);

                    // Get site URL from settings
                    $site_url = '';
                    $site_name = '';
                    $site_url_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_url'";
                    $site_name_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'site_name'";

                    $site_url_result = $conn->query($site_url_query);
                    if ($site_url_result && $site_url_result->num_rows > 0) {
                        $site_url = $site_url_result->fetch_assoc()['setting_value'];
                    } else {
                        $site_url = 'https://manageinc.redconic.com';
                    }

                    $site_name_result = $conn->query($site_name_query);
                    if ($site_name_result && $site_name_result->num_rows > 0) {
                        $site_name = $site_name_result->fetch_assoc()['setting_value'];
                    } else {
                        $site_name = 'Manage Inc.';
                    }

                    // Create verification URL with absolute path
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                    $host = $_SERVER['HTTP_HOST'];

                    // Get the document root path
                    $doc_root = $_SERVER['DOCUMENT_ROOT'];

                    // Get the current script's directory path (admin)
                    $admin_path = dirname(__FILE__);

                    // Get the relative path from document root to the current directory
                    $relative_path = str_replace('\\', '/', str_replace($doc_root, '', $admin_path));

                    // Build the verification URL with the correct path structure
                    // This will create a URL like: https://redconic.com/manageinc-html/admin/verify.php?token=xyz
                    $verification_url = $protocol . $host . $relative_path . '/verify.php?token=' . $verification_token;

                    // Clean up the URL (remove double slashes, etc.)
                    $verification_url = preg_replace('#([^:])//+#', '$1/', $verification_url);

                    // Log the verification URL for debugging
                    error_log("Verification URL: " . $verification_url);

                    // Email subject and content
                    $subject = "Verify Your Account - " . $site_name;
                    $message = "
                    <html>
                    <head>
                        <title>Verify Your Account</title>
                    </head>
                    <body>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;'>
                            <div style='text-align: center; margin-bottom: 20px;'>
                                <img src='{$site_url}/images/login-logo.jpg' alt='{$site_name}' style='max-width: 200px;'>
                            </div>
                            <div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px;'>
                                <h2 style='color: #333; margin-top: 0;'>Welcome to {$site_name}!</h2>
                                <p>Hello {$username},</p>
                                <p>Thank you for creating an account with us. Please click the button below to verify your email address:</p>
                                <div style='text-align: center; margin: 30px 0;'>
                                    <a href='{$verification_url}' style='background-color: #f1ca2f; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Verify Your Email</a>
                                </div>
                                <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                                <p style='word-break: break-all;'><a href='{$verification_url}'>{$verification_url}</a></p>
                                <p>This link will expire in 24 hours.</p>
                                <p>If you did not create an account, please ignore this email.</p>
                            </div>
                            <div style='margin-top: 20px; text-align: center; color: #777; font-size: 12px;'>
                                <p>&copy; " . date('Y') . " {$site_name}. All rights reserved.</p>
                            </div>
                        </div>
                    </body>
                    </html>
                    ";

                    // Send the email
                    $email_sent = $emailSender->sendEmail($email, $subject, $message);

                    // Log the email sending result
                    error_log("Verification email sending result: " . ($email_sent ? "Success" : "Failed") . " to " . $email);
                }

                // Commit transaction
                $conn->commit();

                // Log the activity
                require_once 'includes/admin-functions.php';
                log_activity('create', 'Created user: ' . $username . ' with role: ' . $available_roles[$selected_role], $_SESSION['user_id']);

                if ($send_verification) {
                    if ($email_sent) {
                        $success_message = "User created successfully with role: " . $available_roles[$selected_role] . ". A verification email has been sent to " . $email;
                    } else {
                        $success_message = "User created successfully with role: " . $available_roles[$selected_role] . ", but we couldn't send the verification email. You can resend it from the user list.";
                    }
                } else {
                    $success_message = "User created successfully with role: " . $available_roles[$selected_role] . ". The user is already verified and can log in immediately.";
                }

                // Refresh the user list
                $result = $conn->query($sql);
            } catch (Exception $e) {
                // Rollback on error
                $conn->rollback();
                $error_message = "Error creating user: " . $e->getMessage();
            }
        }
    }
}

// Process form submission for editing a user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_user'])) {
    $user_id = (int)$_POST['user_id'];
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $selected_role = isset($_POST['role_id']) ? (int)$_POST['role_id'] : 0;
    $change_password = isset($_POST['change_password']) && $_POST['change_password'] == '1';

    // Validate input
    if (empty($username) || empty($email)) {
        $error_message = "Username and email are required";
    } elseif ($selected_role === 0) {
        $error_message = "Please select a role for the user";
    } else {
        // Check if username or email already exists for other users
        $check_sql = "SELECT id FROM users WHERE (username = '$username' OR email = '$email') AND id != $user_id";
        $check_result = $conn->query($check_sql);

        if ($check_result->num_rows > 0) {
            $error_message = "Username or email already exists for another user";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Update user information
                if ($change_password && !empty($_POST['password'])) {
                    // Validate password
                    if ($_POST['password'] !== $_POST['confirm_password']) {
                        throw new Exception("Passwords do not match");
                    }

                    // Hash new password
                    $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);

                    // Update user with new password
                    $update_sql = "UPDATE users SET username = '$username', email = '$email', password = '$hashed_password', is_admin = $is_admin WHERE id = $user_id";
                } else {
                    // Update user without changing password
                    $update_sql = "UPDATE users SET username = '$username', email = '$email', is_admin = $is_admin WHERE id = $user_id";
                }

                $conn->query($update_sql);

                // Update user role
                // First, delete existing roles
                $delete_role_sql = "DELETE FROM user_roles WHERE user_id = $user_id";
                $conn->query($delete_role_sql);

                // Then, assign the new role
                $role_sql = "INSERT INTO user_roles (user_id, role_id) VALUES ($user_id, $selected_role)";
                $conn->query($role_sql);

                // Commit transaction
                $conn->commit();

                // Log the activity
                require_once 'includes/admin-functions.php';
                log_activity('update', 'Updated user: ' . $username . ' with role: ' . $available_roles[$selected_role], $_SESSION['user_id']);

                $success_message = "User updated successfully with role: " . $available_roles[$selected_role];

                // Refresh the user list
                $result = $conn->query($sql);
            } catch (Exception $e) {
                // Rollback on error
                $conn->rollback();
                $error_message = "Error updating user: " . $e->getMessage();
            }
        }
    }
}

// Process user deletion
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $user_id = (int)$_GET['id'];

    // Don't allow deleting yourself
    if ($user_id == $_SESSION['user_id']) {
        $error_message = "You cannot delete your own account";
    } else {
        // Get user info before deletion for logging
        $user_info_sql = "SELECT username FROM users WHERE id = $user_id";
        $user_info_result = $conn->query($user_info_sql);
        $deleted_username = '';
        if ($user_info_result && $user_info_result->num_rows > 0) {
            $user_info = $user_info_result->fetch_assoc();
            $deleted_username = $user_info['username'];
        }

        $delete_sql = "DELETE FROM users WHERE id = $user_id";
        if ($conn->query($delete_sql)) {
            // Log the activity
            require_once 'includes/admin-functions.php';
            log_activity('delete', 'Deleted user: ' . $deleted_username, $_SESSION['user_id']);

            $success_message = "User deleted successfully";

            // Refresh the user list
            $result = $conn->query($sql);
        } else {
            $error_message = "Error deleting user: " . $conn->error;
        }
    }
}

// Process resend verification email
if (isset($_GET['action']) && $_GET['action'] == 'resend_verification' && isset($_GET['id'])) {
    $user_id = (int)$_GET['id'];

    // Redirect to admin-resend-verification.php
    redirect("admin-resend-verification.php?user_id=$user_id");
}

// Set page title
$page_title = "User Management";

// Add page-specific CSS
$extra_css = '<link rel="stylesheet" href="assets/css/pages/users.css?v=' . time() . '">';

// Add page-specific body class
$body_class = 'page-users';
?>

<?php include 'includes/header.php'; ?>

<div class="admin-container">
    <div class="admin-content-header">
        <div class="admin-content-title-group">
            <h2 class="admin-content-title"><i class="fas fa-users"></i> User Management</h2>
            <p class="admin-content-subtitle">Manage user accounts and permissions</p>
        </div>
        <div class="admin-content-actions">
            <a href="user.php" class="admin-btn">
                <i class="fas fa-user-plus"></i> Add New User
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <?php if ($result->num_rows > 0): ?>
        <div class="admin-table-responsive">
            <table class="admin-table wp-style">
                <thead>
                    <tr>
                        <th class="username-column">Username</th>
                        <th class="email-column">Email</th>
                        <th class="role-column">Role</th>
                        <th class="date-column">Created</th>
                        <th class="actions-column">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr data-title="<?php echo $row['username']; ?>">
                            <td class="username-column" data-label="Username">
                                <div class="user-title-area">
                                    <div class="user-avatar">
                                        <?php if (!empty($row['profile_image']) && file_exists("../images/profiles/" . $row['profile_image'])): ?>
                                            <img src="../images/profiles/<?php echo $row['profile_image']; ?>" alt="<?php echo $row['username']; ?>">
                                        <?php else: ?>
                                            <i class="fas fa-user-circle"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="user-info">
                                        <strong><?php echo $row['username']; ?></strong>
                                        <?php if ($row['id'] == $_SESSION['user_id']): ?>
                                            <span class="user-badge current">You</span>
                                        <?php endif; ?>
                                        <div class="row-actions">
                                            <span class="edit"><a href="user.php?id=<?php echo $row['id']; ?>">Edit</a> | </span>
                                            <?php if ($row['id'] != $_SESSION['user_id']): ?>
                                                <span class="trash"><a href="?action=delete&id=<?php echo $row['id']; ?>" onclick="return confirm('Are you sure you want to delete this user?')">Delete</a> | </span>
                                            <?php endif; ?>
                                            <?php if ($row['is_verified'] == 0): ?>
                                                <span class="verify"><a href="?action=resend_verification&id=<?php echo $row['id']; ?>">Verify</a></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="email-column" data-label="Email"><?php echo $row['email']; ?></td>
                            <td class="role-column" data-label="Role">
                                <?php
                                // Get user roles
                                $user_id = $row['id'];
                                $user_roles_sql = "SELECT r.name FROM roles r
                                                  JOIN user_roles ur ON r.id = ur.role_id
                                                  WHERE ur.user_id = $user_id";
                                $user_roles_result = $conn->query($user_roles_sql);

                                if ($user_roles_result && $user_roles_result->num_rows > 0) {
                                    while ($role = $user_roles_result->fetch_assoc()) {
                                        $role_class = ($role['name'] == 'Administrator') ? 'admin' : '';
                                        echo '<span class="user-badge ' . $role_class . '">' . $role['name'] . '</span> ';
                                    }
                                } else {
                                    // Fallback to old method if no roles found
                                    if ($row['is_admin'] == 1) {
                                        echo '<span class="user-badge admin">Administrator</span>';
                                    } else {
                                        echo '<span class="user-badge">User</span>';
                                    }
                                }
                                ?>
                            </td>
                            <td class="date-column" data-label="Created">
                                <div class="user-date">
                                    <span class="date-display"><?php echo date('Y/m/d', strtotime($row['created_at'])); ?></span>
                                    <span class="time-display"><?php echo date('g:i a', strtotime($row['created_at'])); ?></span>
                                </div>
                            </td>
                            <td class="actions-column" data-label="Actions">
                                <div class="user-actions">
                                    <a href="user.php?id=<?php echo $row['id']; ?>" class="admin-btn" title="Edit"><i class="fas fa-edit"></i></a>
                                    <?php if ($row['id'] != $_SESSION['user_id']): ?>
                                        <a href="?action=delete&id=<?php echo $row['id']; ?>" class="admin-btn danger" title="Delete" onclick="return confirm('Are you sure you want to delete this user?')"><i class="fas fa-trash-alt"></i></a>
                                    <?php endif; ?>
                                    <?php if ($row['is_verified'] == 0): ?>
                                        <a href="?action=resend_verification&id=<?php echo $row['id']; ?>" class="admin-btn" title="Verify Email"><i class="fas fa-paper-plane"></i></a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <?php
            // Set up pagination parameters
            $base_url = 'users.php';
            $query_params = [];

            // Include the pagination component
            include 'includes/pagination.php';
            ?>
        </div>
    <?php else: ?>
        <div class="admin-alert info">
            <i class="fas fa-info-circle"></i> No users found.
        </div>
    <?php endif; ?>
</div>

<!-- Create User Modal -->
<div class="admin-modal" id="createUserModal">
    <div class="admin-modal-dialog">
        <div class="admin-modal-content">
            <div class="admin-modal-header">
                <h3><i class="fas fa-user-plus"></i> Create New User</h3>
                <button type="button" class="admin-modal-close" data-dismiss="admin-modal">&times;</button>
            </div>
            <div class="admin-modal-body">
                <form class="admin-modal-form" method="post" action="">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-container">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="password-generate" onclick="generatePassword('password'); copyToConfirm();">
                                <i class="fas fa-key"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <div class="password-container">
                            <input type="password" id="confirm_password" name="confirm_password" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="role_id">User Role</label>
                        <select id="role_id" name="role_id" required>
                            <option value="">-- Select a role --</option>
                            <?php foreach ($available_roles as $role_id => $role_name): ?>
                            <option value="<?php echo $role_id; ?>"><?php echo $role_name; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="form-hint">Select the appropriate role for this user.</p>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="is_admin" value="1">
                            <span>Administrator privileges</span>
                        </label>
                        <p class="form-hint">This grants access to the admin panel. Role permissions still apply.</p>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="send_verification" value="1" checked>
                            <span>Send verification email</span>
                        </label>
                        <p class="form-hint">The user will need to verify their email address before they can log in.</p>
                    </div>

                    <input type="hidden" name="create_user" value="1">
                </form>
            </div>
            <div class="admin-modal-footer">
                <button type="button" class="admin-modal-btn admin-modal-btn-secondary" data-dismiss="admin-modal">Cancel</button>
                <button type="button" class="admin-modal-btn admin-modal-btn-primary" onclick="document.querySelector('#createUserModal .admin-modal-form').submit();">Create User</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="admin-modal" id="editUserModal">
    <div class="admin-modal-dialog">
        <div class="admin-modal-content">
            <div class="admin-modal-header">
                <h3><i class="fas fa-user-edit"></i> Edit User</h3>
                <button type="button" class="admin-modal-close" data-dismiss="admin-modal">&times;</button>
            </div>
            <div class="admin-modal-body">
                <form class="admin-modal-form" method="post" action="">
                    <input type="hidden" id="edit_user_id" name="user_id" value="">

                    <div class="form-group">
                        <label for="edit_username">Username</label>
                        <input type="text" id="edit_username" name="username" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_email">Email</label>
                        <input type="email" id="edit_email" name="email" required>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="change_password" name="change_password" value="1" onchange="togglePasswordFields()">
                            <span>Change Password</span>
                        </label>
                    </div>

                    <div id="password_fields" class="hidden">
                        <div class="form-group">
                            <label for="edit_password">New Password</label>
                            <div class="password-container">
                                <input type="password" id="edit_password" name="password">
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('edit_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="password-generate" onclick="generatePassword('edit_password'); copyToEditConfirm();">
                                    <i class="fas fa-key"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit_confirm_password">Confirm New Password</label>
                            <div class="password-container">
                                <input type="password" id="edit_confirm_password" name="confirm_password">
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('edit_confirm_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_role_id">User Role</label>
                        <select id="edit_role_id" name="role_id" required>
                            <option value="">-- Select a role --</option>
                            <?php foreach ($available_roles as $role_id => $role_name): ?>
                            <option value="<?php echo $role_id; ?>"><?php echo $role_name; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="form-hint">Select the appropriate role for this user.</p>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="edit_is_admin" name="is_admin" value="1">
                            <span>Administrator privileges</span>
                        </label>
                        <p class="form-hint">This grants access to the admin panel. Role permissions still apply.</p>
                    </div>

                    <input type="hidden" name="edit_user" value="1">
                </form>
            </div>
            <div class="admin-modal-footer">
                <button type="button" class="admin-modal-btn admin-modal-btn-secondary" data-dismiss="admin-modal">Cancel</button>
                <button type="button" class="admin-modal-btn admin-modal-btn-primary" onclick="document.querySelector('#editUserModal .admin-modal-form').submit();">Update User</button>
            </div>
        </div>
    </div>
</div>

<!-- Styles moved to users.css -->
<style>
/* Modal loader */
.modal-loader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.modal-loader i {
    font-size: 2rem;
    color: #f1ca2f;
}

/* Loading state for modal */
.admin-modal.loading .admin-modal-content {
    position: relative;
}
</style>

<script>
    // Modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        const modalTriggers = document.querySelectorAll('[data-toggle="admin-modal"]');
        const modalClosers = document.querySelectorAll('[data-dismiss="admin-modal"]');

        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', function() {
                const targetModal = document.querySelector('#' + this.getAttribute('data-target'));
                if (targetModal) {
                    targetModal.classList.add('show');
                }
            });
        });

        modalClosers.forEach(closer => {
            closer.addEventListener('click', function() {
                const modal = this.closest('.admin-modal');
                if (modal) {
                    modal.classList.remove('show');
                }
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('admin-modal')) {
                event.target.classList.remove('show');
            }
        });
    });

    // Toggle password visibility
    function togglePasswordVisibility(fieldId) {
        var field = document.getElementById(fieldId);
        var icon = event.currentTarget.querySelector('i');

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Generate a strong password
    function generatePassword(fieldId) {
        var length = 12;
        var charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
        var password = "";

        // Ensure at least one character from each category
        password += getRandomChar("abcdefghijklmnopqrstuvwxyz");
        password += getRandomChar("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        password += getRandomChar("0123456789");
        password += getRandomChar("!@#$%^&*()_+");

        // Fill the rest of the password
        for (var i = 4; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        // Shuffle the password
        password = shuffleString(password);

        // Set the password field value
        document.getElementById(fieldId).value = password;

        // Show the password
        var field = document.getElementById(fieldId);
        var icon = document.querySelector('#' + fieldId).parentNode.querySelector('.password-toggle i');
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    }

    // Get a random character from a string
    function getRandomChar(str) {
        return str.charAt(Math.floor(Math.random() * str.length));
    }

    // Shuffle a string
    function shuffleString(str) {
        var arr = str.split('');
        for (var i = arr.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            var temp = arr[i];
            arr[i] = arr[j];
            arr[j] = temp;
        }
        return arr.join('');
    }

    // Copy password to confirm password field
    function copyToConfirm() {
        var password = document.getElementById('password').value;
        document.getElementById('confirm_password').value = password;
    }

    // Copy password to edit confirm password field
    function copyToEditConfirm() {
        var password = document.getElementById('edit_password').value;
        document.getElementById('edit_confirm_password').value = password;
    }

    // Toggle password fields visibility based on checkbox
    function togglePasswordFields() {
        var passwordFields = document.getElementById('password_fields');
        var changePasswordCheckbox = document.getElementById('change_password');

        if (changePasswordCheckbox.checked) {
            passwordFields.classList.remove('hidden');
        } else {
            passwordFields.classList.add('hidden');
            // Clear password fields
            document.getElementById('edit_password').value = '';
            document.getElementById('edit_confirm_password').value = '';
        }
    }

    // Open edit user modal and populate with user data
    function openEditUserModal(userId) {
        // Prevent default action if called from a link
        if (event) {
            event.preventDefault();
        }

        console.log('Opening edit modal for user ID:', userId);

        // Show loading state
        const editModal = document.getElementById('editUserModal');
        if (editModal) {
            editModal.classList.add('loading');

            // Show the modal first, then load data
            editModal.classList.add('show');

            // Add loading indicator
            const modalContent = editModal.querySelector('.admin-modal-content');
            if (modalContent) {
                if (!document.getElementById('modal-loader')) {
                    const loader = document.createElement('div');
                    loader.id = 'modal-loader';
                    loader.className = 'modal-loader';
                    loader.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    modalContent.appendChild(loader);
                } else {
                    document.getElementById('modal-loader').style.display = 'flex';
                }
            }
        }

        // Fetch user data via AJAX
        fetch('ajax/get_user.php?id=' + userId)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data);

                if (data.success) {
                    // Populate form fields
                    document.getElementById('edit_user_id').value = data.user.id;
                    document.getElementById('edit_username').value = data.user.username;
                    document.getElementById('edit_email').value = data.user.email;
                    document.getElementById('edit_is_admin').checked = data.user.is_admin == 1;

                    // Set role
                    const roleSelect = document.getElementById('edit_role_id');
                    for (let i = 0; i < roleSelect.options.length; i++) {
                        if (roleSelect.options[i].value == data.user.role_id) {
                            roleSelect.selectedIndex = i;
                            break;
                        }
                    }

                    // Reset password change fields
                    document.getElementById('change_password').checked = false;
                    document.getElementById('password_fields').classList.add('hidden');
                } else {
                    console.error('Error in data:', data.message);
                    alert('Error loading user data: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching user data:', error);
                alert('Error loading user data. Please try again.');
            })
            .finally(() => {
                // Remove loading state
                if (editModal) {
                    editModal.classList.remove('loading');

                    // Hide loader
                    const loader = document.getElementById('modal-loader');
                    if (loader) {
                        loader.style.display = 'none';
                    }
                }
            });
    }
</script>

<?php include 'includes/footer.php'; ?>
