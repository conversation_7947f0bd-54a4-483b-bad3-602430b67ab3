/* Enhanced Editor Styles */
.enhanced-editor-container {
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.enhanced-editor-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    border-radius: 0;
    border: none;
    box-shadow: none;
}

/* Toolbar Styles */
.enhanced-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 50px;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 6px;
}

.toolbar-left {
    flex: 1;
    flex-wrap: wrap;
    gap: 4px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-group {
    display: flex;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.toolbar-btn {
    background: #fff;
    border: none;
    border-right: 1px solid #dee2e6;
    padding: 8px 10px;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.toolbar-btn:last-child {
    border-right: none;
}

.toolbar-btn:hover {
    background: #e9ecef;
    color: #212529;
    transform: translateY(-1px);
}

.toolbar-btn:active {
    transform: translateY(0);
    background: #dee2e6;
}

.toolbar-btn.active {
    background: #007bff;
    color: #fff;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-btn.text-btn {
    font-weight: 600;
    min-width: 40px;
    font-size: 12px;
}

/* View Toggle Group */
.view-toggle-group {
    display: flex;
    border: 1px solid #007bff;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 8px;
    background: #fff;
}

.editor-btn {
    background: #fff;
    border: none;
    border-right: 1px solid #007bff;
    padding: 8px 14px;
    cursor: pointer;
    font-size: 13px;
    color: #007bff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.editor-btn:last-child {
    border-right: none;
}

.editor-btn:hover {
    background: #e3f2fd;
}

.editor-btn.active {
    background: #007bff;
    color: #fff;
}

.editor-btn.primary {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.editor-btn.primary:hover {
    background: #0056b3;
}

.editor-btn.secondary {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.editor-btn.secondary:hover {
    background: #545b62;
}

/* Editor Content Area */
.editor-wrapper {
    position: relative;
    background: #fff;
}

.visual-editor {
    padding: 20px;
    min-height: 300px;
    outline: none;
    line-height: 1.7;
    font-size: 15px;
    overflow-y: auto;
    font-family: Georgia, 'Times New Roman', serif;
    color: #212529;
}

.visual-editor:empty:before {
    content: attr(data-placeholder);
    color: #6c757d;
    font-style: italic;
    opacity: 0.7;
}

.visual-editor:focus {
    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.code-editor {
    width: 100%;
    padding: 20px;
    border: none;
    outline: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    background: #f8f9fa;
    color: #495057;
    tab-size: 2;
}

.code-editor:focus {
    background: #fff;
    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* Status Bar */
.editor-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 12px;
    color: #6c757d;
    min-height: 36px;
}

.word-count {
    font-weight: 600;
    color: #495057;
}

.autosave-indicator {
    font-weight: 600;
    color: #28a745;
    display: flex;
    align-items: center;
    gap: 4px;
}

.autosave-indicator:before {
    content: '●';
    font-size: 8px;
}

.autosave-indicator.unsaved {
    color: #ffc107;
}

/* Dialog Styles */
.editor-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.editor-dialog {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: dialogSlideIn 0.3s ease;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.editor-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.editor-dialog-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.editor-dialog-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.editor-dialog-close:hover {
    background: #e9ecef;
    color: #495057;
}

.editor-dialog-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.editor-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-editor-toolbar {
        flex-direction: column;
        align-items: stretch;
        padding: 12px;
    }
    
    .toolbar-section {
        justify-content: center;
        margin-bottom: 8px;
    }
    
    .toolbar-left {
        order: 2;
        justify-content: center;
    }
    
    .toolbar-right {
        order: 1;
        justify-content: center;
        margin-bottom: 8px;
    }
    
    .toolbar-group {
        margin: 2px;
        flex-wrap: wrap;
    }
    
    .visual-editor,
    .code-editor {
        padding: 16px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .editor-dialog {
        margin: 20px;
        width: calc(100% - 40px);
        max-height: calc(100vh - 40px);
    }
    
    .editor-dialog-body {
        padding: 20px;
    }
    
    .editor-dialog-header,
    .editor-dialog-footer {
        padding: 16px 20px;
    }
}

@media (max-width: 480px) {
    .toolbar-btn {
        min-width: 32px;
        height: 32px;
        padding: 6px 8px;
        font-size: 13px;
    }
    
    .editor-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .visual-editor,
    .code-editor {
        padding: 12px;
    }
}
