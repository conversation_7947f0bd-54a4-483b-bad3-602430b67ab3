# Contact Form Email Setup Guide

## Issue Description
The contact form on contact-us.html shows "An error occurred while sending your message. Please try again later." when the submit button is clicked.

## Root Cause Analysis
The issue is likely due to one or more of the following:
1. Missing or incomplete email configuration in the database
2. PHPMailer library not properly configured
3. SMTP settings not configured (if using SMTP)
4. PHP mail() function not working on the hosting provider

## Solution Steps

### Step 1: Check Current Email Configuration
1. Navigate to: `admin/test-email.php`
2. This will show you the current email configuration status
3. Look for any missing or incorrect settings

### Step 2: Setup Basic Email Configuration
1. Navigate to: `admin/setup-email.php`
2. Click "Setup Email Configuration" to create default email settings
3. This will populate the system_settings table with basic email configuration

### Step 3: Configure Email Settings
1. Go to: `admin/settings.php?category=email`
2. Configure the following essential settings:
   - **From Email**: The email address that emails will be sent from (e.g., <EMAIL>)
   - **From Name**: The name that will appear as the sender (e.g., Manage Inc.)
   - **Reply To Email**: Where replies should be sent (e.g., <EMAIL>)

### Step 4: Choose Email Delivery Method

#### Option A: Use PHP mail() Function (Simpler)
1. Keep "Use SMTP" unchecked
2. This uses the server's built-in mail function
3. May not work on all hosting providers

#### Option B: Use SMTP (Recommended)
1. Check "Use SMTP"
2. Configure SMTP settings:
   - **SMTP Host**: Your email provider's SMTP server
   - **SMTP Port**: Usually 587 for TLS or 465 for SSL
   - **SMTP Username**: Your email account username
   - **SMTP Password**: Your email account password
   - **SMTP Security**: TLS (recommended) or SSL

### Step 5: Test Email Configuration
1. Go to: `admin/test-email.php`
2. Enter your email address in the test form
3. Click "Send Test Email"
4. Check if the email is received

## Common SMTP Providers

### Gmail
- SMTP Host: smtp.gmail.com
- SMTP Port: 587
- SMTP Security: TLS
- Username: <EMAIL>
- Password: App-specific password (not your regular password)

### Outlook/Hotmail
- SMTP Host: smtp-mail.outlook.com
- SMTP Port: 587
- SMTP Security: TLS
- Username: <EMAIL>
- Password: Your account password

### Custom Domain (cPanel/WHM)
- SMTP Host: mail.yourdomain.com
- SMTP Port: 587 or 465
- SMTP Security: TLS or SSL
- Username: <EMAIL>
- Password: Your email account password

## Files Modified/Created

### New Files:
- `admin/test-email.php` - Email configuration testing tool
- `admin/setup-email.php` - Email configuration setup script
- `EMAIL_SETUP_GUIDE.md` - This guide

### Modified Files:
- `admin/lib/Mailer.php` - Updated to use system_settings table and improved error handling
- `process-contact.php` - Added better error logging and validation
- `admin/settings.php` - Enhanced with SMTP visibility controls

## Troubleshooting

### If emails still don't send:
1. Check server error logs for detailed error messages
2. Verify SMTP credentials are correct
3. Check if your hosting provider blocks outgoing SMTP connections
4. Try using a different SMTP provider
5. Contact your hosting provider for mail server configuration

### If using shared hosting:
- Many shared hosting providers block external SMTP connections
- You may need to use the hosting provider's SMTP server
- Contact your hosting provider for their recommended email settings

### If using Gmail:
- Enable 2-factor authentication
- Generate an app-specific password
- Use the app-specific password instead of your regular password

## Security Notes
- Never store SMTP passwords in plain text in code
- Use environment variables or secure configuration files for sensitive data
- Regularly rotate SMTP passwords
- Use TLS encryption when possible

## Support
If you continue to experience issues:
1. Check the error logs in `admin/test-email.php`
2. Review server error logs
3. Contact your hosting provider for email configuration assistance
4. Consider using a transactional email service like SendGrid, Mailgun, or Amazon SES

## Quick Fix Checklist
- [ ] Run `admin/setup-email.php` to create basic configuration
- [ ] Configure From Email and From Name in settings
- [ ] Test email sending with `admin/test-email.php`
- [ ] If test fails, configure SMTP settings
- [ ] Test contact form on contact-us.html
- [ ] Check error logs for any remaining issues
