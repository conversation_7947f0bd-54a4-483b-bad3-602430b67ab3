<?php
/**
 * Minimal Contact Form Test
 * This script tests contact form processing with minimal dependencies
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors for AJAX
ini_set('log_errors', 1);

// Set up error handler for fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("FATAL ERROR in minimal-contact-test.php: " . print_r($error, true));
        
        // If this is an AJAX request, return JSON error
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if (!headers_sent()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Server error occurred. Please try again later.']);
            }
        }
    }
});

// Log the start
error_log("=== MINIMAL CONTACT TEST STARTED ===");

// Default response
$response = [
    'success' => false,
    'message' => 'An error occurred while processing your request.'
];

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST request received");
    
    // Get form data
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';
    $source = isset($_POST['source']) ? trim($_POST['source']) : 'Unknown';
    
    error_log("Form data - Name: $name, Email: $email, Source: $source");
    
    // Basic validation
    if (empty($name) || empty($email) || empty($message)) {
        $response['message'] = 'Please fill in all required fields.';
        error_log("Validation failed: missing required fields");
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        error_log("Validation failed: invalid email format");
    } else {
        error_log("Validation passed, attempting to send email");
        
        // Try to send email using PHP's built-in mail() function
        $to = '<EMAIL>'; // Fallback recipient
        $subject = "Contact Form Submission from " . $name;
        
        // Create email content
        $email_content = "New Contact Form Submission\n\n";
        $email_content .= "Name: " . $name . "\n";
        $email_content .= "Email: " . $email . "\n";
        if (!empty($phone)) {
            $email_content .= "Phone: " . $phone . "\n";
        }
        $email_content .= "Source: " . $source . "\n";
        $email_content .= "Date: " . date('Y-m-d H:i:s') . "\n\n";
        $email_content .= "Message:\n" . $message . "\n";
        
        // Set email headers
        $headers = "From: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
        $headers .= "Reply-To: " . $email . "\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        
        error_log("Attempting to send email to: $to");
        error_log("Email subject: $subject");
        
        // Send email
        if (mail($to, $subject, $email_content, $headers)) {
            $response = [
                'success' => true,
                'message' => 'Thank you for your message. We\'ll get back to you soon!'
            ];
            error_log("Email sent successfully");
        } else {
            $response['message'] = 'Failed to send email. Please try again later.';
            error_log("Email sending failed");
        }
    }
} else {
    error_log("Not a POST request: " . $_SERVER['REQUEST_METHOD']);
    $response['message'] = 'Invalid request method.';
}

// Return JSON response for AJAX requests
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }
    
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    error_log("Returning AJAX response: " . json_encode($response));
    echo json_encode($response);
    exit;
}

// For non-AJAX requests, show simple response
?>
<!DOCTYPE html>
<html>
<head>
    <title>Minimal Contact Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .message { padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        form { margin: 20px 0; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; }
        button { background: #f1ca2f; color: #333; padding: 10px 20px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Minimal Contact Form Test</h1>
    
    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="message <?php echo $response['success'] ? 'success' : 'error'; ?>">
            <?php echo htmlspecialchars($response['message']); ?>
        </div>
    <?php endif; ?>
    
    <form method="post">
        <p>
            <label>Name *</label>
            <input type="text" name="name" required>
        </p>
        <p>
            <label>Email *</label>
            <input type="email" name="email" required>
        </p>
        <p>
            <label>Phone</label>
            <input type="tel" name="phone">
        </p>
        <p>
            <label>Message *</label>
            <textarea name="message" required></textarea>
        </p>
        <input type="hidden" name="source" value="Minimal Test">
        <p>
            <button type="submit">Send Test Message</button>
        </p>
    </form>
    
    <h2>📋 Instructions</h2>
    <ol>
        <li>This form uses only PHP's built-in mail() function</li>
        <li>No external dependencies or includes</li>
        <li>If this works, the issue is in the includes or dependencies</li>
        <li>If this fails, the issue is with the server's mail configuration</li>
    </ol>
    
    <h2>🧪 AJAX Test</h2>
    <button onclick="testAjax()">Test AJAX Submission</button>
    <div id="ajax-result"></div>
    
    <script>
    function testAjax() {
        const formData = new FormData();
        formData.append('name', 'AJAX Test User');
        formData.append('email', '<EMAIL>');
        formData.append('phone', '************');
        formData.append('message', 'This is an AJAX test message');
        formData.append('source', 'AJAX Test');
        
        fetch('minimal-contact-test.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.text().then(text => {
                console.log('Raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error('Invalid JSON: ' + text);
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);
            document.getElementById('ajax-result').innerHTML = 
                '<div class="message ' + (data.success ? 'success' : 'error') + '">' +
                'AJAX Result: ' + data.message + '</div>';
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('ajax-result').innerHTML = 
                '<div class="message error">AJAX Error: ' + error.message + '</div>';
        });
    }
    </script>
</body>
</html>
