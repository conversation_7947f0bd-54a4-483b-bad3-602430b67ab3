/**
 * Admin Sidebar CSS
 *
 * This file contains styles for the admin sidebar navigation.
 * The sidebar uses the black and yellow brand colors.
 */

/* Sidebar Container */
.admin-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background-color: var(--secondary-color);
  color: var(--white);
  z-index: var(--z-index-fixed);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal) ease, transform var(--transition-normal) ease;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

/* Hide scrollbar but allow scrolling */
.admin-sidebar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

body.sidebar-collapsed .admin-sidebar {
  width: var(--sidebar-collapsed-width);
}

/* Collapsed sidebar styles */
.admin-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.admin-sidebar-header {
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: var(--topbar-height);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Align to the left */
  width: 100%;
  transition: all var(--transition-normal) ease;
  padding-left: var(--spacing-4); /* Add left padding */
}

.sidebar-logo img {
  max-height: 40px;
  max-width: 100%;
  transition: all var(--transition-normal) ease;
}

body.sidebar-collapsed .sidebar-logo img {
  max-width: 40px;
}

/* Sidebar Menu */
.admin-sidebar-menu {
  flex: 1;
  padding: var(--spacing-2) 0;
  overflow-y: auto;
}

.admin-sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-sidebar-menu li {
  margin: 0;
  padding: 0;
  position: relative;
}

.admin-sidebar-menu a {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast) ease;
  position: relative;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-sidebar-menu a:hover {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.05);
}

.admin-sidebar-menu a.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid var(--primary-color);
}

.admin-sidebar-menu a i {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
  margin-right: var(--spacing-3);
  color: rgba(255, 255, 255, 0.6);
  transition: all var(--transition-fast) ease;
}

.admin-sidebar-menu a:hover i,
.admin-sidebar-menu a.active i {
  color: var(--primary-color);
}

.admin-sidebar-menu a span {
  transition: opacity var(--transition-normal) ease;
}

body.sidebar-collapsed .admin-sidebar-menu a span {
  opacity: 0;
  visibility: hidden;
  width: 0;
}

/* Show submenu on hover in collapsed state */
body.sidebar-collapsed .admin-sidebar-menu .sidebar-item:hover .sidebar-submenu {
  display: block;
  position: absolute;
  left: var(--sidebar-collapsed-width);
  top: 0;
  width: 200px;
  background-color: var(--secondary-color);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  box-shadow: var(--shadow-lg);
  opacity: 1;
  visibility: visible;
  max-height: 500px;
  padding: var(--spacing-1) 0;
  z-index: 1000;
}

body.sidebar-collapsed .admin-sidebar-menu .sidebar-item:hover .sidebar-submenu a span {
  opacity: 1;
  visibility: visible;
  width: auto;
}

/* Submenu */
.admin-sidebar-menu .sidebar-submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease, padding var(--transition-normal) ease;
  background-color: rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  display: none;
}

.admin-sidebar-menu .has-submenu.open .sidebar-submenu {
  max-height: 500px;
  opacity: 1;
  visibility: visible;
  padding: var(--spacing-1) 0;
  display: block;
}

.admin-sidebar-menu .submenu a {
  padding-left: var(--spacing-8);
  font-size: var(--font-size-xs);
  min-height: 36px;
}

.admin-sidebar-menu .submenu-icon {
  margin-left: auto;
  font-size: var(--font-size-xs);
  transition: transform var(--transition-normal) ease;
}

.admin-sidebar-menu .has-submenu.open .submenu-icon {
  transform: rotate(180deg);
}

/* Make submenu icon visible in collapsed state */
body.sidebar-collapsed .admin-sidebar-menu .submenu-icon {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 1;
  visibility: visible;
  width: auto;
}

body.sidebar-collapsed .admin-sidebar-menu .has-submenu.open .submenu-icon {
  transform: translateY(-50%) rotate(180deg);
}

/* Menu Toggle */
.admin-sidebar-menu .menu-toggle {
  cursor: pointer;
}

/* Notification Badge */
.menu-notification {
  position: absolute;
  right: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 0 var(--spacing-1);
}

body.sidebar-collapsed .menu-notification {
  right: var(--spacing-1);
}

/* Sidebar Footer */
.admin-sidebar-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  position: sticky;
  bottom: 0;
  background-color: var(--secondary-color);
  z-index: 1;
}

.admin-sidebar-footer a {
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.admin-sidebar-footer a:hover {
  color: var(--white);
}

.admin-sidebar-footer a i {
  margin-right: var(--spacing-2);
  font-size: var(--font-size-base);
}

body.sidebar-collapsed .admin-sidebar-footer a:not(.sidebar-toggle):not(#sidebarToggleContainer) span {
  display: none;
}

/* Sidebar toggle container */
#sidebarToggleContainer {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* Hide sidebar toggle in mobile view */
@media (max-width: 768px) {
  #sidebarToggleContainer,
  .sidebar-toggle {
    display: none !important;
  }
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  background-color: var(--primary-color);
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: var(--spacing-1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast) ease;
  width: 32px;
  height: 32px;
  margin-left: 10px; /* Add space between logout button and toggle */
}

.sidebar-toggle:hover {
  color: var(--secondary-color);
  background-color: var(--primary-light);
  transform: scale(1.1);
}

.sidebar-toggle:active {
  transform: scale(0.95);
}

.sidebar-toggle i {
  font-size: var(--font-size-base);
  transition: transform var(--transition-normal) ease;
}

body.sidebar-collapsed .sidebar-toggle i {
  transform: rotate(180deg);
}

/* Desktop Sidebar Toggle Button (fixed position) */
.desktop-sidebar-toggle {
  position: fixed;
  left: calc(var(--sidebar-width) - 16px);
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--primary-color);
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-normal) ease;
  opacity: 1; /* Ensure it's visible */
  visibility: visible; /* Ensure it's visible */
}

body.sidebar-collapsed .desktop-sidebar-toggle {
  left: calc(var(--sidebar-collapsed-width) - 16px);
}

.desktop-sidebar-toggle:hover {
  background-color: var(--primary-light);
  transform: translateY(-50%) scale(1.1);
}

.desktop-sidebar-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

.desktop-sidebar-toggle i {
  font-size: var(--font-size-sm);
  transition: transform var(--transition-normal) ease;
}

body.sidebar-collapsed .desktop-sidebar-toggle i {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .desktop-sidebar-toggle {
    display: none; /* Hide in mobile view */
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px !important;
    height: 100vh;
    box-shadow: none;
    z-index: 99999; /* Maximum z-index to ensure it's above everything */
    transition: left 0.3s ease;
    overflow-y: auto;
    transform: none !important;
    will-change: left; /* Optimize for animations */
  }

  .admin-sidebar.show {
    left: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }

  body.sidebar-collapsed .admin-sidebar {
    width: 280px !important;
    left: -280px;
  }

  body.sidebar-collapsed .admin-sidebar.show {
    left: 0;
  }

  /* Ensure menu items are visible */
  .admin-sidebar-menu a span,
  body.sidebar-collapsed .admin-sidebar-menu a span {
    opacity: 1;
    visibility: visible;
    width: auto;
  }

  .admin-sidebar-footer a span,
  body.sidebar-collapsed .admin-sidebar-footer a span {
    display: inline;
  }

  /* Hide user profile menu in mobile sidebar */
  .admin-sidebar .user-profile {
    display: none;
  }

  /* Sidebar Overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999; /* Just below sidebar but above other elements */
    display: none;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }

  .sidebar-overlay.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Prevent scrolling when sidebar is open */
  body.sidebar-open {
    overflow: hidden;
  }

  /* Adjust sidebar toggle in mobile view */
  .sidebar-toggle {
    width: 36px;
    height: 36px;
  }

  /* Fix submenu visibility in mobile view */
  .sidebar-item.has-submenu.open .sidebar-submenu {
    display: block !important;
    max-height: 500px !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: static; /* Ensure submenu is not positioned absolutely in mobile */
    width: 100%; /* Full width in mobile */
    box-shadow: none; /* No shadow in mobile */
    border-radius: 0; /* No border radius in mobile */
  }

  /* Fix submenu toggle icon in mobile view */
  .sidebar-item.has-submenu .menu-toggle .submenu-icon {
    display: inline-block !important;
    position: static; /* Reset position in mobile */
    transform: none; /* Reset transform in mobile */
    margin-left: auto; /* Align to right */
  }

  /* Ensure submenu items are properly styled in mobile */
  .sidebar-submenu a {
    padding-left: var(--spacing-8) !important; /* Consistent padding */
  }

  /* Ensure submenu items are visible */
  .sidebar-subitem a span {
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
  }

  /* Mobile menu toggle button */
  .topbar-mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #000; /* Black background */
    border: none;
    color: var(--primary-color); /* Yellow color */
    font-size: 20px;
    cursor: pointer;
    z-index: 1001;
    border-radius: 4px;
  }

  .topbar-mobile-toggle i {
    color: var(--primary-color); /* Ensure icon is yellow */
  }

  .topbar-mobile-toggle.active {
    color: var(--primary-color);
    background-color: #222; /* Slightly lighter black when active */
  }
}
