<?php
/**
 * Custom Enhanced Header Component for Frontend Editor
 *
 * This file provides a customized header for the frontend editor with the file selector
 * button positioned in the same row as the page title.
 */

// Ensure required variables are set
if (!isset($page_title)) {
    $page_title = basename($_SERVER['PHP_SELF'], '.php');
    $page_title = ucwords(str_replace('_', ' ', $page_title));
}

if (!isset($page_icon)) {
    $page_icon = 'fas fa-code';
}
?>

<div class="admin-content-header enhanced-header">
    <div class="header-main">
        <h2><i class="<?php echo $page_icon; ?>"></i> <?php echo $page_title; ?></h2>

        <!-- File selector button positioned in the header row -->
        <div class="file-selector-container">
            <a href="#" class="admin-btn file-selector-trigger" id="fileSelectorBtn">
                <i class="fas fa-folder-open"></i> <span>Select File</span>
            </a>
        </div>
    </div>

    <?php if (isset($stats_data) && !empty($stats_data)): ?>
    <div class="stats-container">
        <?php foreach ($stats_data as $stat): ?>
        <span class="stat-item <?php echo isset($stat['highlight']) && $stat['highlight'] ? 'highlight' : ''; ?>">
            <i class="<?php echo $stat['icon']; ?>"></i> <?php echo $stat['text']; ?>
        </span>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <?php if (isset($primary_action) && !empty($primary_action)): ?>
    <div class="admin-content-header-actions">
        <a href="<?php echo $primary_action['url']; ?>" class="admin-btn primary">
            <i class="<?php echo $primary_action['icon']; ?>"></i> <?php echo $primary_action['text']; ?>
        </a>
    </div>
    <?php endif; ?>

    <?php if (isset($secondary_actions) && !empty($secondary_actions)): ?>
    <div class="admin-content-header-actions secondary-actions">
        <?php foreach ($secondary_actions as $action): ?>
        <a href="<?php echo $action['url']; ?>" class="admin-btn <?php echo isset($action['class']) ? $action['class'] : 'outline'; ?>">
            <i class="<?php echo $action['icon']; ?>"></i> <?php echo $action['text']; ?>
        </a>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <?php if (isset($back_link) && !empty($back_link)): ?>
    <div class="admin-content-header-actions back-action">
        <a href="<?php echo $back_link['url']; ?>" class="admin-btn outline">
            <i class="fas fa-arrow-left"></i> <?php echo $back_link['text']; ?>
        </a>
    </div>
    <?php endif; ?>
</div>
