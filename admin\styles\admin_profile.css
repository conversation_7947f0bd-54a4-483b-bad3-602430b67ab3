/* Profile Page Styles */

.profile-container {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0;
}

/* Ensure admin content uses full width */
.admin-content {
    width: 100%;
    max-width: none;
    padding: 20px;
}

.admin-content-header {
    margin-bottom: 30px;
}

.admin-content-title {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.admin-content-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

/* Profile Overview Card */
.profile-overview-card {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    width: 100%;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 25px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, #f1ca2f, #e6b800);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(241, 202, 47, 0.3);
    position: relative;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    color: #fff;
    font-size: 48px;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.profile-email {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 15px 0;
}

.profile-meta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.profile-role,
.profile-joined {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    background: #f9fafb;
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
}

.profile-role i,
.profile-joined i {
    color: #f1ca2f;
}

/* Profile Tabs */
.profile-tabs {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    width: 100%;
}

.tab-nav {
    display: flex;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    overflow-x: auto;
}

.tab-btn {
    background: none;
    border: none;
    padding: 18px 24px;
    font-size: 15px;
    font-weight: 600;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
    min-width: 0;
    flex-shrink: 0;
}

.tab-btn:hover {
    color: #374151;
    background: #f3f4f6;
}

.tab-btn.active {
    color: #f1ca2f;
    background: #fff;
    border-bottom-color: #f1ca2f;
}

.tab-btn i {
    font-size: 16px;
}

/* Tab Content */
.tab-content {
    padding: 0;
}

.tab-pane {
    display: none;
    padding: 30px;
}

.tab-pane.active {
    display: block;
}

/* Form Styles */
.profile-form {
    max-width: 100%;
    width: 100%;
}

.form-section {
    margin-bottom: 40px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f3f4f6;
}

.section-title i {
    color: #f1ca2f;
    font-size: 22px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

/* Two column grid for larger sections */
.form-grid.two-column {
    grid-template-columns: 1fr 1fr;
}

/* Three column grid for smaller items */
.form-grid.three-column {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #f1ca2f;
    box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.1);
}

.form-group input[type="file"] {
    padding: 8px 12px;
    border: 2px dashed #d1d5db;
    background: #f9fafb;
}

.form-group input[type="file"]:hover {
    border-color: #f1ca2f;
    background: #fffbf0;
}

.form-help {
    font-size: 13px;
    color: #6b7280;
    margin-top: 6px;
}

/* Checkbox Styles */
.checkbox-group {
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    color: #374151;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    background: #f9fafb;
}

.checkbox-label:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
    accent-color: #f1ca2f;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #f1ca2f;
    border-color: #f1ca2f;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-weight: bold;
    font-size: 12px;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
}

.btn-primary {
    background: #f1ca2f;
    color: #1f2937;
}

.btn-primary:hover {
    background: #e6b800;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(241, 202, 47, 0.3);
}

.btn-secondary {
    background: #6b7280;
    color: #fff;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .form-grid.two-column {
        grid-template-columns: 1fr;
    }

    .form-grid.three-column {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .profile-container {
        padding: 0;
    }

    .profile-overview-card {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
    }

    .profile-name {
        font-size: 24px;
    }

    .profile-meta {
        justify-content: center;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        justify-content: center;
        padding: 15px 12px;
        font-size: 14px;
    }

    .tab-pane {
        padding: 20px;
    }

    .form-grid,
    .form-grid.two-column,
    .form-grid.three-column {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .section-title {
        font-size: 18px;
    }

    .profile-tabs {
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .profile-overview-card {
        padding: 15px;
    }

    .tab-btn {
        padding: 12px 8px;
        font-size: 13px;
        min-width: 100px;
    }

    .tab-pane {
        padding: 15px;
    }

    .profile-avatar {
        width: 70px;
        height: 70px;
    }

    .avatar-placeholder {
        font-size: 36px;
    }
}
